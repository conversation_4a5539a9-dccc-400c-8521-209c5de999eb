package config

import (
	"os"
	"strconv"
	"strings"
)

type config struct {
	Proxy struct {
		Key   string
		IpMax string
	}
	Mode                        string
	MaxConcurrence              int64
	DynamicScriptMaxConcurrence int64
	KDMaxConcurrence            int64
	TELDMaxConcurrence          int64
	EChargeMaxConcurrence       int64
	YunChargeMaxConcurrence     int64
	XiaoJuMaxConcurrence        int64
	NIOMaxConcurrence           int64
	StarChargeMaxConcurrence    int64
	HFChargeMaxConcurrence      int64
	WJCloudMaxConcurrence       int64
	DKCloudMaxConcurrence       int64
	SYChargeMaxConcurrence      int64
	CDWMaxConcurrence           int64
	ECZMaxConcurrence           int64
	GQEnergyMaxConcurrence      int64
	YunHuiMaxConcurrence        int64
	HuoLaLaMaxConcurrence       int64
	KafkaHosts                  []string
	FastDataHost                string
	StartTaskTopic              string
	CancelTaskTopic             string
	TaskRespTopic               string
	TestToken                   string
	KDAPiVersion                string
}

var Config config

func GetEnvWithDefault(key, value string) string {
	v, exist := os.LookupEnv(key)
	if !exist {
		return value
	}
	return v
}

func GetEnvWithDefaultInt64(key string, value int64) int64 {
	v, exist := os.LookupEnv(key)
	if !exist {
		return value
	}
	d, err := strconv.Atoi(v)
	if err != nil {
		return value
	}
	return int64(d)
}

func GetEnvWithDefaultAsArray(key, value, sep string) []string {
	v, exist := os.LookupEnv(key)
	if !exist {
		return strings.Split(value, sep)
	}
	return strings.Split(v, sep)
}

func init() {
	Config.Proxy.Key = GetEnvWithDefault("PROXY_KEY", "F60BB9EC")
	Config.Proxy.IpMax = GetEnvWithDefault("PROXY_IP_MAX", "15")

	// 启动爬虫任务：crawl-task-start取消爬虫任务：crawl-task-cancel接收爬虫任务：crawl-task-resp
	Config.KafkaHosts = GetEnvWithDefaultAsArray("KAFKA_HOSTS", "**************:9092,**************:9092,**************:9092", ",")
	// Config.FastDataHost = GetEnvWithDefault("FAST_DATA_HOST", "http://fast-data-admin.funeng-funeng:80")
	Config.FastDataHost = GetEnvWithDefault("FAST_DATA_HOST", "http://fast-data-admin-alitest.leo.bangdao-tech.com")

	Config.StartTaskTopic = GetEnvWithDefault("START_TASK_TOPIC", "crawl-task-start-local")
	Config.CancelTaskTopic = GetEnvWithDefault("CANCEL_TASK_TOPIC", "crawl-task-cancel-local")
	Config.TaskRespTopic = GetEnvWithDefault("TASK_RESP_TOPIC", "crawl-task-resp-local")

	Config.Mode = os.Getenv("MODE")
	Config.TestToken = os.Getenv("TEST_TOKEN")
	Config.KDAPiVersion = strings.ToUpper(GetEnvWithDefault("KD_API_VERSION", "V1"))

	Config.MaxConcurrence = min(max(GetEnvWithDefaultInt64("MAX_CONCURRENCE", 75), 1), 150)
	Config.DynamicScriptMaxConcurrence = min(max(GetEnvWithDefaultInt64("DYNAMIC_SCRIPT_MAX_CONCURRENCE", 1), 10), 150)
	Config.KDMaxConcurrence = min(max(GetEnvWithDefaultInt64("KD_MAX_CONCURRENCE", 75), 1), 150)
	Config.TELDMaxConcurrence = min(max(GetEnvWithDefaultInt64("TELD_MAX_CONCURRENCE", 10), 1), 150)
	Config.EChargeMaxConcurrence = min(max(GetEnvWithDefaultInt64("ECHARGE_MAX_CONCURRENCE", 1), 10), 150)
	Config.YunChargeMaxConcurrence = min(max(GetEnvWithDefaultInt64("YUNCHARGE_MAX_CONCURRENCE", 10), 1), 150)
	Config.XiaoJuMaxConcurrence = min(max(GetEnvWithDefaultInt64("XIAOJU_MAX_CONCURRENCE", 1), 1), 150)
	Config.NIOMaxConcurrence = min(max(GetEnvWithDefaultInt64("NIO_MAX_CONCURRENCE", 10), 1), 150)
	Config.StarChargeMaxConcurrence = min(max(GetEnvWithDefaultInt64("STARTCHARGE_MAX_CONCURRENCE", 10), 1), 150)
	Config.HFChargeMaxConcurrence = min(max(GetEnvWithDefaultInt64("HFCHARGE_MAX_CONCURRENCE", 10), 1), 150)
	Config.WJCloudMaxConcurrence = min(max(GetEnvWithDefaultInt64("WJCLOUD_MAX_CONCURRENCE", 10), 1), 150)
	Config.DKCloudMaxConcurrence = min(max(GetEnvWithDefaultInt64("DKCLOUD_MAX_CONCURRENCE", 10), 1), 150)
	Config.SYChargeMaxConcurrence = min(max(GetEnvWithDefaultInt64("SYCHARGE_MAX_CONCURRENCE", 10), 1), 150)
	Config.CDWMaxConcurrence = min(max(GetEnvWithDefaultInt64("CDW_MAX_CONCURRENCE", 10), 1), 150)
	Config.ECZMaxConcurrence = min(max(GetEnvWithDefaultInt64("ECZ_MAX_CONCURRENCE", 20), 1), 150)
	Config.GQEnergyMaxConcurrence = min(max(GetEnvWithDefaultInt64("GQENERGY_MAX_CONCURRENCE", 20), 1), 150)
	Config.YunHuiMaxConcurrence = min(max(GetEnvWithDefaultInt64("YUNHUI_MAX_CONCURRENCE", 1), 20), 150)
	Config.HuoLaLaMaxConcurrence = min(max(GetEnvWithDefaultInt64("HUOLALA_MAX_CONCURRENCE", 1), 20), 150)
}
