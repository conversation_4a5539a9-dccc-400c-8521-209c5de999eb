{"name": "miniprogram-sm-crypto", "version": "0.3.13", "description": "miniprogram js library", "main": "miniprogram_dist/index.js", "scripts": {"prepublish": "npm run build", "dev": "gulp dev --develop", "watch": "gulp watch --develop --watch", "build": "gulp", "dist": "npm run build", "clean-dev": "gulp clean --develop", "clean": "gulp clean", "test": "jest ./test/* --silent --bail", "coverage": "jest ./test/* --coverage --bail", "lint": "eslint \"src/**/*.js\" --fix", "lint-tools": "eslint \"tools/**/*.js\" --rule \"import/no-extraneous-dependencies: false\""}, "miniprogram": "miniprogram_dist", "repository": {"type": "git", "url": "https://github.com/wechat-miniprogram/sm-crypto.git"}, "author": "wechat-miniprogram", "license": "MIT", "devDependencies": {"babel-core": "^6.26.3", "babel-loader": "^7.1.5", "babel-plugin-module-resolver": "^3.1.1", "babel-preset-env": "^1.7.0", "colors": "^1.3.1", "eslint": "^5.3.0", "eslint-loader": "^2.1.0", "gulp": "^4.0.0", "gulp-clean": "^0.4.0", "gulp-if": "^2.0.2", "gulp-install": "^1.1.0", "gulp-less": "^3.5.0", "gulp-rename": "^1.4.0", "gulp-sourcemaps": "^2.6.4", "j-component": "git+https://github.com/JuneAndGreen/j-component.git", "jest": "^23.5.0", "through2": "^2.0.3", "webpack": "^4.16.5", "webpack-node-externals": "^1.7.2", "eslint-config-airbnb-base": "13.1.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0"}, "dependencies": {"jsbn": "^1.1.0"}, "jest": {"testEnvironment": "jsdom", "testURL": "https://jest.test", "collectCoverageFrom": ["src/**/*.js"], "moduleDirectories": ["node_modules", "src"]}}