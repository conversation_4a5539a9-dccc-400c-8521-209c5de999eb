## 0.3.13

* 支持根据私钥获取公钥

## 0.3.12

* 优化 sm3 运行性能

## 0.3.11

* sm2 支持压缩公钥

## 0.3.10

* 支持 sm3 hmac 模式

## 0.3.9

* 补充 sm4 解密时的 padding 判断

## 0.3.8

* sm2 解密时兼容密文可能是大写的情况

## 0.3.7

* 默认填充改为 pkcs#7，如传入 pkcs#5 也转到 pkcs#7 逻辑

## 0.3.6

* sm2 加解密支持二进制数据

## 0.3.5

* sm2.generateKeyPairHex 支持完整的 BigInteger 入参

## 0.3.4

* sm2 支持验证公钥接口
* sm2 生成密钥时支持自定义随机数

## 0.3.2

* 修复 sm2 在 userId 长度大于 31 时新旧版本验签不通过的问题

## 0.3.0

* sm2、sm3 重构
* sm4 支持 cbc 模式

## 0.2.7

* 优化 sm3 性能

## 0.2.5

* sm3 支持字节数组输入

## 0.2.4

* 修复 sm4 四字节字符输出编码

## 0.2.3

* sm3/sm4 支持输入四字节字符

## 0.2.2

* sm3 支持中文输入

## 0.2.0

* 修复 sm2 点 16 进制串可能不满 64 位的问题
* sm4 默认支持 pkcs#5 填充方式
* sm4 支持输入输出为字符串

## 0.1.3

* 支持用户传入 userId

## 0.1.0

* 修复 sm2 中 z 值数组可能包含 256 的问题。

## 0.0.6

* 修复加解密偶现失败的情况。

## 0.0.5

* 修复签名时如果使用 hash，会执行两次 utf8 转 hex 的问题。
* 修复 sm2/utils/js 中当传入 hex 为奇数位串会导致出错的问题。
