const s = require("miniprogram-sm-crypto");
const s4 = require('./s4');
const util = require('./util');
const base64Util = require('./base64');

function signature(body, headersStr) {
  const m = (function () {
    function E() {
      this.__strings__ = [];
    }

    E.prototype.append = function (str) {
      this.__strings__.push(str);
    };

    E.prototype.toString = function () {
      return this.__strings__.join("");
    };

    return E;
  })();

  function y(e) {
    return e + "evone";
  }

  function v(e) {
    var t = util.get2a(),
      n = s.sm2.doEncrypt(e, t);
    return "04".concat(n);
  }

  var a = new Date().getTime();
  var r = y(a);
  // ! 失败频率高是因为两种语言 json 序列化格式问题
  var i = JSON.stringify(JSON.parse(body));

  function h(e) {
    return s4.a4.ea_CBC(e);
  }

  const o = new m();

  o.append(r);
  o.append("\n");
  o.append("POST");
  o.append("\n");
  var c = base64Util.Base64.encode(i);
  p = s.sm3(c);
  o.append(p.toUpperCase());
  o.append("\n");
  o.append("application/json;charset=UTF-8");
  o.append("\n");
  o.append(a);
  o.append("\n");

  o.append(headersStr);
  var d = v(r);
  const l = h(o.toString());
  // x-evone-signature
  const signature = "EVOneUni:".concat(d, ":").concat(l);
  return JSON.stringify({
    signature,
    timestamp: a.toString(),
    // requestId
  })
}

console.log(signature(...process.argv.splice(2)))
