import sys
import ast
import json

# from importlib import import_module
# from internal import DataWrapper

# print(DataWrapper)
# dw = DataWrapper()
# print(dw.result('''[{"date": "2024-06-01 周六", "max": "27°", "min": "18°", "weather": "阴~多云", "cloud": "东北风1级", "sky": "36 优"}, {"date": "2024-06-02 周日", "max": "28°", "min": "18°", "weather": "多云~阴", "cloud": "东南风3级", "sky": "34 优"}, {"date": "2024-06-03 周一", "max": "31°", "min": "18°", "weather": "多云~阴", "cloud": "西南风3级", "sky": "55 良"}, {"date": "2024-06-04 周二", "max": "28°", "min": "21°", "weather": "多云~阴", "cloud": "东北风3级", "sky": "53 良"}, {"date": "2024-06-05 周三", "max": "23°", "min": "18°", "weather": "中雨~小雨", "cloud": "东南风3级", "sky": "46 优"}, {"date": "2024-06-06 周四", "max": "24°", "min": "17°", "weather": "阴", "cloud": "东南风3级", "sky": "37 优"}, {"date": "2024-06-07 周五", "max": "25°", "min": "19°", "weather": "阴~晴", "cloud": "东南风3级", "sky": "33 优"}, {"date": "2024-06-08 周六", "max": "30°", "min": "21°", "weather": "雾~阴", "cloud": "西南风1级", "sky": "48 优"}, {"date": "2024-06-09 周日", "max": "31°", "min": "22°", "weather": "阴", "cloud": "南风3级", "sky": "61 良"}, {"date": "2024-06-10 周一", "max": "28°", "min": "22°", "weather": "阴", "cloud": "东南风4级", "sky": "36 优"}, {"date": "2024-06-11 周二", "max": "25°", "min": "23°", "weather": "雾~阴", "cloud": "东南风3级", "sky": "30 优"}, {"date": "2024-06-12 周三", "max": "31°", "min": "22°", "weather": "雾~多云", "cloud": "南风1级", "sky": "39 优"}, {"date": "2024-06-13 周四", "max": "33°", "min": "23°", "weather": "雾~多云", "cloud": "南风2级", "sky": "73 良"}, {"date": "2024-06-14 周五", "max": "33°", "min": "23°", "weather": "雾~多云", "cloud": "东南风2级", "sky": "66 良"}, {"date": "2024-06-15 周六", "max": "34°", "min": "24°", "weather": "多云", "cloud": "西南风2级", "sky": "71 良"}, {"date": "2024-06-16 周日", "max": "34°", "min": "24°", "weather": "阴~多云", "cloud": "东风3级", "sky": "54 良"}, {"date": "2024-06-17 周一", "max": "31°", "min": "21°", "weather": "雾~晴", "cloud": "南风3级", "sky": "38 优"}, {"date": "2024-06-18 周二", "max": "31°", "min": "23°", "weather": "多云~中雨", "cloud": "东南风4级", "sky": "45 优"}, {"date": "2024-06-19 周三", "max": "25°", "min": "22°", "weather": "中雨~大雨", "cloud": "东南风3级", "sky": "21 优"}, {"date": "2024-06-20 周四", "max": "29°", "min": "25°", "weather": "小雨", "cloud": "西风3级", "sky": "39 优"}, {"date": "2024-06-21 周五", "max": "29°", "min": "24°", "weather": "中雨~小雨", "cloud": "南风3级", "sky": "32 优"}, {"date": "2024-06-22 周六", "max": "29°", "min": "24°", "weather": "大雨", "cloud": "西南风3级", "sky": "21 优"}, {"date": "2024-06-23 周日", "max": "27°", "min": "21°", "weather": "雾~阴", "cloud": "西南风3级", "sky": "28 优"}]'''))
# print(dw.result(123))

# fmt: off
forbiddenModules = ['sys', 'subprocess']
# fmt: on

# fd = open("./weather.py", "r")
# fileContent = fd.read()
fileContent = sys.argv[1]
root = ast.parse(fileContent)

pkgs = []
for node in ast.walk(root):
    if isinstance(node, ast.Import):
        for a in node.names:
            pkgs.append(a.name.split(".", maxsplit=1)[0])
    elif isinstance(node, ast.ImportFrom):
        if node.level == 0:
            pkgs.append(node.module.split(".", maxsplit=1)[0])

unique_arr = list(set(pkgs))
for p in unique_arr:
    if p in forbiddenModules:
        print(f"脚本引入禁用的模块：{p}")
        exit(1)

# allowed_builtins = {
#     "__builtins__": {
#         "__import__": __import__,
#         "__build_class__": __build_class__,
#         "__name__": __name__,
#         "print": print,
#         "int": int,
#         # modules
#         "datetime": import_module("datetime"),
#         "random": import_module("random"),
#         "time": import_module("time"),
#         "re": import_module("re"),
#         "requests": import_module("requests"),
#     }
# }

if len(sys.argv) < 4:
    print("动态脚本参数错误")
    exit(1)

gParams = json.loads(sys.argv[2])
params = json.loads(sys.argv[3])
exec(fileContent)
exit(0)
