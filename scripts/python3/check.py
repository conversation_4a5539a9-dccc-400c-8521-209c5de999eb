import sys
import ast
from importlib.metadata import distributions

installed_packages = [d.metadata["Name"] for d in distributions()]

# fmt: off
builtinModules = ['getopt', 'sched', 'getpass', 'secrets', 'gettext', 'select', 'glob', 'selectors', 'graphlib', 'setuptools', 'grp', 'shelve', 'gzip', 'shlex', 'hashlib', 'shutil', 'heapq', 'signal', 'hmac', 'site', 'html', 'sitecustomize', 'abc', 'http', 'smtpd', 'aifc', 'idlelib', 'smtplib', 'antigravity', 'idna', 'sndhdr', 'argparse', 'imaplib', 'socket', 'array', 'imghdr', 'socketserver', 'ast', 'imp', 'sqlite3', 'asynchat', 'importlib', 'sre_compile', 'asyncio', 'inspect', 'sre_constants', 'asyncore', 'sre_parse', 'atexit', 'ipaddress', 'ssl', 'audioop', 'itertools', 'stat', 'base64', 'json', 'statistics', 'bdb', 'keyword', 'string', 'binascii', 'lib2to3', 'stringprep', 'binhex', 'linecache', 'struct', 'bisect', 'locale', 'subprocess', 'builtins', 'logging', 'sunau', 'bz2', 'lzma', 'symtable', 'cProfile', 'mailbox', 'sys', 'calendar', 'mailcap', 'sysconfig', 'certifi', 'marshal', 'syslog', 'cgi', 'math', 'tabnanny', 'cgitb', 'mimetypes', 'tarfile', 'charset_normalizer', 'mmap', 'telnetlib', 'check', 'modulefinder', 'tempfile', 'chunk', 'multiprocessing', 'termios', 'cmath', 'netrc', 'test', 'cmd', 'nis', 'textwrap', 'code', 'nntplib', 'this', 'codecs', 'ntpath', 'threading', 'codeop', 'nturl2path', 'time', 'collections', 'numbers', 'timeit', 'colorsys', 'opcode', 'tkinter', 'compileall', 'operator', 'token', 'concurrent', 'optparse', 'tokenize', 'configparser', 'os', 'trace', 'contextlib', 'pathlib', 'traceback', 'contextvars', 'pdb', 'tracemalloc', 'copy', 'pickle', 'tty', 'copyreg', 'pickletools', 'turtle', 'crypt', 'pip', 'turtledemo', 'csv', 'pipes', 'types', 'ctypes', 'pkg_resources', 'typing', 'curses', 'pkgutil', 'unicodedata', 'dataclasses', 'platform', 'unittest', 'datetime', 'plistlib', 'urllib', 'dbm', 'poplib', 'urllib3', 'decimal', 'posix', 'uu', 'difflib', 'posixpath', 'uuid', 'dis', 'pprint', 'venv', 'distutils', 'profile', 'warnings', 'doctest', 'pstats', 'wave', 'email', 'pty', 'weakref', 'encodings', 'pwd', 'webbrowser', 'ensurepip', 'py_compile', 'wsgiref', 'enum', 'pyclbr', 'xdrlib', 'errno', 'pydoc', 'xml', 'exec', 'pydoc_data', 'xmlrpc', 'faulthandler', 'pyexpat', 'xxlimited', 'fcntl', 'queue', 'xxlimited_35', 'filecmp', 'quopri', 'xxsubtype', 'fileinput', 'random', 'zipapp', 'fnmatch', 're', 'zipfile', 'fractions', 'readline', 'zipimport', 'ftplib', 'reprlib', 'zlib', 'functools', 'zoneinfo', 'gc', 'resource', 'genericpath', 'rlcompleter', 'get_weather', 'runpy']
exceptModules = ['io', 'kafka', "PIL"]
# fmt: on

# fd = open("./get_weather.py", "r")
# fileContent = fd.read()
fileContent = sys.argv[1]
root = ast.parse(fileContent)

pkgs = []
for node in ast.walk(root):
    if isinstance(node, ast.Import):
        for a in node.names:
            pkgs.append(a.name.split(".", maxsplit=1)[0])
    elif isinstance(node, ast.ImportFrom):
        if node.level == 0:
            pkgs.append(node.module.split(".", maxsplit=1)[0])

unique_arr = list(set(pkgs))
third_party_pkgs = []
for p in unique_arr:
    if p not in builtinModules and p not in exceptModules:
        third_party_pkgs.append(p)
    #     print(f"第三方模块：{p}")
    # else:
    #     print(f"内置模块：{p}")

for p in third_party_pkgs:
    if p not in installed_packages:
        print(f"未安装的第三方模块：{p}", file=sys.stderr)
        exit(1)

print(f"全部第三方模块已安装")
exit(0)
