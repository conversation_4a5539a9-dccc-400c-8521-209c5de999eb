import ast
import astor

class PrintRemover(ast.NodeTransformer):
    def visit_Expr(self, node):
        # Check if the expression is a call to print
        if isinstance(node.value, ast.Call) and isinstance(node.value.func, ast.Name) and node.value.func.id == 'print':
            return None  # Remove this node
        return node

def remove_print_statements(source_code):
    # Parse the source code into an AST
    tree = ast.parse(source_code)
    # Transform the AST to remove print statements
    transformer = PrintRemover()
    transformed_tree = transformer.visit(tree)
    # Fix missing locations in the transformed tree
    ast.fix_missing_locations(transformed_tree)
    # Convert the transformed AST back to source code
    return astor.to_source(transformed_tree)

# Example usage
# source_code = """
# def example():
#     print("This is a print statement")
#     x = 42
#     print("Another print statement")
#     return x
# """

# new_code = remove_print_statements(source_code)
# print(new_code)
