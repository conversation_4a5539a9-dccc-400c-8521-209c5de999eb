"use strict";
var e = require("./master/@babel/runtime/helpers/typeof");
!(function (r) {
  "object" === ("undefined" == typeof exports ? "undefined" : e(exports)) &&
  "undefined" != typeof module
    ? (module.exports = r())
    : "function" == typeof define && define.amd
    ? define([], r)
    : (("undefined" != typeof window
        ? window
        : "undefined" != typeof global
        ? global
        : "undefined" != typeof self
        ? self
        : this
      ).base64js = r());
})(function () {
  return (function e(r, n, t) {
    function o(u, i) {
      if (!n[u]) {
        if (!r[u]) {
          var a = "function" == typeof require && require;
          if (!i && a) return a(u, !0);
          if (f) return f(u, !0);
          var d = new Error("Cannot find module '" + u + "'");
          throw ((d.code = "MODULE_NOT_FOUND"), d);
        }
        var c = (n[u] = { exports: {} });
        r[u][0].call(
          c.exports,
          function (e) {
            var n = r[u][1][e];
            return o(n || e);
          },
          c,
          c.exports,
          e,
          r,
          n,
          t
        );
      }
      return n[u].exports;
    }
    for (
      var f = "function" == typeof require && require, u = 0;
      u < t.length;
      u++
    )
      o(t[u]);
    return o;
  })(
    {
      "/": [
        function (e, r, n) {
          (n.byteLength = function (e) {
            return (3 * e.length) / 4 - d(e);
          }),
            (n.toByteArray = function (e) {
              var r,
                n,
                t,
                u,
                i,
                a = e.length;
              (u = d(e)), (i = new f((3 * a) / 4 - u)), (n = u > 0 ? a - 4 : a);
              var c = 0;
              for (r = 0; r < n; r += 4)
                (t =
                  (o[e.charCodeAt(r)] << 18) |
                  (o[e.charCodeAt(r + 1)] << 12) |
                  (o[e.charCodeAt(r + 2)] << 6) |
                  o[e.charCodeAt(r + 3)]),
                  (i[c++] = (t >> 16) & 255),
                  (i[c++] = (t >> 8) & 255),
                  (i[c++] = 255 & t);
              2 === u
                ? ((t =
                    (o[e.charCodeAt(r)] << 2) | (o[e.charCodeAt(r + 1)] >> 4)),
                  (i[c++] = 255 & t))
                : 1 === u &&
                  ((t =
                    (o[e.charCodeAt(r)] << 10) |
                    (o[e.charCodeAt(r + 1)] << 4) |
                    (o[e.charCodeAt(r + 2)] >> 2)),
                  (i[c++] = (t >> 8) & 255),
                  (i[c++] = 255 & t));
              return i;
            }),
            (n.fromByteArray = function (e) {
              for (
                var r,
                  n = e.length,
                  o = n % 3,
                  f = "",
                  u = [],
                  i = 16383,
                  a = 0,
                  d = n - o;
                a < d;
                a += i
              )
                u.push(h(e, a, a + i > d ? d : a + i));
              1 === o
                ? ((r = e[n - 1]),
                  (f += t[r >> 2]),
                  (f += t[(r << 4) & 63]),
                  (f += "=="))
                : 2 === o &&
                  ((r = (e[n - 2] << 8) + e[n - 1]),
                  (f += t[r >> 10]),
                  (f += t[(r >> 4) & 63]),
                  (f += t[(r << 2) & 63]),
                  (f += "="));
              return u.push(f), u.join("");
            });
          for (
            var t = [],
              o = [],
              f = "undefined" != typeof Uint8Array ? Uint8Array : Array,
              u =
                "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
              i = 0,
              a = u.length;
            i < a;
            ++i
          )
            (t[i] = u[i]), (o[u.charCodeAt(i)] = i);
          function d(e) {
            var r = e.length;
            if (r % 4 > 0)
              throw new Error("Invalid string. Length must be a multiple of 4");
            return "=" === e[r - 2] ? 2 : "=" === e[r - 1] ? 1 : 0;
          }
          function c(e) {
            return (
              t[(e >> 18) & 63] +
              t[(e >> 12) & 63] +
              t[(e >> 6) & 63] +
              t[63 & e]
            );
          }
          function h(e, r, n) {
            for (var t, o = [], f = r; f < n; f += 3)
              (t = (e[f] << 16) + (e[f + 1] << 8) + e[f + 2]), o.push(c(t));
            return o.join("");
          }
          (o["-".charCodeAt(0)] = 62), (o["_".charCodeAt(0)] = 63);
        },
        {},
      ],
    },
    {},
    []
  )("/");
});
