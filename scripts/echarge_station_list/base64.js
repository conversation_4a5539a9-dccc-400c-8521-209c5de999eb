"use strict";
var e = require("./master/@babel/runtime/helpers/typeof");
!(function (t, r) {
  "object" === ("undefined" == typeof exports ? "undefined" : e(exports)) &&
  "undefined" != typeof module
    ? (module.exports = r(t))
    : "function" == typeof define && define.amd
    ? define(r)
    : r(t);
})(
  "undefined" != typeof self
    ? self
    : "undefined" != typeof window
    ? window
    : "undefined" != typeof global
    ? global
    : void 0,
  function (e) {
    var t,
      r = (e = e || {}).Base64,
      n = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
      o = (function (e) {
        for (var t = {}, r = 0, n = e.length; r < n; r++) t[e.charAt(r)] = r;
        return t;
      })(n),
      a = String.fromCharCode,
      u = function (e) {
        if (e.length < 2)
          return (t = e.charCodeAt(0)) < 128
            ? e
            : t < 2048
            ? a(192 | (t >>> 6)) + a(128 | (63 & t))
            : a(224 | ((t >>> 12) & 15)) +
              a(128 | ((t >>> 6) & 63)) +
              a(128 | (63 & t));
        var t =
          65536 + 1024 * (e.charCodeAt(0) - 55296) + (e.charCodeAt(1) - 56320);
        return (
          a(240 | ((t >>> 18) & 7)) +
          a(128 | ((t >>> 12) & 63)) +
          a(128 | ((t >>> 6) & 63)) +
          a(128 | (63 & t))
        );
      },
      c = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,
      i = function (e) {
        return e.replace(c, u);
      },
      f = function (e) {
        var t = [0, 2, 1][e.length % 3],
          r =
            (e.charCodeAt(0) << 16) |
            ((e.length > 1 ? e.charCodeAt(1) : 0) << 8) |
            (e.length > 2 ? e.charCodeAt(2) : 0);
        return [
          n.charAt(r >>> 18),
          n.charAt((r >>> 12) & 63),
          t >= 2 ? "=" : n.charAt((r >>> 6) & 63),
          t >= 1 ? "=" : n.charAt(63 & r),
        ].join("");
      },
      d =
        e.btoa && "function" == typeof e.btoa
          ? function (t) {
              return e.btoa(t);
            }
          : function (e) {
              if (e.match(/[^\x00-\xFF]/))
                throw new RangeError("The string contains invalid characters.");
              return e.replace(/[\s\S]{1,3}/g, f);
            },
      h = function (e) {
        return d(i(String(e)));
      },
      l = function (e) {
        return e
          .replace(/[+\/]/g, function (e) {
            return "+" == e ? "-" : "_";
          })
          .replace(/=/g, "");
      },
      p = function (e, t) {
        return t ? l(h(e)) : h(e);
      };
    e.Uint8Array &&
      (t = function (e, t) {
        for (var r = "", o = 0, a = e.length; o < a; o += 3) {
          var u = e[o],
            c = e[o + 1],
            i = e[o + 2],
            f = (u << 16) | (c << 8) | i;
          r +=
            n.charAt(f >>> 18) +
            n.charAt((f >>> 12) & 63) +
            (void 0 !== c ? n.charAt((f >>> 6) & 63) : "=") +
            (void 0 !== i ? n.charAt(63 & f) : "=");
        }
        return t ? l(r) : r;
      });
    var s,
      A =
        /[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,
      g = function (e) {
        switch (e.length) {
          case 4:
            var t =
              (((7 & e.charCodeAt(0)) << 18) |
                ((63 & e.charCodeAt(1)) << 12) |
                ((63 & e.charCodeAt(2)) << 6) |
                (63 & e.charCodeAt(3))) -
              65536;
            return a(55296 + (t >>> 10)) + a(56320 + (1023 & t));
          case 3:
            return a(
              ((15 & e.charCodeAt(0)) << 12) |
                ((63 & e.charCodeAt(1)) << 6) |
                (63 & e.charCodeAt(2))
            );
          default:
            return a(((31 & e.charCodeAt(0)) << 6) | (63 & e.charCodeAt(1)));
        }
      },
      y = function (e) {
        return e.replace(A, g);
      },
      b = function (e) {
        var t = e.length,
          r = t % 4,
          n =
            (t > 0 ? o[e.charAt(0)] << 18 : 0) |
            (t > 1 ? o[e.charAt(1)] << 12 : 0) |
            (t > 2 ? o[e.charAt(2)] << 6 : 0) |
            (t > 3 ? o[e.charAt(3)] : 0),
          u = [a(n >>> 16), a((n >>> 8) & 255), a(255 & n)];
        return (u.length -= [0, 0, 2, 1][r]), u.join("");
      },
      x =
        e.atob && "function" == typeof e.atob
          ? function (t) {
              return e.atob(t);
            }
          : function (e) {
              return e.replace(/\S{1,4}/g, b);
            },
      B = function (e) {
        return x(String(e).replace(/[^A-Za-z0-9\+\/]/g, ""));
      },
      C = function (e) {
        return (function (e) {
          return y(x(e));
        })(
          String(e)
            .replace(/[-_]/g, function (e) {
              return "-" == e ? "+" : "/";
            })
            .replace(/[^A-Za-z0-9\+\/]/g, "")
        );
      };
    e.Uint8Array &&
      (s = function (e) {
        return Uint8Array.from(B(e), function (e) {
          return e.charCodeAt(0);
        });
      });
    if (
      ((e.Base64 = {
        VERSION: "2.6.2",
        atob: B,
        btoa: d,
        fromBase64: C,
        toBase64: p,
        utob: i,
        encode: p,
        encodeURI: function (e) {
          return p(e, !0);
        },
        btou: y,
        decode: C,
        noConflict: function () {
          var t = e.Base64;
          return (e.Base64 = r), t;
        },
        fromUint8Array: t,
        toUint8Array: s,
      }),
      "function" == typeof Object.defineProperty)
    ) {
      var v = function (e) {
        return { value: e, enumerable: !1, writable: !0, configurable: !0 };
      };
      e.Base64.extendString = function () {
        Object.defineProperty(
          String.prototype,
          "fromBase64",
          v(function () {
            return C(this);
          })
        ),
          Object.defineProperty(
            String.prototype,
            "toBase64",
            v(function (e) {
              return p(this, e);
            })
          ),
          Object.defineProperty(
            String.prototype,
            "toBase64URI",
            v(function () {
              return p(this, !0);
            })
          );
      };
    }
    return (
      e.Meteor && (Base64 = e.Base64),
      "undefined" != typeof module && module.exports
        ? (module.exports.Base64 = e.Base64)
        : "function" == typeof define &&
          define.amd &&
          define([], function () {
            return e.Base64;
          }),
      { Base64: e.Base64 }
    );
  }
);
