"use strict";
var e = require("./master/@babel/runtime/helpers/regeneratorRuntime"),
  t = require("./master/@babel/runtime/helpers/objectSpread2"),
  n = require("./master/@babel/runtime/helpers/asyncToGenerator"),
  // r = require("./master/@babel/runtime/helpers/objectWithoutProperties"),
  a = require("./master/@babel/runtime/helpers/typeof"),
  o = ["position_name", "page_name"],
  // s = require("./log"),
  i = require("./allconst"),
  c = require("./node_modules/miniprogram-sm-crypto/miniprogram_dist/index"),
  u = {
    TYPE_ZERO: 0,
    TYPE_ONE: 1,
    TYPE_TWO: 2,
    TYPE_THREE: 3,
    TYPE_FOUR: 4,
    TYPE_FIVE: 5,
    TYPE_SIX: 6,
    TYPE_SEVEN: 7,
    TYPE_EIGHT: 8,
    TYPE_THOUSAND28: 1028,
    TYPE_THOUSAND30: 1030,
    TYPE_1016: 1016,
    TYPE_1017: 1017,
    TYPE_10034: 10034,
    TYPE_10: 10,
    TYPE_12: 12,
    TYPE_20: 20,
    TYPE_1002: 1002,
    TYPE_1400001: 1400001,
  },
  f = {
    TYPE_10: 10,
    TYPE_22: 22,
    TYPE_42: 42,
    TYPE_20: 20,
    TYPE_40: 40,
    TYPE_41: 41,
    TYPE_70: 70,
    TYPE_80: 80,
    TYPE_81: 81,
    TYPE_1006: 1006,
  },
  E = function (e) {
    return e.toString();
  },
  T = function (e) {
    return (
      void 0 === e ||
      null == e ||
      "" == e ||
      "undefined" == e ||
      "null" == e
    );
  },
  p = 0;
function l(e, t) {
  return T(e) ? "0.0000" : (e = Number.parseFloat(e)).toFixed(t);
}
function g() {
  return i.lin1;
}
function _() {
  return (_ = n(
    e().mark(function t() {
      return e().wrap(function (e) {
        for (;;)
          switch ((e.prev = e.next)) {
            case 0:
              return e.abrupt(
                "return",
                new Promise(function (e, t) {
                  wx.getNetworkType({
                    success: function (n) {
                      var r = n.networkType;
                      "none" === r
                        ? t(!1)
                        : "wifi" === r ||
                            "3g" === r ||
                            "4g" === r ||
                            "5g" === r
                          ? e(!0)
                          : t(!1);
                    },
                    fail: function () {
                      t(!1);
                    },
                  });
                }),
              );
            case 1:
            case "end":
              return e.stop();
          }
      }, t);
    }),
  )).apply(this, arguments);
}
module.exports = t(
  t(
    {
      formatTime: function (e) {
        var t = e.getFullYear(),
          n = e.getMonth() + 1,
          r = e.getDate(),
          a = e.getHours(),
          o = e.getMinutes(),
          s = e.getSeconds();
        return (
          n < 10 && (n = "0" + n),
          r < 10 && (r = "0" + r),
          a < 10 && (a = "0" + a),
          o < 10 && (o = "0" + o),
          s < 10 && (s = "0" + s),
          [t, n, r].map(E).join("/") + " " + [a, o, s].map(E).join(":")
        );
      },
      isEmpty: T,
      showTipNoCancel: function (e, t, n) {
        wx.showModal({
          title: "提示",
          content: e,
          confirmText: t,
          showCancel: !1,
          confirmColor: "#0E9A6C",
          complete: function (e) {
            n();
          },
        });
      },
      isQuickClick: function () {
        var e = new Date().getTime(),
          t = e - p < 1e3;
        return (p = e), t;
      },
      indexFrom: {
        ECHARGEPROTOCOL: 1,
        USERPROTOCOL: 2,
        PRIVACYPROTOCOL: 3,
        ACTIVITYURL: 10,
      },
      sm2Enc: function (e) {
        var t = i.s2b1 + i.s2b2 + i.s2b3 + i.s2b4,
          n = c.sm2.doEncrypt(e, t);
        return "04".concat(n);
      },
      sm2Dec: function (e, t) {
        return (
          e.startsWith("04") && (e = e.substring(2)), c.sm2.doDecrypt(e, t)
        );
      },
      showTip: function (e, t, n) {
        wx.showModal({
          title: "提示",
          content: e,
          confirmText: t,
          cancelText: "取消",
          confirmColor: "#0E9A6C",
          cancelColor: "#999999",
          success: function (e) {
            e.confirm && n();
          },
        });
      },
      codeType: { RESETCODE: 6 },
      page: { PAGESIZE: 1 },
      isLogin: function () {
        var e = wx.getStorageSync("sessionId");
        return !T(e);
      },
      formatTimeSecond: function (e) {
        var t = e.getFullYear(),
          n = e.getMonth() + 1,
          r = e.getDate(),
          a = e.getHours(),
          o = e.getMinutes(),
          s = e.getSeconds();
        return (
          n < 10 && (n = "0" + n),
          r < 10 && (r = "0" + r),
          a < 10 && (a = "0" + a),
          o < 10 && (o = "0" + o),
          s < 10 && (s = "0" + s),
          [t, n, r].map(E).join("-") + " " + [a, o].map(E).join(":")
        );
      },
      toFix: function (e) {
        return T(e) ? "0.00" : (e = Number.parseFloat(e)).toFixed(2);
      },
      toFixMax: l,
      echargerType: {
        ACCOUNTONLINE: 160,
        SACNECHARGE: 161,
        CARDOFFLINE: 162,
        PLUGCHARGE: 163,
      },
      get2a: function () {
        return i.s2a1 + i.s2a2 + i.s2a3 + i.s2a4;
      },
      get41: function () {
        return i.s41 + i.s42 + i.s43 + i.s44;
      },
      get4i: function () {
        return i.s4i1 + i.s4i2 + i.s4i3;
      },
      getenk: function () {
        return i.enk1 + i.enk2 + i.enk3 + i.enk4;
      },
      sm2Enclogin: function (e) {
        var t = g(),
          n = c.sm2.doEncrypt(e, t);
        return "04".concat(n);
      },
      getlogin: g,
      sm2DecbyKey: function (e, t) {
        var n = "";
        return (
          "string" == typeof e &&
            e.startsWith("04") &&
            ((e = e.substring(2)), (n = c.sm2.doDecrypt(e, t))),
          n
        );
      },
      loginStatus: { LOGIN_EFFICACY: 101 },
    },
    {},
  ),
  {},
  {
    ToastType: {
      SUCCESS: "success",
      FAIL: "fail",
      NONE: "none",
      EXCEPTION: "expection",
    },
    checkID: function (e) {
      return !!(
        (function (e) {
          var t = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2],
            n = e.substring(17);
          if (
            /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
              e,
            )
          ) {
            for (var r = 0, a = 0; a < 17; a++) r += e[a] * t[a];
            if (
              [1, 0, "X", 9, 8, 7, 6, 5, 4, 3, 2][r % 11] == n.toUpperCase()
            )
              return !0;
          }
          return !1;
        })(e) &&
        (function (e) {
          if (
            /^(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)$/.test(
              e,
            )
          ) {
            var t = e.substring(0, 4),
              n = e.substring(4, 6),
              r = e.substring(6, 8),
              a = new Date(t + "-" + n + "-" + r);
            if (a && a.getMonth() == parseInt(n) - 1) return !0;
          }
          return !1;
        })(e.substring(6, 14)) &&
        (function (e) {
          if (
            /^[1-9][0-9]/.test(e) &&
            {
              11: "北京",
              12: "天津",
              13: "河北",
              14: "山西",
              15: "内蒙古",
              21: "辽宁",
              22: "吉林",
              23: "黑龙江 ",
              31: "上海",
              32: "江苏",
              33: "浙江",
              34: "安徽",
              35: "福建",
              36: "江西",
              37: "山东",
              41: "河南",
              42: "湖北 ",
              43: "湖南",
              44: "广东",
              45: "广西",
              46: "海南",
              50: "重庆",
              51: "四川",
              52: "贵州",
              53: "云南",
              54: "西藏 ",
              61: "陕西",
              62: "甘肃",
              63: "青海",
              64: "宁夏",
              65: "新疆",
              71: "台湾",
              81: "香港",
              82: "澳门",
            }[e]
          )
            return !0;
          return !1;
        })(e.substring(0, 2))
      );
    },
    image_upload_url:
      "https://alipaylite-test.ourval.com/echargeapi/external/uploadImage",
    showToast: function (e, t) {
      wx.showToast({ title: t, icon: e, duration: 2e3 });
    },
    showToastNone: function (e) {
      wx.showToast({
        type: "none",
        content: e,
        duration: 2e3,
        success: function () {},
      });
    },
    paserStationTypsStr: function (e) {
      var t = "--";
      switch (parseInt(e)) {
        case 0:
          t = "鲁能社会桩";
          break;
        case 1:
          t = "国网桩";
          break;
        case 7:
          t = "个人桩";
          break;
        case 8:
          t = "电插座";
          break;
        case 9:
          t = "互联互通";
          break;
        case 11:
          t = "即插即充";
          break;
        case 12:
          t = "苏创桩交流桩";
          break;
        case 13:
          t = "联行社会桩";
          break;
        case 14:
          t = "联行个人桩";
          break;
        case 15:
          t = "苏创交流桩即插即充";
          break;
        case 16:
          t = "苏创直流桩";
          break;
        case 17:
          t = "苏创直流";
          break;
        case 18:
          t = "专用站点";
          break;
        case 19:
          t = "联行社会桩";
          break;
        case 20:
          t = "互联互通-小菊";
          break;
        case 21:
          t = "物联自建";
          break;
        case 22:
          t = "物联社会桩(有序)";
          break;
        case 23:
          t = "物联联行桩";
          break;
        case 24:
          t = "个人桩";
          break;
        case 26:
          t = "国网后付费";
          break;
        default:
          t = "--";
      }
      return t;
    },
    requestSubscribeMessage: function (e, t, n, r) {
      wx.requestSubscribeMessage({
        tmplIds: [e],
        success: function (n) {
          "accept" == n[e]
            ? t()
            : (function (e, t) {
                wx.showModal({
                  title: "温馨提示",
                  content: t,
                  confirmText: "我知道了",
                  showCancel: !1,
                  confirmColor: "#0E9A6C",
                  complete: function () {
                    e();
                  },
                });
              })(t, r);
        },
        fail: function (e) {
          !(function (e, t) {
            wx.showModal({
              title: "温馨提示",
              content: t,
              confirmText: "去设置",
              cancelText: "取消",
              success: function (t) {
                t.confirm
                  ? wx.openSetting({ complete: function (e) {} })
                  : t.cancel && e();
              },
            });
          })(t, n);
        },
      });
    },
    getScreenWidth: function () {
      return getApp().systemInfo.screenWidth;
    },
    getUserInfo: function () {
      return JSON.parse(wx.getStorageSync("userInfo"));
    },
    evs_pageview: function (e) {
      var t = JSON.stringify(e),
        n = e.page_name;
      getApp().sensors.track("$pageview", {
        evs_page_info: t,
        evs_page_name: n,
      });
    },
    evs_click: function (e) {
      var t = e.position_name,
        n = (e.page_name, r(e, o)),
        a = JSON.stringify(n);
      getApp().sensors.track("$WebClick", {
        evs_position_info: a,
        evs_position_name: t,
      });
    },
    orderStatus: { UNPAY_ORDER: 1, FINISH_ORDER: 2 },
    orderType: {
      ORDER_TYPE_TIMEOUT: 1,
      ORDER_TYPE_ECHARGE: 2,
      ORDER_TYPE_ALL: 3,
    },
    getFormattedDate: function (e) {
      var t,
        n = new Date(e);
      return (
        (t = n ? new Date(n) : new Date()).setMinutes(
          t.getMinutes() - t.getTimezoneOffset(),
        ),
        t.toISOString().replace("T", " ").replace("Z", "").substring(0, 19)
      );
    },
    payChannel: {
      PAY_BY_WATTLE: "7",
      PAY_BY_WECHAT: "2",
      PAY_BY_WATTLE_wxMini: "wxMini",
      PAY_BY_WATTLE_evonepay: "evonepay",
    },
    businesstype: { CHARGE: 0, ORDER: 1, DYNASTPARKING: 2 },
    payStatus: { PAYSUCCESS: 1, PAYFALI: 2, PAYCANCLE: 3 },
    getTime: function (e) {
      var t = parseInt((e / 60 / 60) % 24);
      t = t < 10 ? "0" + t : t;
      var n = parseInt(e / 60 / 60);
      n = n < 10 ? "0" + n : n;
      var r = parseInt(e / 60);
      return [t, n, (r = r < 10 ? "0" + r : r)];
    },
    ENUME: u,
    ORDER_PAY_STATUS: f,
    getOrderStatus: function (e) {
      switch (e) {
        case f.TYPE_41:
        case f.TYPE_70:
          return u.TYPE_ZERO;
        case f.TYPE_20:
        case f.TYPE_40:
          return u.TYPE_ONE;
        case f.TYPE_80:
        case f.TYPE_81:
          return u.TYPE_TWO;
        default:
          return -1;
      }
    },
    toFixMax: l,
    deepCopy: function e(t) {
      if ("object" !== a(t)) return t;
      if (null !== t) {
        var n;
        for (var r in ((n = t instanceof Array ? [] : {}), t))
          t.hasOwnProperty(r) && (n[r] = e(t[r]));
        return n;
      }
    },
    getLastTime: function (e, t) {
      if (null == t) return "--";
      var n = new Date().getTime() - t,
        r = parseInt(n / 1e3 / 60),
        a = parseInt(n / 60 / 60 / 1e3),
        o = parseInt(n / 24 / 60 / 60 / 1e3);
      return o > 30
        ? "--"
        : o >= 1
          ? o + "天"
          : a >= 1
            ? a + "小时"
            : r >= 1
              ? r + "分钟"
              : "--";
    },
    goStationDetail: function (e) {
      wx.navigateTo({ url: "/pages/pile_details/index?stationId=" + e });
    },
    UserType: { USERSIMPLE: 1, ENTERPRISE: 2 },
    uuid: function (e) {
      for (var t = [], n = 0; n < 36; n++)
        t[n] = "0123456789abcdef".substr(Math.floor(16 * Math.random()), 1);
      (t[14] = "4"),
        (t[19] = "0123456789abcdef".substr((3 & t[19]) | 8, 1)),
        (t[8] = t[13] = t[18] = t[23] = "-");
      var r = t.join("");
      return (r = r.split("-").join("")), r;
    },
    getTimecharging: function (e) {
      var t = Math.floor(e / 1e3),
        n = Math.floor(t / 3600),
        r = Math.floor((t - 3600 * n) / 60);
      return [n, r, t - 3600 * n - 60 * r];
    },
    getNetworkStatus: function () {
      return _.apply(this, arguments);
    },
  },
);