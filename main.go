package main

import (
	"fmt"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"runtime/debug"
	"strings"
	"syscall"

	"tianyan-crawler/internal/app/apiservice"
	"tianyan-crawler/internal/app/receiver"
	"tianyan-crawler/internal/app/scheduler"
	"tianyan-crawler/internal/app/sender"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"

	_ "go.uber.org/automaxprocs"
)

func main() {
	// pprof
	go func() {
		http.ListenAndServe(":8000", apiservice.NewHTTPService().Router)
	}()

	defer func() {
		if err := recover(); err != nil {
			// 获取完整的堆栈信息
			stack := string(debug.Stack())
			panicMsg := fmt.Sprintf("PANIC: %v\nStack Trace:\n%s", err, stack)

			// 记录到panic日志文件
			logger.PanicLogger.Error(panicMsg)

			// 控制台输出（保留原有逻辑）
			log.Printf("出现异常: 全局异常捕获 %v\n", err)
			debug.PrintStack()
		}
	}()

	ch := make(chan *task.TaskResponseMessage)
	scheduler := scheduler.NewTaskScheduler(receiver.NewTaskReceiver(ch), sender.NewTaskRespSender(ch))
	scheduler.Start()

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
	s := <-c
	logger.Info(fmt.Sprintf("receive %s signal, do graceful shutdown", strings.ToUpper(s.String())))
	scheduler.Stop()
}
