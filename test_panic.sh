#!/bin/bash

# Panic测试脚本
# 用于测试main.go中的panic捕获功能

echo "=== tianyan-crawler Panic 测试工具 ==="
echo ""
echo "用法: $0 [panic类型]"
echo ""
echo "支持的panic类型:"
echo "  string     - 字符串panic"
echo "  slice      - 数组越界panic"
echo "  nil        - 空指针panic"  
echo "  goroutine  - goroutine中的panic"
echo "  nested     - 嵌套函数panic"
echo "  default    - 默认panic"
echo ""

PANIC_TYPE=${1:-string}

echo "准备测试panic类型: $PANIC_TYPE"
echo "程序将在启动后3秒触发panic..."
echo ""

# 设置环境变量并启动程序
export TEST_PANIC=$PANIC_TYPE
go run main.go

echo ""
echo "程序已退出，检查panic日志:"
echo "----------------------------------------"
if [ -f "logs/panic.log" ]; then
    tail -20 logs/panic.log
else
    echo "panic.log 文件未找到"
fi
