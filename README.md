# xdt-spider

# 参数配置
参数通过环境变量注入, 配置项如下:

* `MAX_CONCURRENCE`, 爬取站点和站点价格时的最大并发数, 默认值`75`, 取值min(150, `MAX_CONCURRENCE`)
* `KAFKA_HOSTS`, kafka集群地址, 多个host以`,`隔开, 默认值`"**************:9092,**************:9092,**************:9092"`
* `KD_API_VERSION`, 快电API版本, 默认值`V1`, 可选项`V1`,`V2`

# 架构图
![XdtSpider](docs/xdt-spider-arch.png)

# 运行逻辑
1. receiver监听kafka获取新任务
2. runner执行任务, 具体逻辑如下
   1. 获取代理ip, 逻辑如下
      * 是否已有代理ip
        * 有, 则检查是否过期(距离ip过期时间3秒钟内视为代理ip已过期), 若过期按无流程处理
        * 无, 通过代理api接口获取新ip, 并检查新ip可用性
   2. 根据`MAX_CONCURRENCE`并发爬取所有经纬度下所有站点
   3. 根据`MAX_CONCURRENCE`并发爬取所有去重后的站点价格, 每次请求完后将数据转交给sender
3. sender实时发送爬取数据到kafka
