{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "MainDebugger",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      // "args": ["-gcflags", "all=-N -l"],
      // "env": {
      //   "PKG_CONFIG_PATH": "/Library/Frameworks/Python.framework/Versions/3.7/lib/pkgconfig"
      // },
      "program": "${workspaceFolder}/main.go",
    }
  ]
}
