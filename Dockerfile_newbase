FROM registry.cn-hangzhou.aliyuncs.com/riet/xdt-spider:base
WORKDIR /

ARG PYTHON_VERSION=3.12.4

ENV WORK_PATH=/

COPY Python-$PYTHON_VERSION.tgz .
COPY openssl-OpenSSL_1_1_1d.tar .
# COPY scripts/python3/get_weather.py .

RUN  yum -y install zlib-devel bzip2-devel libffi-devel openssl-devel ncurses-devel sqlite-devel readline-devel tk-devel gdbm-devel db4-devel libpcap-devel xz-devel gcc make perl \
  && tar -xvf openssl-OpenSSL_1_1_1d.tar \
  && cd $WORK_PATH/openssl-OpenSSL_1_1_1d \
  && ./config --prefix=/usr/local/openssl no-zlib \
  && make \
  && make install \
  # && mv /usr/bin/openssl /usr/bin/openssl.old \
  # && mv /usr/lib64/openssl /usr/lib64/openssl.old \
  && mv /usr/lib64/libssl.so /usr/lib64/libssl.so.old \
  && ln -s /usr/local/openssl/bin/openssl /usr/bin/openssl \
  && ln -s /usr/local/openssl/include/openssl /usr/include/openssl \
  && ln -s /usr/local/openssl/lib/libssl.so /usr/lib64/libssl.so \
  && echo "/usr/local/openssl/lib" >> /etc/ld.so.conf \
  && ldconfig -v \
  # && openssl version \
  && cd $WORK_PATH \
  && set -ex \
  && yum clean all \
  && tar --extract -f $WORK_PATH/Python-$PYTHON_VERSION.tgz \
  && cd $WORK_PATH/Python-$PYTHON_VERSION/ \
  && mkdir /usr/local/python3 \
  && ./configure --prefix=/usr/local/python3 --with-openssl=/usr/local/openssl \
  && make clean \
  && make && make install \
  && ln -s /usr/local/python3/bin/python3 /usr/bin/python3 \
  && ln -s /usr/local/python3/bin/pip3 /usr/bin/pip3 \
  && cd $WORK_PATH \
  # 开始删除垃圾
  && rm -r $WORK_PATH/Python-$PYTHON_VERSION* \
  && rm -r openssl-OpenSSL_1_1_1d \
  && rm -f openssl-OpenSSL_1_1_1d.tar \
  && yum -y remove zlib-devel bzip2-devel libffi-devel openssl-devel ncurses-devel sqlite-devel readline-devel tk-devel gdbm-devel db4-devel libpcap-devel xz-devel gcc make perl \
  # 打印 python 版本 和 pip版本
  # && python3 --version \
  && echo "export PATH=/usr/local/python3/bin:$PATH" >> /etc/profile \
  # && pip3 -V \
  # && pip3 install requests \
  # && pip3 install numpy \
  # && pip3 install pandas
