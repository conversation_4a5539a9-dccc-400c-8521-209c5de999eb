FROM registry.cn-hangzhou.aliyuncs.com/riet/golang:1.21.1-centos as build
WORKDIR /go
COPY . .
RUN go env -w GOPROXY=https://goproxy.cn && \
    go build -ldflags="-s -w"
RUN upx tianyan-crawler

FROM registry.cn-hangzhou.aliyuncs.com/open_project/python:tianyan-crawler-playwright
WORKDIR /opt

COPY --from=build /go/tianyan-crawler tianyan-crawler
COPY --from=build /go/scripts/ scripts

ENV TZ=Asia/Shanghai

RUN ln -snf /usr/share/zoneinfo/${TZ} /etc/localtime &&\
    echo ${TZ} > /etc/timezone
RUN pip config --user set global.progress_bar off &&\
    pip3 install --default-timeout=100\
    requests\
    numpy\
    pandas\
    oss2\
    openpyxl\
    xlrd==2.0.1\
    loguru\
    kafka-python-ng\
    bs4\
    pdfplumber\
    pytesseract\
    ddddocr -i https://pypi.tuna.tsinghua.edu.cn/simple/

EXPOSE 8000
CMD ["./tianyan-crawler"]
