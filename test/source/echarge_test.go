package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/segmentio/kafka-go"
)

type EChargeParams struct {
	Positions []CityInfo `json:"positions"`
}

func TestEChargeSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}

	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target:           "echarge",
			Type:             "PRESET",
			NeedDynamicParam: false,
			NeedRetry:        true,
			AccountInfo: map[string]any{
				"token": "",
			},
			GlobalParam: map[string]any{
				"bizType":    "STATION",
				"city":       "无锡市",
				"cityCode":   "320200",
				"scriptUrl":  "",
				"channel":    "E充电",
				"templateId": "TPL66860885812701_18",
			},
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "E充电",
				},
				{
					NeedSplit:  "N",
					ParamCode:  "city",
					ParamValue: "大连市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_name": "无锡市充电站点1",
				"city":         "无锡市",
				"CITY_CODE":    "320200",
				"station_id":   "300003000100004550",
				"LGN":          "120.**************",
				"lon":          "120.**************",
				"LAT":          "31.***************",
				"lat":          "31.***************",
			},
			// {
			// 	"station_name": "无锡市充电站点2",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003103",
			// 	"LGN":          "120.39109721192663",
			// 	"lon":          "120.39109721192663",
			// 	"LAT":          "31.693398880150127",
			// 	"lat":          "31.693398880150127",
			// },
			// {
			// 	"station_name": "无锡市充电站点3",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003104",
			// 	"LGN":          "120.39121243707137",
			// 	"lon":          "120.39121243707137",
			// 	"LAT":          "31.828288800250206",
			// 	"lat":          "31.828288800250206",
			// },
			// {
			// 	"station_name": "无锡市充电站点4",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003105",
			// 	"LGN":          "120.39098275879739",
			// 	"lon":          "120.39098275879739",
			// 	"LAT":          "31.423619039949962",
			// 	"lat":          "31.423619039949962",
			// },
			// {
			// 	"station_name": "无锡市充电站点5",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003106",
			// 	"LGN":          "120.39086907307585",
			// 	"lon":          "120.39086907307585",
			// 	"LAT":          "31.288729119849876",
			// 	"lat":          "31.288729119849876",
			// },
			// {
			// 	"station_name": "无锡市充电站点6",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003107",
			// 	"LGN":          "120.2327952412026",
			// 	"lon":          "120.2327952412026",
			// 	"LAT":          "31.55850896005004",
			// 	"lat":          "31.55850896005004",
			// },
			// {
			// 	"station_name": "无锡市充电站点7",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003108",
			// 	"LGN":          "120.0746077236078",
			// 	"lon":          "120.0746077236078",
			// 	"LAT":          "31.55850896005004",
			// 	"lat":          "31.55850896005004",
			// },
			// {
			// 	"station_name": "无锡市充电站点8",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003109",
			// 	"LGN":          "119.916420206013",
			// 	"lon":          "119.916420206013",
			// 	"LAT":          "31.55850896005004",
			// 	"lat":          "31.55850896005004",
			// },
			// {
			// 	"station_name": "无锡市充电站点9",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003110",
			// 	"LGN":          "119.7582326884182",
			// 	"lon":          "119.7582326884182",
			// 	"LAT":          "31.55850896005004",
			// 	"lat":          "31.55850896005004",
			// },
			// {
			// 	"station_name": "无锡市充电站点10",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003111",
			// 	"LGN":          "119.6000451708234",
			// 	"lon":          "119.6000451708234",
			// 	"LAT":          "31.55850896005004",
			// 	"lat":          "31.55850896005004",
			// },
			// {
			// 	"station_name": "无锡市充电站点11",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003112",
			// 	"LGN":          "120.23268078807335",
			// 	"lon":          "120.23268078807335",
			// 	"LAT":          "31.693398880150127",
			// 	"lat":          "31.693398880150127",
			// },
			// {
			// 	"station_name": "无锡市充电站点12",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003113",
			// 	"LGN":          "120.07426436422007",
			// 	"lon":          "120.07426436422007",
			// 	"LAT":          "31.693398880150127",
			// 	"lat":          "31.693398880150127",
			// },
			// {
			// 	"station_name": "无锡市充电站点13",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003114",
			// 	"LGN":          "119.91584794036679",
			// 	"lon":          "119.91584794036679",
			// 	"LAT":          "31.693398880150127",
			// 	"lat":          "31.693398880150127",
			// },
			// {
			// 	"station_name": "无锡市充电站点14",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003115",
			// 	"LGN":          "119.7574315165135",
			// 	"lon":          "119.7574315165135",
			// 	"LAT":          "31.693398880150127",
			// 	"lat":          "31.693398880150127",
			// },
			// {
			// 	"station_name": "无锡市充电站点15",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003116",
			// 	"LGN":          "120.23256556292861",
			// 	"lon":          "120.23256556292861",
			// 	"LAT":          "31.828288800250206",
			// 	"lat":          "31.828288800250206",
			// },
			// {
			// 	"station_name": "无锡市充电站点16",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003117",
			// 	"LGN":          "120.07391868878588",
			// 	"lon":          "120.07391868878588",
			// 	"LAT":          "31.828288800250206",
			// 	"lat":          "31.828288800250206",
			// },
			// {
			// 	"station_name": "无锡市充电站点17",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003118",
			// 	"LGN":          "119.91527181464312",
			// 	"lon":          "119.91527181464312",
			// 	"LAT":          "31.828288800250206",
			// 	"lat":          "31.828288800250206",
			// },
			// {
			// 	"station_name": "无锡市充电站点18",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003119",
			// 	"LGN":          "119.75662494050039",
			// 	"lon":          "119.75662494050039",
			// 	"LAT":          "31.828288800250206",
			// 	"lat":          "31.828288800250206",
			// },
			// {
			// 	"station_name": "无锡市充电站点19",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003120",
			// 	"LGN":          "120.2327952412026",
			// 	"lon":          "120.2327952412026",
			// 	"LAT":          "31.423619039949962",
			// 	"lat":          "31.423619039949962",
			// },
			// {
			// 	"station_name": "无锡市充电站点20",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003121",
			// 	"LGN":          "120.0746077236078",
			// 	"lon":          "120.0746077236078",
			// 	"LAT":          "31.423619039949962",
			// 	"lat":          "31.423619039949962",
			// },
			// {
			// 	"station_name": "无锡市充电站点21",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003122",
			// 	"LGN":          "119.916420206013",
			// 	"lon":          "119.916420206013",
			// 	"LAT":          "31.423619039949962",
			// 	"lat":          "31.423619039949962",
			// },
			// {
			// 	"station_name": "无锡市充电站点22",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003123",
			// 	"LGN":          "119.7582326884182",
			// 	"lon":          "119.7582326884182",
			// 	"LAT":          "31.423619039949962",
			// 	"lat":          "31.423619039949962",
			// },
			// {
			// 	"station_name": "无锡市充电站点23",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003124",
			// 	"LGN":          "119.6000451708234",
			// 	"lon":          "119.6000451708234",
			// 	"LAT":          "31.423619039949962",
			// 	"lat":          "31.423619039949962",
			// },
			// {
			// 	"station_name": "无锡市充电站点24",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003125",
			// 	"LGN":          "120.23290892692414",
			// 	"lon":          "120.23290892692414",
			// 	"LAT":          "31.288729119849876",
			// 	"lat":          "31.288729119849876",
			// },
			// {
			// 	"station_name": "无锡市充电站点25",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003126",
			// 	"LGN":          "120.07494878077243",
			// 	"lon":          "120.07494878077243",
			// 	"LAT":          "31.288729119849876",
			// 	"lat":          "31.288729119849876",
			// },
			// {
			// 	"station_name": "无锡市充电站点26",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003127",
			// 	"LGN":          "119.91698863462072",
			// 	"lon":          "119.91698863462072",
			// 	"LAT":          "31.288729119849876",
			// 	"lat":          "31.288729119849876",
			// },
			// {
			// 	"station_name": "无锡市充电站点27",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003128",
			// 	"LGN":          "119.75902848846901",
			// 	"lon":          "119.75902848846901",
			// 	"LAT":          "31.288729119849876",
			// 	"lat":          "31.288729119849876",
			// },
			// {
			// 	"station_name": "无锡市充电站点28",
			// 	"city":         "无锡市",
			// 	"station_id":   "300003000100003129",
			// 	"LGN":          "119.6010683423173",
			// 	"lon":          "119.6010683423173",
			// 	"LAT":          "31.288729119849876",
			// 	"lat":          "31.288729119849876",
			// },
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestEChargeErrorTasks(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	// r := kafka.NewReader(kafka.ReaderConfig{
	// 	Brokers: config.Config.KafkaHosts,
	// 	Topic:   "spider-task-finished-bird",
	// 	GroupID: "spider-task-finished-bird",
	// })

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	// cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	// if err != nil {
	// 	fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
	// 	os.Exit(1)
	// }

	// defer cityNames.Close()

	// citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	cityNames := [][]string{
		{"37.057026", "114.388695"},
		{"30.382278", "120.19325"},
		{"32.87793", "119.847786"},
		{"22.703451", "113.77885"},
		{"29.807339", "121.4329"},
		{"36.302395", "119.99694"},
		{"39.932762", "116.077255"},
	}

	for _, r := range cityNames {
		// var r string
		// err := cityNames.Scan(&r)
		// if err != nil {
		// 	fmt.Println(err)
		// }
		// citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_supply where latitude = $1 and longitude = $2"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r[0], r[1])
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		// curCitys := []EChargeCityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			cityIdInfoMap[cityInfo.City] = append(cityIdInfoMap[cityInfo.City], cityInfo)
			// curCitys = append(curCitys, cityInfo)
		}

		// cityIdInfoMap[r] = curCitys
	}

	// fmt.Println(cityIdInfoMap)

	// ch := make(chan *task.TaskResp)
	// err = os.Mkdir("../data", os.ModePerm)
	// if err != nil {
	// 	fmt.Println(err)
	// }

	for city, cityInfos := range cityIdInfoMap {
		// fmt.Println(key, city)
		// 	if len(cityIdInfoMap[city]) == 0 {
		// 		fmt.Println(city)
		// 	}
		// f, err := os.Create("../data/" + city + ".txt")
		// if err != nil {
		// 	log.Fatal(err)
		// }
		// // remember to close the file
		// defer f.Close()

		// curPositions := cityIdInfoMap[city]
		// // fmt.Println(curPositions)
		task := StartTaskMessage[EChargeParams]{
			StartTaskMessage: task.StartTaskMessage{
				Target: "echarge",
				GlobalParam: map[string]any{
					"bizType":  "STATION",
					"province": "江苏省",
					"city":     city,
					"cityCode": "320200",
				},
			},
			Params: EChargeParams{
				Positions: cityInfos,
				// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	}
}

func TestEChargeTaskInQueue(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	// cityNames := [][]string{
	// 	{"32.038806", "118.641369"},
	// }

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_with_city_code where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	// for _, city := range citys {
	// 	curPositions := cityIdInfoMap[city]
	// 	// fmt.Println(curPositions)
	// 	msg := EChargeStartMessage{
	// 		TaskId: "1",
	// 		Target: "echarge",
	// 		OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": city},
	// 		Params: struct {
	// 			Positions []EChargeCityInfo "json:\"positions\""
	// 			SSDI      string            "json:\"ssdi\""
	// 			Token     string            "json:\"token\""
	// 		}{
	// 			Positions: curPositions,
	// 			SSDI:      "",
	// 			Token:     "",
	// 		},
	// 	}
	// 	r, _ := json.Marshal(msg)
	// 	_ = w.WriteMessages(context.Background(), kafka.Message{Value: r})
	// }

	cIdx := 0

	tk := StartTaskMessage[EChargeParams]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "echarge",
			GlobalParam: map[string]any{
				"bizType":  "STATION",
				"province": "江苏省",
				"city":     citys[cIdx],
				"cityCode": "320200",
			},
		},
		Params: EChargeParams{
			Positions: cityIdInfoMap[citys[cIdx]],
			// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
		},
	}
	tk.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: tk.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		task := StartTaskMessage[EChargeParams]{
			StartTaskMessage: task.StartTaskMessage{
				Target: "echarge",
				GlobalParam: map[string]any{
					"bizType":  "STATION",
					"province": "江苏省",
					"city":     citys[cIdx],
					"cityCode": "320200",
				},
			},
			Params: EChargeParams{
				Positions: cityIdInfoMap[citys[cIdx]],
				// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}

func TestEChargeTaskWithCityNames(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	// cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	// if err != nil {
	// 	fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
	// 	os.Exit(1)
	// }
	// defer cityNames.Close()

	// citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	cityNames := []string{
		"泰安市",
		"江门市",
		"廊坊市",
		"郑州市",
		"保定市",
		"丽水市",
		"苏州市",
		"镇江市",
		"玉溪市",
		"潍坊市",
		"徐州市",
		"绍兴市",
		"盐城市",
		"大连市",
		"济宁市",
		"广州市",
		"成都市",
		"金华市",
		"天津市",
		"嘉兴市",
		"长春市",
		"无锡市",
		"福州市",
		"东莞市",
		"孝感市",
		"重庆城区",
		"湘潭市",
		"中山市",
		"惠州市",
		"佛山市",
		"泰州市",
		"铜仁市",
		"西安市",
		"南通市",
		"岳阳市",
		"深圳市",
		"合肥市",
		"宁波市",
		"邢台市",
		"临沂市",
		"扬州市",
		"武汉市",
		"马鞍山市",
		"上海市",
		"烟台市",
		"唐山市",
		"青岛市",
		"北京市",
		"南宁市",
		"鄂州市",
		"南京市",
		"杭州市",
		"济南市",
		"常州市",
		"长沙市",
		"珠海市",
		"宿州市",
		"湖州市",
		"哈尔滨市",
		"温州市",
	}

	for _, r := range cityNames {
		// var r string
		// err := cityNames.Scan(&r)
		// if err != nil {
		// 	fmt.Println(err)
		// }
		// citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo CityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_supply where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	tk := StartTaskMessage[EChargeParams]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "echarge",
			GlobalParam: map[string]any{
				"bizType":  "STATION",
				"province": "江苏省",
				"city":     cityNames[cIdx],
				"cityCode": "320200",
			},
		},
		Params: EChargeParams{
			Positions: cityIdInfoMap[cityNames[cIdx]],
			// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
		},
	}
	tk.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: tk.ToByte()})
	fmt.Println("Start: ", cityNames[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", cityNames[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(cityNames) {
			continue
		}
		task := StartTaskMessage[EChargeParams]{
			StartTaskMessage: task.StartTaskMessage{
				Target: "echarge",
				GlobalParam: map[string]any{
					"bizType":  "STATION",
					"province": "江苏省",
					"city":     cityNames[cIdx],
					"cityCode": "320200",
				},
			},
			Params: EChargeParams{
				Positions: cityIdInfoMap[cityNames[cIdx]],
				// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", cityNames[cIdx])
	}
}

func TestEChargeSupplyPositions(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_supply group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	cityIdInfoMap := map[string][]CityInfo{}

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_supply where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	tk := StartTaskMessage[EChargeParams]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "echarge",
			GlobalParam: map[string]any{
				"bizType":  "STATION",
				"province": "江苏省",
				"city":     citys[cIdx],
				"cityCode": "320200",
			},
		},
		Params: EChargeParams{
			Positions: cityIdInfoMap[citys[cIdx]],
			// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
		},
	}
	tk.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: tk.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			break
		}
		task := StartTaskMessage[EChargeParams]{
			StartTaskMessage: task.StartTaskMessage{
				Target: "echarge",
				GlobalParam: map[string]any{
					"bizType":  "STATION",
					"province": "江苏省",
					"city":     citys[cIdx],
					"cityCode": "320200",
				},
			},
			Params: EChargeParams{
				Positions: cityIdInfoMap[citys[cIdx]],
				// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}
