package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/segmentio/kafka-go"
)

type DKCloudParams struct {
	Positions []CityInfo `json:"positions"`
	// Token     string     `json:"token"`
}

type Param struct {
	NeedSplit  string `json:"needSplit"`
	ParamCode  string `json:"paramCode"`
	ParamValue string `json:"paramValue"`
}

func TestDKCloudSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			TemplateId:       "TPL66840012316425_16",
			Target:           "dkcloud",
			Type:             "PRESET",
			NeedDynamicParam: false,
			NeedRetry:        true,
			AccountInfo:      map[string]any{"token": ""},
			GlobalParam: map[string]any{
				"bizType":    "STATION_HOUR",
				"province":   "江苏省",
				"channel":    "达克云",
				"taskId":     "TASK66850493515604",
				"templateId": "TPL66840012316428_22",
			},
			ParamConfigs: []task.ParamConfig{
				// {
				// 	NeedSplit:  "N",
				// 	ParamCode:  "channel",
				// 	ParamValue: "达克云",
				// },
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "苏州市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "998",
				"station_name": "武汉滨江万科里充电站",
				"city":         "上海市",
				"lat":          "31.********",
				"LAT":          "31.********",
				"lon":          "121.9472594",
				"LGN":          "121.9472594",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestDKCloudErrorTasks(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	// r := kafka.NewReader(kafka.ReaderConfig{
	// 	Brokers: config.Config.KafkaHosts,
	// 	Topic:   "spider-task-finished-bird",
	// 	GroupID: "spider-task-finished-bird",
	// })

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	// cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	// if err != nil {
	// 	fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
	// 	os.Exit(1)
	// }

	// defer cityNames.Close()

	// citys := []string{}
	// cityInfos := []DKCloudCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	cityNames := [][]string{
		{"37.057026", "114.388695"},
		{"30.382278", "120.19325"},
		{"32.87793", "119.847786"},
		{"22.703451", "113.77885"},
		{"29.807339", "121.4329"},
		{"36.302395", "119.99694"},
		{"39.932762", "116.077255"},
	}

	for _, r := range cityNames {
		// var r string
		// err := cityNames.Scan(&r)
		// if err != nil {
		// 	fmt.Println(err)
		// }
		// citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo DKCloudCityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_supply where latitude = $1 and longitude = $2"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r[0], r[1])
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		// curCitys := []DKCloudCityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			cityIdInfoMap[cityInfo.City] = append(cityIdInfoMap[cityInfo.City], cityInfo)
			// curCitys = append(curCitys, cityInfo)
		}

		// cityIdInfoMap[r] = curCitys
	}

	// fmt.Println(cityIdInfoMap)

	// ch := make(chan *task.TaskResp)
	// err = os.Mkdir("../data", os.ModePerm)
	// if err != nil {
	// 	fmt.Println(err)
	// }

	for city, cityInfos := range cityIdInfoMap {
		// fmt.Println(key, city)
		// 	if len(cityIdInfoMap[city]) == 0 {
		// 		fmt.Println(city)
		// 	}
		// f, err := os.Create("../data/" + city + ".txt")
		// if err != nil {
		// 	log.Fatal(err)
		// }
		// // remember to close the file
		// defer f.Close()

		// curPositions := cityIdInfoMap[city]
		// // fmt.Println(curPositions)
		task := StartTaskMessage[DKCloudParams]{
			StartTaskMessage: task.StartTaskMessage{
				Target: "DKCloud",
				GlobalParam: map[string]any{
					"bizType":  "STATION",
					"province": "江苏省",
					"city":     city,
					"cityCode": "320200",
				},
			},
			Params: DKCloudParams{
				Positions: cityInfos,
				// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	}
}

func TestDKCloudTaskInQueue(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	// cityInfos := []DKCloudCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	// cityNames := [][]string{
	// 	{"32.038806", "118.641369"},
	// }

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo DKCloudCityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_with_city_code where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	// for _, city := range citys {
	// 	curPositions := cityIdInfoMap[city]
	// 	// fmt.Println(curPositions)
	// 	msg := DKCloudStartMessage{
	// 		TaskId: "1",
	// 		Target: "DKCloud",
	// 		OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": city},
	// 		Params: struct {
	// 			Positions []DKCloudCityInfo "json:\"positions\""
	// 			SSDI      string            "json:\"ssdi\""
	// 			Token     string            "json:\"token\""
	// 		}{
	// 			Positions: curPositions,
	// 			SSDI:      "",
	// 			Token:     "",
	// 		},
	// 	}
	// 	r, _ := json.Marshal(msg)
	// 	_ = w.WriteMessages(context.Background(), kafka.Message{Value: r})
	// }

	cIdx := 0

	tk := StartTaskMessage[DKCloudParams]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "DKCloud",
			GlobalParam: map[string]any{
				"bizType":  "STATION",
				"province": "江苏省",
				"city":     "无锡市",
				"cityCode": "320200",
			},
		},
		Params: DKCloudParams{
			Positions: cityIdInfoMap[citys[cIdx]],
			// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
		},
	}
	tk.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: tk.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		task := StartTaskMessage[DKCloudParams]{
			StartTaskMessage: task.StartTaskMessage{
				Target: "DKCloud",
				GlobalParam: map[string]any{
					"bizType":  "STATION",
					"province": "江苏省",
					"city":     "无锡市",
					"cityCode": "320200",
				},
			},
			Params: DKCloudParams{
				Positions: cityIdInfoMap[citys[cIdx]],
				// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}

func TestDKCloudTaskWithCityNames(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	// cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	// if err != nil {
	// 	fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
	// 	os.Exit(1)
	// }
	// defer cityNames.Close()

	// citys := []string{}
	// cityInfos := []DKCloudCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	cityNames := []string{
		"泰安市",
		"江门市",
		"廊坊市",
		"郑州市",
		"保定市",
		"丽水市",
		"苏州市",
		"镇江市",
		"玉溪市",
		"潍坊市",
		"徐州市",
		"绍兴市",
		"盐城市",
		"大连市",
		"济宁市",
		"广州市",
		"成都市",
		"金华市",
		"天津市",
		"嘉兴市",
		"长春市",
		"无锡市",
		"福州市",
		"东莞市",
		"孝感市",
		"重庆城区",
		"湘潭市",
		"中山市",
		"惠州市",
		"佛山市",
		"泰州市",
		"铜仁市",
		"西安市",
		"南通市",
		"岳阳市",
		"深圳市",
		"合肥市",
		"宁波市",
		"邢台市",
		"临沂市",
		"扬州市",
		"武汉市",
		"马鞍山市",
		"上海市",
		"烟台市",
		"唐山市",
		"青岛市",
		"北京市",
		"南宁市",
		"鄂州市",
		"南京市",
		"杭州市",
		"济南市",
		"常州市",
		"长沙市",
		"珠海市",
		"宿州市",
		"湖州市",
		"哈尔滨市",
		"温州市",
	}

	for _, r := range cityNames {
		// var r string
		// err := cityNames.Scan(&r)
		// if err != nil {
		// 	fmt.Println(err)
		// }
		// citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo CityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_supply where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	tk := StartTaskMessage[DKCloudParams]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "DKCloud",
			GlobalParam: map[string]any{
				"bizType":  "STATION",
				"province": "江苏省",
				"city":     cityNames[cIdx],
				"cityCode": "320200",
			},
		},
		Params: DKCloudParams{
			Positions: cityIdInfoMap[cityNames[cIdx]],
			// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
		},
	}
	tk.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: tk.ToByte()})
	fmt.Println("Start: ", cityNames[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", cityNames[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(cityNames) {
			continue
		}
		task := StartTaskMessage[DKCloudParams]{
			StartTaskMessage: task.StartTaskMessage{
				Target: "DKCloud",
				GlobalParam: map[string]any{
					"bizType":  "STATION",
					"province": "江苏省",
					"city":     cityNames[cIdx],
					"cityCode": "320200",
				},
			},
			Params: DKCloudParams{
				Positions: cityIdInfoMap[cityNames[cIdx]],
				// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", cityNames[cIdx])
	}
}

func TestDKCloudSupplyPositions(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_supply group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	cityIdInfoMap := map[string][]CityInfo{}

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo DKCloudCityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_supply where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	tk := StartTaskMessage[DKCloudParams]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "DKCloud",
			GlobalParam: map[string]any{
				"bizType":  "STATION",
				"province": "江苏省",
				"city":     citys[cIdx],
				"cityCode": "320200",
			},
		},
		Params: DKCloudParams{
			Positions: cityIdInfoMap[citys[cIdx]],
			// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
		},
	}
	tk.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: tk.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			break
		}
		task := StartTaskMessage[DKCloudParams]{
			StartTaskMessage: task.StartTaskMessage{
				Target: "DKCloud",
				GlobalParam: map[string]any{
					"bizType":  "STATION",
					"province": "江苏省",
					"city":     citys[cIdx],
					"cityCode": "320200",
				},
			},
			Params: DKCloudParams{
				Positions: cityIdInfoMap[citys[cIdx]],
				// Token:     "c:wxsp:2D312592DF08422C9E2FB530D7D023BF",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}
