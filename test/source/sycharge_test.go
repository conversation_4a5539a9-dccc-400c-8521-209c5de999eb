package test

import (
	"context"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/segmentio/kafka-go"
)

type SYChargeParams struct {
	Positions []CityInfo `json:"positions"`
	// Token     string     `json:"token"`
}

func TestSYChargeSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			TemplateId:       "TPL66810568019253_2",
			Target:           "sycharge",
			Type:             "PRESET",
			NeedRetry:        true,
			NeedDynamicParam: false,
			AccountInfo:      map[string]any{"token": ""},
			GlobalParam: map[string]any{
				"bizType":    "CHARGING_GUN",
				"province":   "江苏省",
				"channel":    "顺易充",
				"taskId":     "TASK66850493515604",
				"templateId": "TPL66810568019253_2",
			},
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "新星市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "157833",
				"station_name": "山东省威海市乳山银滩5A级旅游度假区充电站",
				"city":         "深圳市",
				"CITY":         "深圳市",
				"CITY_CODE":    "440300",
				"LAT":          "31.***************",
				"lat":          "31.***************",
				"LGN":          "120.**************",
				"lon":          "120.**************",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}
