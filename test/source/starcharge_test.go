package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/segmentio/kafka-go"
)

type StarChargeParams struct {
	Positions []CityInfo `json:"positions"`
}

func TestStarChargeSendSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	// "globalParam":{"bizType":"STATION","templateId":"TPL66830059312647_4","taskId":"TASK67090455519586"},"needDynamicParam":true,"needRetry":false,"paramConfigs":[{"needSplit":"N","paramCode":"city","paramValue":"无锡市"}],"target":"starcharge","taskInstanceId":"20241012161150205001950031040962","type":"PRESET"}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target:           "starcharge",
			Type:             "PRESET",
			NeedDynamicParam: false,
			NeedRetry:        true,
			AccountInfo: map[string]any{
				"type":  "wx",
				"token": "ozGb50IMSZmJmdl0e6HcfOfDENCA",
			},
			GlobalParam: map[string]any{
				"bizType":    "DETAIL",
				"province":   "江苏省",
				"channel":    "星星充电",
				"taskId":     "TASK67090455519586",
				"templateId": "TPL66830059312647_5",
			},
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "星星充电",
				},
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "无锡市",
				},
			},
		},
		RetryParams: []map[string]any{
			// **********	星星60千瓦快充
			// ********	观墩花苑09高
			// ********	46栋丁单元门口
			// ********	茅建宏
			// ********	黎络盈
			// ********	丁家塘4号
			// ********	7KW星星充电
			// ********	珑樾花园7幢电梯口
			// ********	世茂九溪墅五期
			// ********	星星充电桩-张巷6队
			// ********	聚划算星星内卖电
			// ********	华山苑2-31车位充电桩
			// 10692720	紫檀园
			// 10771837	观湖四号楼二单元
			{
				"station_id":   "05dcca6d-1561-4322-830a-b4c9b15b2518",
				"station_name": "星星充电-新地假日广场新光大厦快充站",
				"city":         "上海市",
				"CITY_CODE":    "310100",
				"LGN":          "121.459057",
				"lon":          "121.459057",
				"LAT":          "31.229091",
				"lat":          "31.229091",
			},
			// {
			// 	"station_id":   "********",
			// 	"station_name": "观墩花苑09高",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
			// {
			// 	"station_id":   "21e5655f-00d5-487e-be28-2396f508d297",
			// 	"station_name": "仪征晟泰一品55栋3号桩",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
			// {
			// 	"station_id":   "0a592c30-fb84-4f72-bd99-c4e15dbb42f8",
			// 	"station_name": "东方名城充电站",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
			// {
			// 	"station_id":   "MA01H3BQ1.395815801_3210030101",
			// 	"station_name": "特来电扬州国泰大厦地下快充站",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestStarChargeCrawlingPriceTask(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	task := StartTaskMessage[StarChargeParams]{
		// Target: "starcharge",
		// OriReq: map[string]string{"crawlTarget": "STATION", "accountType": "普通用户", "city": "无锡市"},
		Params: StarChargeParams{
			Positions: []CityInfo{
				{
					StationID:   "b4c33539-0043-49a1-ab7f-973a1c66f4d2",
					StationName: "中石化-星星充电-新吴万达广场快充站",
					City:        "无锡市",
					CityCode:    "320200",
					Latitude:    "31.***************",
					Longitude:   "120.**************",
					Province:    "江苏省",
				},
				// {
				// 	StationID:   "9ce2b76e-f745-42ff-8939-eed2435dd511",
				// 	StationName: "无锡市气象局",
				// 	City:        "无锡市",
				// 	CityCode:    "320200",
				// 	Latitude:    "31.***************",
				// 	Longitude:   "120.**************",
				// 	Province:    "江苏省",
				// },
				// {
				// 	StationID:   "MA01H3BQ1.MA002TMQX_110115MA002TMQX0088",
				// 	StationName: "北京市亦庄开发区通泰国际公馆公共充电站",
				// 	City:        "北京市",
				// 	CityCode:    "320200",
				// 	Latitude:    "39.78159",
				// 	Longitude:   "116.558324",
				// 	Province:    "北京",
				// },
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestStarChargeTaskInQueue(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	// cityNames := [][]string{
	// 	{"32.038806", "118.641369"},
	// }

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, ykc_city_id, province, latitude, longitude from crawler_city_with_city_code where city = $1 and ykc_city_id != '' limit 1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	// for _, city := range citys {
	// 	curPositions := cityIdInfoMap[city]
	// 	// fmt.Println(curPositions)
	// 	msg := EChargeStartMessage{
	// 		TaskId: "1",
	// 		Target: "echarge",
	// 		OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": city},
	// 		Params: struct {
	// 			Positions []EChargeCityInfo "json:\"positions\""
	// 			SSDI      string            "json:\"ssdi\""
	// 			Token     string            "json:\"token\""
	// 		}{
	// 			Positions: curPositions,
	// 			SSDI:      "",
	// 			Token:     "",
	// 		},
	// 	}
	// 	r, _ := json.Marshal(msg)
	// 	_ = w.WriteMessages(context.Background(), kafka.Message{Value: r})
	// }

	cIdx := 0

	task := StartTaskMessage[StarChargeParams]{
		// TaskId: "1",
		// Target: "StarCharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: StarChargeParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[StarChargeParams]{
			// TaskId: "1",
			// Target: "StarCharge",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: StarChargeParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}

func TestStarChargeSupplyPositions(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_supply group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	cityIdInfoMap := map[string][]CityInfo{}

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, ykc_city_id, province, latitude, longitude from crawler_city_supply where city = $1 and ykc_city_id != ''"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	task := StartTaskMessage[StarChargeParams]{
		// Target: "StarCharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: StarChargeParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[StarChargeParams]{
			// Target: "StarCharge",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: StarChargeParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}
