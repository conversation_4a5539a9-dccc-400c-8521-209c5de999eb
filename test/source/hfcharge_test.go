package test

import (
	"context"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/segmentio/kafka-go"
)

type HFChargeParams struct {
	Token     string     `json:"token"`
	SSDI      string     `json:"ssdi"`
	Positions []CityInfo `json:"positions"`
}

func TestHFChargeSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}

	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target:           "hfcharge",
			Type:             "PRESET",
			NeedRetry:        true,
			NeedDynamicParam: false,
			AccountInfo: map[string]any{
				"token": "C01eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Z0tg0fdsWzz_BWSZXGDxv241gt5xTVPntFZUJCUeqfE",
				"ssdi":  "0356bcee-c5d2-58fb-2fad-70bc0f113891",
			},
			GlobalParam: map[string]any{
				"bizType":    "CHARGING_GUN",
				"province":   "安徽省",
				"city":       "锦州市",
				"cityCode":   "320200",
				"scriptUrl":  "",
				"channel":    "合肥充电",
				"templateId": "TPL67060454718631_1",
			},
			ParamConfigs: []task.ParamConfig{
				// {
				// 	NeedSplit:  "N",
				// 	ParamCode:  "channel",
				// 	ParamValue: "E充电",
				// },
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "合肥市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "3bf63c6a-aca3-4d0c-b75d-289187689a24",
				"station_name": "合肥玉龙路停车场充电站",
				"city":         "合肥市",
				"CITY":         "合肥市",
				"city_code":    "340100",
				"CITY_CODE":    "340100",
				"lat":          "31.853905",
				"lon":          "117.355996",
				"LAT":          "31.853905",
				"LGN":          "117.355996",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}
