package test

import (
	"encoding/json"
	"math/rand"
	"time"

	"tianyan-crawler/internal/app/task"
)

type Position struct {
	StationID   string `json:"stationId,omitempty"`
	StationName string `json:"stationName,omitempty"`
	// Lat         string `json:"LAT,omitempty"`
	// Lgn         string `json:"LGN,omitempty"`
	Latitude   string `json:"LAT"`
	Longitude  string `json:"LGN"`
	BusinessId string `json:"businessId"`
	Address    string `json:"address,omitempty"`
	City       string `json:"city,omitempty"`
	CityCode   string `json:"cityCode,omitempty"`
	Province   string `json:"province,omitempty"`
}

type CityInfo Position

type StartTaskMessage[T any] struct {
	task.StartTaskMessage
	Params      T `json:"params"`
	RetryParams T `json:"retryParams"`
}

func RandStr(length int) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano() + rand.Int63n(100)))
	str := "0123456789abcdefghijklmnopqrstuvwxyz"
	bytes := []byte(str)
	result := []byte{}
	for i := 0; i < length; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

func (s *StartTaskMessage[T]) Init() {
	if s.TaskInstanceId == "" {
		s.TaskInstanceId = RandStr(16)
	}
}

func (s StartTaskMessage[T]) ToByte() []byte {
	b, _ := json.Marshal(s)
	return b
}
