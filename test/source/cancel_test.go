package test

import (
	"testing"
)

func TestKDCancelTask(t *testing.T) {
	// topic := config.Config.CancelTaskTopic
	// w := &kafka.Writer{
	// 	Addr:                   kafka.TCP(config.Config.KafkaHosts...),
	// 	Topic:                  topic,
	// 	AllowAutoTopicCreation: true,
	// }
	// req := task.CancelTaskMessage{
	// 	TaskInstanceId: "20240718163500236235752031042256",
	// }
	// _ = w.WriteMessages(context.Background(), kafka.Message{Value: req.ToByte()})
}
