package test

import (
	"context"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/segmentio/kafka-go"
)

func TestHuoLaLaSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "huolala",
			Type:   "PRESET",
			AccountInfo: map[string]any{
				"token": "6fc1d0a59ab94b1aa85b3b7cfc2998b7",
			},
			GlobalParam: map[string]any{
				"bizType":    "DETAIL",
				"province":   "江苏省",
				"channel":    "货拉拉",
				"taskId":     "TASK66850493515604",
				"templateId": "TPL67050311513736_8",
			},
			NeedRetry:        true,
			NeedDynamicParam: false,
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "货拉拉",
				},
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "无锡市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "GZWX559-ZT60488",
				"station_name": "昭通龙山古镇充电站",
				"business_id":  "MA7HEPWG4",
				"city":         "昭通市",
				"CITY":         "昭通市",
				"CITY_CODE":    "411600",
				"LAT":          "27.281101",
				"lat":          "27.281101",
				"LGN":          "103.684353",
				"lon":          "103.684353",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}
