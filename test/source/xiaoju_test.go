package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/segmentio/kafka-go"
)

type XiaoJuParams struct {
	Token     string     `json:"token"`
	WSGSIG    string     `json:"wsgsig"`
	TTID      string     `json:"ttid"`
	Positions []CityInfo `json:"positions"`
}

func TestXiaoJuSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}

	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target:           "xj",
			Type:             "PRESET",
			NeedDynamicParam: true,
			NeedRetry:        false,
			// {"accountInfo":{"ssdi":"","openId":"","type":"alipay","userId":"","priKey":"","token":"-PFdj8FPRrTWw3Zis8aqvY96FC6eu9-IDlIOtzXlkg8szDnOwkAMgNG7fLUVecaz2bf5l7A0gwSiinJ3BKF63duYSmCLLoowE5GEmYmhqiZMI1Kv3t3c82jmwiyECrMSIPwc_BLZXXv21JrV0pvw_-lWYuNxe97_1u-7C6f3azW3UZoJZ4JkRXvy5qMgXI72Suj-CgAA__8="},"globalParam":{"bizType":"DETAIL","templateId":"TPL67020678916753_1","taskId":"TASK67020969611563"},"needDynamicParam":true,"needRetry":false,"paramConfigs":[{"needSplit":"N","paramCode":"city","paramValue":"吉安市"},{"needSplit":"N","paramCode":"channel","paramValue":"小桔充电"}],"target":"xj","taskInstanceId":"20241227110212987792384031161055","type":"PRESET"}
			AccountInfo: map[string]any{
				"type":  "wx",
				"token": "sqY4Be37FZKnhHTLUox6Jc-BS7QE3pzzoMRwSY4sIR4kzDkOwzAMRNG7_JowKFPUwttkcZZGARKkMnz3wHE10_y3MpTAJp0UYSQiCWMmXNX3a0Sq3udeelX3noWRCRWGEyCcjjkT3lsydy3WW55duO6xsBArn9f3fVmIrFVtE25_2JtnsybcCZIn7aU2qwXhcbhPQrdfAAAA__8=",
			},
			GlobalParam: map[string]any{
				"bizType":    "DETAIL",
				"province":   "江苏省",
				"channel":    "小桔充电",
				"taskId":     "TASK66850493515604",
				"templateId": "TPL67070464316402_2",
			},
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "小桔充电",
				},
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "天津市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "101437000_4822",
				"station_name": "小鹏超充 吉安星光Park站",
				"city":         "无锡市",
				"CITY":         "无锡市",
				"lon":          "114.1482995",
				"LGN":          "114.1482995",
				"lat":          "30.18868424",
				"LAT":          "30.18868424",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestXiaoJuTaskInQueue(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	// cityNames := [][]string{
	// 	{"32.038806", "118.641369"},
	// }

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_with_city_code where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	// for _, city := range citys {
	// 	curPositions := cityIdInfoMap[city]
	// 	// fmt.Println(curPositions)
	// 	msg := EChargeStartMessage{
	// 		TaskId: "1",
	// 		Target: "echarge",
	// 		OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": city},
	// 		Params: struct {
	// 			Positions []EChargeCityInfo "json:\"positions\""
	// 			SSDI      string            "json:\"ssdi\""
	// 			Token     string            "json:\"token\""
	// 		}{
	// 			Positions: curPositions,
	// 			SSDI:      "",
	// 			Token:     "",
	// 		},
	// 	}
	// 	r, _ := json.Marshal(msg)
	// 	_ = w.WriteMessages(context.Background(), kafka.Message{Value: r})
	// }

	cIdx := 0

	task := StartTaskMessage[XiaoJuParams]{
		// Target: task_target,
		// OriReq: map[string]string{
		// 	"crawlTarget": "TARGET_PRICE",
		// 	"accountType":  "普通用户",
		// 	"city":         citys[cIdx],
		// },
		Params: XiaoJuParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx >= len(citys) {
			break
		}
		task := StartTaskMessage[EChargeParams]{
			// Target: task_target,
			// OriReq: map[string]string{
			// 	"crawlTarget": "TARGET_SITE",
			// 	"accountType":  "普通用户",
			// 	"city":         citys[cIdx],
			// },
			Params: EChargeParams{
				Positions: cityIdInfoMap[citys[cIdx]],
				// Token:     "c:wxsp:541D46EE88B04B2199E2E03A199DE889",
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}

func TestXiaoJuWithCityNames(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	// cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_cn group by city")
	// if err != nil {
	// 	fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
	// 	os.Exit(1)
	// }

	// defer cityNames.Close()

	// citys := []string{}
	// cityInfos := []CityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	cityNames := []string{
		"苏州市",
		"潍坊市",
		"大连市",
		"绍兴市",
		"广州市",
		"西安市",
		"合肥市",
		"泉州市",
		"武汉市",
		"烟台市",
		"上海市",
		"厦门市",
		"沈阳市",
		"长沙市",
		"保定市",
		"徐州市",
		"成都市",
		"长春市",
		"佛山市",
		"惠州市",
		"中山市",
		"临沂市",
		"扬州市",
		"青岛市",
		"贵阳市",
		"珠海市",
		"嘉兴市",
		"福州市",
		"深圳市",
		"兰州市",
		"太原市",
		"北京市",
		"杭州市",
		"济南市",
		"温州市",
		"郑州市",
		"南昌市",
		"昆明市",
		"天津市",
		"金华市",
		"无锡市",
		"东莞市",
		"南通市",
		"宁波市",
		"南宁市",
		"石家庄市",
		"南京市",
		"常州市",
		"哈尔滨市",
	}

	for _, r := range cityNames {
		// var r string
		// err := cityNames.Scan(&r)
		// if err != nil {
		// 	fmt.Println(err)
		// }
		// citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo CityInfo
		cityQueryStr := "select city, province, latitude, longitude from crawler_city_less where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	// fmt.Println(cityIdInfoMap)

	// ch := make(chan *task.TaskResp)
	// err = os.Mkdir("../data", os.ModePerm)
	// if err != nil {
	// 	fmt.Println(err)
	// }

	for _, city := range cityNames {
		// f, err := os.Create("../data/" + city + ".txt")
		// if err != nil {
		// 	log.Fatal(err)
		// }
		// // remember to close the file
		// defer f.Close()

		curPositions := cityIdInfoMap[city]
		// fmt.Println(curPositions)
		task := StartTaskMessage[XiaoJuParams]{
			// TaskId: "1",
			// Target: "xj",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": city},
			Params: XiaoJuParams{
				Positions: curPositions,
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	}
}

func TestXiaoJuSupplyPositions(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_supply group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	cityIdInfoMap := map[string][]CityInfo{}

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, city_code, province, latitude, longitude from crawler_city_supply where city = $1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	task := StartTaskMessage[XiaoJuParams]{
		// Target: task_target,
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: XiaoJuParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[XiaoJuParams]{
			// Target: task_target,
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: XiaoJuParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}
