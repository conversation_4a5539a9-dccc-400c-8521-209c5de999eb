package test

import (
	"context"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/segmentio/kafka-go"
)

type DynamicParams struct {
	Positions []CityInfo `json:"positions"`
	// Token     string     `json:"token"`
}

func TestDynamicSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}

	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId: "20240718163500236235752031042256",
			Target:      "echarge",
			Type:        "DYNAMIC_SCRIPT",
			AccountInfo: map[string]any{
				// "sign": "3632f741c7bac4eccf9a52e667eb775a",
				// "key":  "c564e5a37692c72d79bc08c005a2668b",
			},
			NeedDynamicParam: false,
			NeedRetry:        false,
			GlobalParam: map[string]any{
				"bizType":    "BROWSER_PLUGIN",
				"city":       "锦州市",
				"cityCode":   "320200",
				"scriptUrl":  "https://test-adserving-oss.bangdao-tech.com/common/1/20240828141025932260172031050477$spider.py",
				"channel":    "E充电",
				"templateId": "TPL66820817210140_12",
			},
			ParamConfigs: []task.ParamConfig{
				// {
				// 	NeedSplit:  "N",
				// 	ParamCode:  "province",
				// 	ParamValue: "广东省",
				// },
				// {
				// 	NeedSplit:  "N",
				// 	ParamCode:  "need_get_day",
				// 	ParamValue: "1",
				// },
				{
					NeedSplit:  "N",
					ParamCode:  "start_day",
					ParamValue: "2024-08-15",
				},
				{
					NeedSplit:  "N",
					ParamCode:  "end_day",
					ParamValue: "2024-08-31",
				},
				// {
				// 	NeedSplit:  "N",
				// 	ParamCode:  "month",
				// 	ParamValue: "11",
				// },
			},
		},
		RetryParams: []map[string]any{},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestDynamicCancelTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.CancelTaskTopic,
		AllowAutoTopicCreation: true,
	}

	req := task.CancelTaskMessage{
		TaskInstanceId: "20240718163500236235752031042256",
	}
	_ = w.WriteMessages(context.Background(), kafka.Message{Value: req.ToByte()})
}
