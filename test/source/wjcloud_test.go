package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/segmentio/kafka-go"
)

type WJCloudParams struct {
	Positions []CityInfo `json:"positions"`
}

func TestWJCloudSendSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}

	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target:           "wjcloud",
			Type:             "PRESET",
			NeedDynamicParam: false,
			AccountInfo: map[string]any{
				"token": "123",
			},
			GlobalParam: map[string]any{
				"bizType":    "DETAIL",
				"province":   "江苏省",
				"channel":    "蔚景云",
				"taskId":     "TASK66850493515604",
				"templateId": "TPL67100247512181",
			},
			NeedRetry:    true,
			ParamConfigs: []task.ParamConfig{},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "62754",
				"business_id":  "505037",
				"station_name": "金川恒信高分子科技有限公司",
				"fast_gun_id":  "18450",
				"slow_gun_id":  "18450",
				"city":         "徐州市",
				"lat":          "38.514809",
				"lon":          "102.28949",
				"CITY":         "徐州市",
				"LAT":          "38.514809",
				"LGN":          "102.28949",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestWJCloudCrawlingPriceTask(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	task := StartTaskMessage[WJCloudParams]{
		// Target: "yuncharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": "滁州市"},
		Params: WJCloudParams{
			Positions: []CityInfo{
				{
					StationID:   "70580",
					StationName: "易充得大润发充电站",
					City:        "海口市",
					CityCode:    "2554",
					Latitude:    "20.017587",
					Longitude:   "110.374097",
					Province:    "海南省",
				},
				{
					StationID:   "117158",
					StationName: "吴江区沃尔玛超市南侧充电站",
					City:        "苏州市",
					CityCode:    "1041",
					Latitude:    "31.1673",
					Longitude:   "120.640037",
					Province:    "江苏省",
				},
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestWJCloudTaskInQueue(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	// cityNames := [][]string{
	// 	{"32.038806", "118.641369"},
	// }

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, ykc_city_id, province, latitude, longitude from crawler_city_with_city_code where city = $1 and ykc_city_id != '' limit 1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	// for _, city := range citys {
	// 	curPositions := cityIdInfoMap[city]
	// 	// fmt.Println(curPositions)
	// 	msg := EChargeStartMessage{
	// 		TaskId: "1",
	// 		Target: "echarge",
	// 		OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": city},
	// 		Params: struct {
	// 			Positions []EChargeCityInfo "json:\"positions\""
	// 			SSDI      string            "json:\"ssdi\""
	// 			Token     string            "json:\"token\""
	// 		}{
	// 			Positions: curPositions,
	// 			SSDI:      "",
	// 			Token:     "",
	// 		},
	// 	}
	// 	r, _ := json.Marshal(msg)
	// 	_ = w.WriteMessages(context.Background(), kafka.Message{Value: r})
	// }

	cIdx := 0

	task := StartTaskMessage[WJCloudParams]{
		// TaskId: "1",
		// Target: "yuncharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: WJCloudParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[WJCloudParams]{
			// TaskId: "1",
			// Target: "yuncharge",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: WJCloudParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}

func TestWJCloudSupplyPositions(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_supply group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	cityIdInfoMap := map[string][]CityInfo{}

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, ykc_city_id, province, latitude, longitude from crawler_city_supply where city = $1 and ykc_city_id != ''"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	task := StartTaskMessage[WJCloudParams]{
		// Target: "yuncharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: WJCloudParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[WJCloudParams]{
			// Target: "yuncharge",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: WJCloudParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}
