package test

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"testing"
	"time"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/segmentio/kafka-go"
)

type YunChargeParams struct {
	Positions []CityInfo `json:"positions"`
}

func TestYunChargeSendSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			Target:           "yuncharge",
			Type:             "PRESET",
			NeedDynamicParam: false,
			NeedRetry:        true,
			AccountInfo: map[string]any{
				"userId": "********",
				"priKey": "6jy4khkwz7w100qj",
				"token":  "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJqd3QiLCJpYXQiOjE3NTAyODU1ODQsInN1YiI6IntcImNvbmZpcm1GbGFnXCI6XCIxXCIsXCJpc0JpbmRpbmdDYXJcIjpcIjBcIixcImlzQ2FyTW9yZU51bVwiOlwiMFwiLFwibGFzdExvZ2luVGltZVwiOlwiMjAyNS0wNi0xOSAwNjoyNjoyNFwiLFwicHViS2V5XCI6XCJNSUdmTUEwR0NTcUdTSWIzRFFFQkFRVUFBNEdOQURDQmlRS0JnUUNoYmFIRWZOUldvdkJEWi8zMm9OdDFDVUpMZEM2QjB3TDN3d1Z0NU5TcktIYXh5U2FWRzFoQVk2eU8rU1V0eXV3bDRGMS91bW9rdWVFZVIxeGI5YlhXNFh2ZitOSGlEMFFPK1NZaXZuczBDZnFobGZxUzQ0NWJGUXQrUDNpc0N1cGQ1eTJpV3dHdDNQZ3BobVJTVllVWC9DZ1p5Uy9xY0s5UXhpQjNCNkZoS3dJREFRQUJcIixcInJlZ2lzdENpdHlcIjpcIuWNl-S6rOW4glwiLFwicmVnaXN0Q2l0eUlkXCI6XCI5OTdcIixcInNlY3JldEZyZWVQYXlGbGFnXCI6XCIxXCIsXCJ1c2VyQWNjb3VudFwiOlwiMTM0MDcxOTY5ODRcIixcInVzZXJJZFwiOjE3ODc0MTg1fSIsImV4cCI6MTc1MDg5MDM4NH0.rvXUcUG3ncOW3qAShMLfmcfe6jp4nPqab_RYOlyCkyA",
			},
			GlobalParam: map[string]any{
				"bizType":    "PRICE",
				"province":   "江苏省",
				"channel":    "云快充",
				"taskId":     "TASK67100491610142",
				"templateId": "TPL67100597614381_3",
			},
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "云快充",
				},
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "上海市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "192819",
				"station_name": "安徽省合肥市蜀山区南艳湖清潭路3号停车场交流充电站",
				"city":         "合肥市",
				"LGN":          "117.255066",
				"lon":          "117.255066",
				"LAT":          "31.768819",
				"lat":          "31.768819",
				"business_id":  "1237",
			},
			// {
			// 	"station_id":   "191390",
			// 	"station_name": "上海市闵行区名都古北花园二期小区公共充电站",
			// 	"LGN":          "",
			// 	"city":         "上海市",
			// 	"lon":          "121.400239",
			// 	"LAT":          "",
			// 	"lat":          "31.187854",
			// 	"business_id":  "978",
			// },
			// {
			// 	"station_id":   "118083",
			// 	"station_name": "无锡玉祁街道停车场 蔚来超充站",
			// 	"LGN":          "",
			// 	"city":         "无锡市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
			// {
			// 	"station_id":   "10742643",
			// 	"station_name": "观墩花苑09高",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
			// {
			// 	"station_id":   "21e5655f-00d5-487e-be28-2396f508d297",
			// 	"station_name": "仪征晟泰一品55栋3号桩",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
			// {
			// 	"station_id":   "0a592c30-fb84-4f72-bd99-c4e15dbb42f8",
			// 	"station_name": "东方名城充电站",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
			// {
			// 	"station_id":   "MA01H3BQ1.395815801_3210030101",
			// 	"station_name": "特来电扬州国泰大厦地下快充站",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestYunChargeCrawlingPriceTask(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	task := StartTaskMessage[YunChargeParams]{
		// Target: "yuncharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": "滁州市"},
		Params: YunChargeParams{
			Positions: []CityInfo{
				{
					StationID:   "70580",
					StationName: "易充得大润发充电站",
					City:        "海口市",
					CityCode:    "2554",
					Latitude:    "20.017587",
					Longitude:   "110.374097",
					Province:    "海南省",
				},
				{
					StationID:   "117158",
					StationName: "吴江区沃尔玛超市南侧充电站",
					City:        "苏州市",
					CityCode:    "1041",
					Latitude:    "31.1673",
					Longitude:   "120.640037",
					Province:    "江苏省",
				},
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestGenNonce(t *testing.T) {
	r := rand.New(rand.NewSource(time.Now().UnixNano() + rand.Int63n(100)))
	str := "0123456789abcdefghijklmnopqrstuvwxyz"
	bytes := []byte(str)
	result := []byte{}
	for i := 0; i < 4; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	fmt.Println(string(result))
}

func TestYunChargeTaskInQueue(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	// cityNames := [][]string{
	// 	{"32.038806", "118.641369"},
	// }

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, ykc_city_id, province, latitude, longitude from crawler_city_with_city_code where city = $1 and ykc_city_id != '' limit 1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	// for _, city := range citys {
	// 	curPositions := cityIdInfoMap[city]
	// 	// fmt.Println(curPositions)
	// 	msg := EChargeStartMessage{
	// 		TaskId: "1",
	// 		Target: "echarge",
	// 		OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": city},
	// 		Params: struct {
	// 			Positions []EChargeCityInfo "json:\"positions\""
	// 			SSDI      string            "json:\"ssdi\""
	// 			Token     string            "json:\"token\""
	// 		}{
	// 			Positions: curPositions,
	// 			SSDI:      "",
	// 			Token:     "",
	// 		},
	// 	}
	// 	r, _ := json.Marshal(msg)
	// 	_ = w.WriteMessages(context.Background(), kafka.Message{Value: r})
	// }

	cIdx := 0

	task := StartTaskMessage[YunChargeParams]{
		// TaskId: "1",
		// Target: "yuncharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: YunChargeParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[YunChargeParams]{
			// TaskId: "1",
			// Target: "yuncharge",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: YunChargeParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}

func TestYunChargeSupplyPositions(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_supply group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	cityIdInfoMap := map[string][]CityInfo{}

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, ykc_city_id, province, latitude, longitude from crawler_city_supply where city = $1 and ykc_city_id != ''"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	task := StartTaskMessage[YunChargeParams]{
		// Target: "yuncharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: YunChargeParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[YunChargeParams]{
			// Target: "yuncharge",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: YunChargeParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}
