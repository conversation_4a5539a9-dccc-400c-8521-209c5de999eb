package test

import (
	"context"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/common/util"

	// v1 "tianyan-crawler/internal/source/kd/api/v1"

	"github.com/segmentio/kafka-go"
)

func TestKDRespRead(t *testing.T) {
	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-resp-bird",
	})
	for {
		m, _ := r.ReadMessage(context.Background())
		t.Log(util.BytesToString(m.Value))
	}
}

func TestKDSendTask(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}
	// req1 := `{"oriReq":{"crawlTarget":"TARGET_PRICE","accountType":"VIP"},"params":{"positions":[{"address":"手机地点8270","city":"济南市","latitude":"36.********","longitude":"117.2882622","province":"山东省"},{"address":"手机地点8271","city":"济南市","latitude":"36.********","longitude":"117.6245306","province":"山东省"},{"address":"手机地点8272","city":"济南市","latitude":"37.********","longitude":"117.2888552","province":"山东省"},{"address":"手机地点8273","city":"济南市","latitude":"37.********","longitude":"117.6263096","province":"山东省"},{"address":"手机地点8274","city":"济南市","latitude":"37.3265186","longitude":"117.2894562","province":"山东省"},{"address":"手机地点8275","city":"济南市","latitude":"37.3265186","longitude":"117.6281125","province":"山东省"},{"address":"手机地点8276","city":"济南市","latitude":"36.********","longitude":"117.2882622","province":"山东省"},{"address":"手机地点8277","city":"济南市","latitude":"36.********","longitude":"117.6245306","province":"山东省"},{"address":"手机地点8278","city":"济南市","latitude":"36.********","longitude":"117.2876771","province":"山东省"},{"address":"手机地点8279","city":"济南市","latitude":"36.********","longitude":"117.6227752","province":"山东省"},{"address":"手机地点8280","city":"济南市","latitude":"36.********","longitude":"116.9519938","province":"山东省"},{"address":"手机地点8281","city":"济南市","latitude":"36.********","longitude":"116.6157254","province":"山东省"},{"address":"手机地点8282","city":"济南市","latitude":"37.********","longitude":"116.9514008","province":"山东省"},{"address":"手机地点8283","city":"济南市","latitude":"37.********","longitude":"116.6139464","province":"山东省"},{"address":"手机地点8284","city":"济南市","latitude":"37.3265186","longitude":"116.9507998","province":"山东省"},{"address":"手机地点8285","city":"济南市","latitude":"37.3265186","longitude":"116.6121435","province":"山东省"},{"address":"手机地点8286","city":"济南市","latitude":"36.********","longitude":"116.9519938","province":"山东省"},{"address":"手机地点8287","city":"济南市","latitude":"36.********","longitude":"116.6157254","province":"山东省"},{"address":"手机地点8288","city":"济南市","latitude":"36.********","longitude":"116.9525789","province":"山东省"},{"address":"手机地点8289","city":"济南市","latitude":"36.********","longitude":"116.6174808","province":"山东省"}],"token":"10088001android79e29c15c7645472db34fccfd1ee3f644"},"target":"kd","taskId":"20230908135607552000051031000976"}`
	req1 := `{"oriReq":{"crawlTarget":"TARGET_PRICE","accountType":"普通用户"},"params":{"positions":[{"address":"手机地点538","city":"广州市","latitude":"31.497948","longitude":"120.362121","province":"广东省"}],"token":"10088001android7a0877ffe9f024225aa19f6df8fc2d580"},"target":"kd","taskId":"20230908100526792000457030951589"}`
	// req2 := `{"params":{"token":"10088001ios34fe584b5f2494b3f9de8e60c45e9c35d", "positions":[{"latitude":"31.********","longitude":"120.7431595"},{"latitude":"31.********","longitude":"121.0588905"},{"latitude":"31.********","longitude":"120.7436145"},{"latitude":"31.********","longitude":"121.0602555"},{"latitude":"31.********","longitude":"120.7431595"},{"latitude":"31.********","longitude":"121.0588905"},{"latitude":"30.********","longitude":"120.7427106"},{"latitude":"30.********","longitude":"121.0575438"},{"latitude":"31.********","longitude":"120.4274285"},{"latitude":"31.********","longitude":"120.1116975"},{"latitude":"31.********","longitude":"120.4269735"},{"latitude":"31.********","longitude":"120.1103325"},{"latitude":"31.********","longitude":"120.4274285"},{"latitude":"31.********","longitude":"120.1116975"},{"latitude":"30.********","longitude":"120.4278774"},{"latitude":"30.********","longitude":"120.1130442"}]},"target":"kd","taskId":"mctest2"}`
	// req3 := `{"params":{"token":"10088001ios34fe584b5f2494b3f9de8e60c45e9c35d", "positions":[{"latitude":"31.36659592","longitude":"121.6303957"},{"latitude":"31.36659592","longitude":"121.9458991"},{"latitude":"31.63637576","longitude":"121.6308491"},{"latitude":"31.63637576","longitude":"121.9472594"},{"latitude":"31.09681608","longitude":"121.6303957"},{"latitude":"31.09681608","longitude":"121.9458991"},{"latitude":"30.82703624","longitude":"121.6299483"},{"latitude":"30.82703624","longitude":"121.9445569"},{"latitude":"31.36659592","longitude":"121.3148923"},{"latitude":"31.63637576","longitude":"121.3144389"},{"latitude":"31.09681608","longitude":"121.3148923"},{"latitude":"30.82703624","longitude":"121.3153397"}]},"target":"kd","taskId":"mctest3"}`
	//
	// w.WriteMessages(context.Background(), kafka.Message{Value: util.StringToBytes(req)}, kafka.Message{Value: util.StringToBytes(req2)}, kafka.Message{Value: util.StringToBytes(req3)})
	// req := fmt.Sprintf(`{"taskId": "mctest","target": "kd","params": {"positions": [{"latitude": "31.49055","longitude": "120.36434"}],"token": "%s"}}`, config.Config.TestToken)
	for i := 0; i < 1; i++ {
		_ = w.WriteMessages(context.Background(), kafka.Message{Value: util.StringToBytes(req1)})
	}
	// _ = w.WriteMessages(context.Background(), kafka.Message{Value: util.StringToBytes(req2)})
	// _ = w.WriteMessages(context.Background(), kafka.Message{Value: util.StringToBytes(req3)})

}

func TestKDSite(t *testing.T) {
	// api := v1.NewKDApi("")
	// m, err := api.GetSitePrice("XPCC632-QZ39070")
	// if err != nil {
	// 	t.Log(err.Stack())
	// } else {
	// 	b, _ := json.Marshal(m)
	// 	fmt.Println(util.BytesToString(b))
	// 	t.Log(util.BytesToString(b))
	// }
	// 	s, err := api.GetSiteList(1, "31.499127445701017", "120.35704693876883")
	// 	if err != nil {
	// 		t.Log(err.Stack())
	// 	} else {
	// 		// t.Log(s.SiteIdInfo())
	// 		// t.Log(s.SiteIdName())
	// 		t.Log(s.SiteTotal())
	// 		// t.Log(s.SiteCount())
	// 	}
	// 	d, err := api.GetSiteOperator("JSJC329-QX48543", "31.92445408", "118.9557864")
	// 	if err != nil {
	// 		t.Log(err.Stack())
	// 	} else {
	// 		t.Log(d.OperatorId())
	// 		t.Log(d.OperatorName())
	// 	}
}
