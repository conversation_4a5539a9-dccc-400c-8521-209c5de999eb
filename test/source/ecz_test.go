package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/segmentio/kafka-go"
)

type ECZAccountInfo struct {
	Token string `json:"token"`
}

func TestECZSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target: "ecz",
			Type:   "PRESET",
			AccountInfo: map[string]any{
				"token": "123",
			},
			GlobalParam: map[string]any{
				"bizType":    "DETAIL",
				"province":   "江苏省",
				"channel":    "依威新能源",
				"taskId":     "TASK66770442417754",
				"templateId": "TPL66810109714898_8",
			},
			NeedRetry:        true,
			NeedDynamicParam: false,
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "E充站",
				},
				{
					NeedSplit:  "N",
					ParamCode:  "city",
					ParamValue: "海口市,嘉兴市,杭州市,武汉市,廊坊市,惠州市,成都市,镇江市,南昌市,广州市,福州市,长沙市,天津市,上海市,北京市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "5725",
				"station_name": "碧桂园肇庆封开",
				"city":         "肇庆市",
				"CITY":         "肇庆市",
				"LAT":          "31.673595",
				"LGN":          "120.290727",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestECZCrawlingTaskFromDB(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	// citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	// var cityInfo EChargeCityInfo
	cityQueryStr := "select city, latitude, longitude from locations where city = $1"
	cityRows, err := dbpool.Query(context.Background(), cityQueryStr, "苏州市")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}

	curCitys := []CityInfo{}
	// curCityName := ""

	for cityRows.Next() {
		var cityInfo CityInfo
		err := cityRows.Scan(&cityInfo.City, &cityInfo.Latitude, &cityInfo.Longitude)
		if err != nil {
			fmt.Println(err)
		}
		if cityIdInfoMap[cityInfo.City] == nil {
			cityIdInfoMap[cityInfo.City] = []CityInfo{}
		}
		curCitys = append(curCitys, cityInfo)
	}

	fmt.Println(curCitys)

	params := []map[string]any{}
	for _, l := range curCitys {
		params = append(params, map[string]any{
			"city": l.City,
			"CITY": l.City,
			"LAT":  l.Latitude,
			"LGN":  l.Longitude,
		})
	}

	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target: "ecz",
			Type:   "PRESET",
			AccountInfo: map[string]any{
				"token": "123",
			},
			GlobalParam: map[string]any{
				"bizType":    "STATION",
				"province":   "江苏省",
				"channel":    "依威新能源",
				"taskId":     "TASK66850493515604",
				"templateId": "TPL66790843915101_3",
			},
			NeedRetry:        true,
			NeedDynamicParam: false,
			ParamConfigs:     []task.ParamConfig{},
		},
		RetryParams: params,
	}
	task.Init()
	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}
