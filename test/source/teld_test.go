package test

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/segmentio/kafka-go"
)

type TELDParams struct {
	Token     string     `json:"token"`
	SSDI      string     `json:"ssdi"`
	Positions []CityInfo `json:"positions"`
}

func TestTELDSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	// {"accountInfo":{"ssdi":"16b7d764-7c56-7fd3-eda0-a1974f75258c","openId":"","type":"alipay","userId":"","priKey":"","token":""},"globalParam":{"bizType":"STATION","templateId":"TPL66800294212869_1","taskId":"TASK66800294216736"},"needDynamicParam":true,"needRetry":false,"paramConfigs":[{"needSplit":"Y","paramCode":"city","paramValue":"无锡市"}],"target":"teld","taskInstanceId":"20241101113219023020521031241252","type":"PRESET"}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "teld",
			Type:   "PRESET",
			AccountInfo: map[string]any{
				"token": "C01eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aAHMWgEPoqyYcGLaUTTQn4xn6w__yiRkZ3C1DUxkeoc",
				"ssdi":  "1ee72b92-297c-1077-e33e-94a9f0e39de0",
			},
			GlobalParam: map[string]any{
				"bizType":    "STATION",
				"province":   "江苏省",
				"channel":    "特来电",
				"taskId":     "TASK66850493515604",
				"templateId": "TPL66800294212869_4",
			},
			NeedRetry:        true,
			NeedDynamicParam: false,
			ParamConfigs: []task.ParamConfig{
				// {
				// 	NeedSplit:  "N",
				// 	ParamCode:  "channel",
				// 	ParamValue: "特来电",
				// },
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "无锡市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "dcf459ff-15c2-4316-8d51-e09f28936c3f",
				"station_name": "无锡山韵佳苑C区31单元快充站",
				"city":         "无锡市",
				"CITY":         "无锡市",
				"city_code":    "320200",
				"LAT":          "31.548102",
				"lat":          "31.548102",
				"LGN":          "120.423769",
				"lon":          "120.423769",
			},
			// {
			// 	"station_id":   "53f4244e-ffdc-4db7-be58-ab14caab22d7",
			// 	"station_name": "无锡市气象局",
			// 	"city":         "无锡市",
			// 	"city_code":    "320200",
			// 	"lat":          "31.***************",
			// 	"lon":          "120.**************",
			// },
		},
	}
	task.Init()

	// task := `{"oriReq":{"crawlTarget":"CHARGING_GUN","accountType":"普通用户"},"params":{"deviceId":"b713ef74-a8eb-fda8-cf61-205e199e4840","positions":[{"city":"巴中市","cityCode":"511900","latitude":"31.700673","longitude":"106.556821","stationId":"8b8e1aa0-eae5-4d4c-b34b-c8bc25038bc8","stationName":"巴中柳林明汇集团充电站"}],"ssdi":"b713ef74-a8eb-fda8-cf61-205e199e4840","token":"C01eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Mb8ZY3QWtTfSAr4pCayZ-TLJksco3Pt_ua0YHiHbXcQ"},"target":"teld","taskId":"20240412115643235000006220640014"}`

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestTELDDataFromFile(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	content, err := os.ReadFile(filepath.Join("./", "teld_data.txt"))
	if err != nil {
		t.Fatal(err)
	}

	task := new(StartTaskMessage[TELDParams])
	if err := json.Unmarshal(content, &task); err != nil {
		t.Fatal(err)
	}

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestTledCronJob(t *testing.T) {
	map_t := map[string]string{}
	fmt.Println(map_t["a"])
}
