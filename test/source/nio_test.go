package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	// "tianyan-crawler/internal/source/yuncharge/api/util"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/segmentio/kafka-go"
)

type NIOParams struct {
	Positions []CityInfo `json:"positions"`
	Token     string     `json:"token"`
	DeviceID  string     `json:"deviceId"`
}

func TestNIOSendSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	// {"accountInfo":{"ssdi":"a8ebf7cdac70b6a5b5976745a284ae94","openId":"","type":"alipay","userId":"","priKey":"","token":"Bearer VoKz4FqsqSxWny303OVy7Pxrt6Z1WVbWlpy8dYkVmsX4swfXocpXbtzVAlT7T4VdK7To--yqbo7te4xVsQCotrEh3xZ6I38s8JL-vS2LRkE="},"globalParam":{"bizType":"PRICE","templateId":"TPL67070729814783_4","taskId":"TASK67050096910814"},"needDynamicParam":true,"needRetry":false,"paramConfigs":[{"needSplit":"N","paramCode":"city","paramValue":"无锡市"},{"needSplit":"N","paramCode":"channel","paramValue":"蔚来加电"}],"target":"nio","taskInstanceId":"20241125184743165017316030750717","type":"PRESET"}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target:           "nio",
			Type:             "PRESET",
			NeedDynamicParam: false,
			NeedRetry:        true,
			AccountInfo: map[string]any{
				"type":  "alipay",
				"ssdi":  "a8ebf7cdac70b6a5b5976745a284ae94",
				"token": "Bearer VoKz4FqsqSxWny303OVy7Pxrt6Z1WVbWlpy8dYkVmsX4swfXocpXbtzVAlT7T4VdK7To--yqbo7te4xVsQCotrEh3xZ6I38s8JL-vS2LRkE=",
			},
			GlobalParam: map[string]any{
				"bizType":    "STATION",
				"province":   "江苏省",
				"channel":    "蔚来加电",
				"taskId":     "TASK67090455519586",
				"templateId": "TPL67070360313735_7",
			},
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "蔚来加电",
				},
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "重庆市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "CS-XX-e2ae3148-313495ee",
				"station_name": "中石化-星星充电-宜兴新天地广场快充站",
				"city":         "上海市",
				"LGN":          "121.4619",
				"lon":          "121.4619",
				"LAT":          "31.22795",
				"lat":          "31.22795",
				"price_json":   "",
			},
			// {
			// 	"station_id":   "10742643",
			// 	"station_name": "观墩花苑09高",
			// 	"LGN":          "",
			// 	"city":         "廊坊市",
			// 	"lon":          "120.36303",
			// 	"LAT":          "",
			// 	"lat":          "31.482582",
			// },
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}

func TestNIOTaskInQueue(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_with_city_code group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	// cityInfos := []EChargeCityInfo{}
	cityIdInfoMap := map[string][]CityInfo{}

	// cityNames := [][]string{
	// 	{"32.038806", "118.641369"},
	// }

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, ykc_city_id, province, latitude, longitude from crawler_city_with_city_code where city = $1 and ykc_city_id != '' limit 1"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	// for _, city := range citys {
	// 	curPositions := cityIdInfoMap[city]
	// 	// fmt.Println(curPositions)
	// 	msg := EChargeStartMessage{
	// 		TaskId: "1",
	// 		Target: "echarge",
	// 		OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": city},
	// 		Params: struct {
	// 			Positions []EChargeCityInfo "json:\"positions\""
	// 			SSDI      string            "json:\"ssdi\""
	// 			Token     string            "json:\"token\""
	// 		}{
	// 			Positions: curPositions,
	// 			SSDI:      "",
	// 			Token:     "",
	// 		},
	// 	}
	// 	r, _ := json.Marshal(msg)
	// 	_ = w.WriteMessages(context.Background(), kafka.Message{Value: r})
	// }

	cIdx := 0

	task := StartTaskMessage[NIOParams]{
		// TaskId: "1",
		// Target: "yuncharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: NIOParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[NIOParams]{
			// TaskId: "1",
			// Target: "yuncharge",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: NIOParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}

func TestNIOSupplyPositions(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: config.Config.KafkaHosts,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	pgsqlURL := "postgres://zachbird@localhost:5432/dev"
	dbpool, err := pgxpool.New(context.Background(), pgsqlURL)

	if err != nil {
		fmt.Fprintf(os.Stderr, "Unable to create connection pool: %v\n", err)
		os.Exit(1)
	}
	defer dbpool.Close()

	cityNames, err := dbpool.Query(context.Background(), "select city from crawler_city_supply group by city")
	if err != nil {
		fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
		os.Exit(1)
	}
	defer cityNames.Close()

	citys := []string{}
	cityIdInfoMap := map[string][]CityInfo{}

	for cityNames.Next() {
		var r string
		err := cityNames.Scan(&r)
		if err != nil {
			fmt.Println(err)
		}
		citys = append(citys, r)
		// fmt.Println(citys)

		// var cityInfo EChargeCityInfo
		cityQueryStr := "select city, ykc_city_id, province, latitude, longitude from crawler_city_supply where city = $1 and ykc_city_id != ''"
		cityRows, err := dbpool.Query(context.Background(), cityQueryStr, r)
		if err != nil {
			fmt.Fprintf(os.Stderr, "QueryRow failed: %v\n", err)
			os.Exit(1)
		}

		curCitys := []CityInfo{}
		// curCityName := ""

		for cityRows.Next() {
			var cityInfo CityInfo
			err := cityRows.Scan(&cityInfo.City, &cityInfo.CityCode, &cityInfo.Province, &cityInfo.Latitude, &cityInfo.Longitude)
			if err != nil {
				fmt.Println(err)
			}
			if cityIdInfoMap[cityInfo.City] == nil {
				cityIdInfoMap[cityInfo.City] = []CityInfo{}
			}
			curCitys = append(curCitys, cityInfo)
		}

		cityIdInfoMap[r] = curCitys
	}

	cIdx := 0

	task := StartTaskMessage[NIOParams]{
		// Target: "yuncharge",
		// OriReq: map[string]string{"crawlTarget": "TARGET_PRICE", "accountType": "普通用户", "city": citys[cIdx]},
		Params: NIOParams{
			Positions: cityIdInfoMap[citys[cIdx]],
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
	fmt.Println("Start: ", citys[cIdx])

	for {
		r.ReadMessage(context.Background())
		fmt.Println("Finished: ", citys[cIdx])
		fmt.Println("---------*******---------")
		cIdx++
		if cIdx == len(citys) {
			continue
		}
		task := StartTaskMessage[NIOParams]{
			// Target: "yuncharge",
			// OriReq: map[string]string{"crawlTarget": "TARGET_SITE", "accountType": "普通用户", "city": citys[cIdx]},
			Params: NIOParams{
				Positions: cityIdInfoMap[citys[cIdx]],
			},
		}
		task.Init()

		_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
		fmt.Println("Start: ", citys[cIdx])
	}
}
func TestNIOGetPriceList(t *testing.T) {
	// priKey := util.RandStr(16)
	// fmt.Println("priKey", priKey)

	// pubKey, token := ForceLogin()
	// fmt.Println("pubKey", pubKey, token)

	// exchangeKey, _ := util.RsaEncrypt([]byte(priKey), pubKey)

	// fmt.Println("exchangeKey:", base64.StdEncoding.EncodeToString(exchangeKey))

	// ExchangeAppKey(base64.StdEncoding.EncodeToString(exchangeKey), priKey, token)
}
