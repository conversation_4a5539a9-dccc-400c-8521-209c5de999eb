package test

import (
	"context"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/segmentio/kafka-go"
)

type CDWAccountInfo struct {
	Token string `json:"token"`
}

func TestCDWSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	// {"globalParam":{"bizType":"PRICE","templateId":"TPL66790843915101_3","taskId":"TASK66770762114627"},"needDynamicParam":true,"needRetry":false,"paramConfigs":[{"needSplit":"N","paramCode":"city","paramValue":"天津市"},{"needSplit":"N","paramCode":"channel","paramValue":"车电网"}],"target":"cdw","taskInstanceId":"20240914184712863000124030751115","type":"PRESET"}
	// {"globalParam":{"bizType":"PRICE","templateId":"TPL66790843915101_4","taskId":"TASK66800193117317"},"needDynamicParam":true,"needRetry":false,"paramConfigs":[{"needSplit":"N","paramCode":"city","paramValue":"上海市"},{"needSplit":"N","paramCode":"channel","paramValue":"车电网"}],"target":"cdw","taskInstanceId":"20240918000020113129553030751110","type":"PRESET"}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			// TaskInstanceId:   "20240718163500236235752031042256",
			Target: "cdw",
			Type:   "PRESET",
			AccountInfo: map[string]any{
				"token": "123",
			},
			GlobalParam: map[string]any{
				"bizType":    "DETAIL",
				"province":   "江苏省",
				"channel":    "车电网",
				"taskId":     "TASK66800193117317",
				"templateId": "TPL66790843915101_4",
			},
			NeedRetry:        true,
			NeedDynamicParam: false,
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "车电网",
				},
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "上海市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "998",
				"station_name": "武汉滨江万科里充电站",
				// "fast_gun_id":  "8900",
				"slow_gun_id": "17322",
				"city":        "武汉市",
				"lat":         "22.5906740",
				"LAT":         "22.5906740",
				"lon":         "113.9924880",
				"LGN":         "113.9924880",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}
