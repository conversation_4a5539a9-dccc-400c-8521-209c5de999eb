package test

import (
	"context"
	"testing"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"

	"github.com/segmentio/kafka-go"
)

func TestYunHuiSingleTask(t *testing.T) {
	w := &kafka.Writer{
		Addr:                   kafka.TCP(config.Config.KafkaHosts...),
		Topic:                  config.Config.StartTaskTopic,
		AllowAutoTopicCreation: true,
	}
	// {"accountInfo":{"ssdi":"16b7d764-7c56-7fd3-eda0-a1974f75258c","openId":"","type":"alipay","userId":"","priKey":"","token":""},"globalParam":{"bizType":"STATION","templateId":"TPL66800294212869_1","taskId":"TASK66800294216736"},"needDynamicParam":true,"needRetry":false,"paramConfigs":[{"needSplit":"Y","paramCode":"city","paramValue":"无锡市"}],"target":"teld","taskInstanceId":"20241101113219023020521031241252","type":"PRESET"}
	task := StartTaskMessage[[]map[string]any]{
		StartTaskMessage: task.StartTaskMessage{
			Target: "yunhui",
			Type:   "PRESET",
			AccountInfo: map[string]any{
				"token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************.WK1U7X_-nsHjqtZTUySriQvbUF1_HwNIcaodLkD3j0U",
			},
			GlobalParam: map[string]any{
				"bizType":    "PRICE",
				"province":   "江苏省",
				"channel":    "云辉融电",
				"taskId":     "TASK66850493515604",
				"templateId": "TPL67050311513736_8",
			},
			NeedRetry:        true,
			NeedDynamicParam: false,
			ParamConfigs: []task.ParamConfig{
				{
					NeedSplit:  "N",
					ParamCode:  "channel",
					ParamValue: "云辉融电",
				},
				{
					NeedSplit:  "Y",
					ParamCode:  "city",
					ParamValue: "南通市",
				},
			},
		},
		RetryParams: []map[string]any{
			{
				"station_id":   "f4fd119dff7d4f0eab09e35a9170589b",
				"station_name": "斯博特充电站（启东高新区快充站）",
				"business_id":  "*********",
				"city":         "南通市",
				"CITY":         "南通市",
				"CITY_CODE":    "320600",
				"LAT":          "32.000000",
				"lat":          "32.000000",
				"LGN":          "120.864610",
				"lon":          "120.864610",
			},
		},
	}
	task.Init()

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: task.ToByte()})
}
