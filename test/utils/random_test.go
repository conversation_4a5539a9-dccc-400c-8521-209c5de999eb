package test

import (
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"testing"
	"time"
)

// RandIntStr returns the random int string with digit length,
// for 1 < digital <= 18. The result will be in the range of [0, 10^18 - 1].
// for digit length.
func RandIntStr(digit int) string {
	r := rand.New(rand.NewSource(time.Now().Unix()))
	return strconv.FormatInt(r.Int63n(int64(math.Pow10(digit))), 10)
}

func TestRandom(t *testing.T) {
	fmt.Println(RandIntStr(18))
}

type StationPriceResp struct {
	StubGroupDetailFeeInfos []map[string]any `json:"stubGroupDetailFeeInfos"`
}

func TestNPE(t *testing.T) {
	// priceInfo := make(map[string]any)

	spr := new(StationPriceResp)
	d, decodeErr := json.Marshal(nil)
	if decodeErr != nil {
		fmt.Println("星星充电价格数据解析失败")
		// priceInfo["fastGun"] = []map[string]any{}
		// fmt.Println(priceInfo)
	}

	fmt.Println([]byte(d))

	json.Unmarshal([]byte(d), &spr)
	fmt.Println(spr)
}

func TestJobId(t *testing.T) {
	ts := time.Now().Format("20060102150405")
	rd := ts + RandIntStr(1)
	fmt.Println(rd, len(rd))
}
