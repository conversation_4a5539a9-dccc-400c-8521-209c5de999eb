package test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"golang.org/x/time/rate"
)

func TestLimiter(t *testing.T) {
	// limiter := rate.NewLimiter(rate.Every(time.Second), 2)
	somtimes := rate.Sometimes{Interval: 1 * time.Second}
	for {
		somtimes.Do(func() {
			fmt.Println("Function called")
		})
		// if limiter.Allow() {
		// 	fmt.Println("Function called")
		// } else {
		// 	fmt.Println("Rate limit exceeded")
		// }
		// time.Sleep(100 * time.Millisecond)
	}
}

func TestWaitN(t *testing.T) {
	// limit := rate.NewLimiter(1, 5) // 每秒产生 3 个token，桶容量 5

	// ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	// defer cancel() // 超时取消
	limit := rate.Every(5 * time.Second)
	limiter := rate.NewLimiter(limit, 1)
	for /* 循环条件 */ {
		// 等待直到有足够的令牌可用
		t1 := time.Now()
		fmt.Println(t1.Format(time.DateTime), "开始等待")
		err := limiter.Wait(context.Background())
		if err != nil {
			// 处理错误，例如，如果上下文被取消
			fmt.Println("no exec")
		} else {
			t2 := time.Now()
			fmt.Println("exec", t2.Sub(t1))
		}
	}
	// for i := 0; ; i++ { // 有多少令牌直接消耗掉
	// 	fmt.Printf("%03d %s\n", i, time.Now().Format("2006-01-02 15:04:05.000"))
	// 	limit.Wait(context.Background())
	// 	// if err != nil { // 超时取消 err != nil
	// 	// 	fmt.Println("err: ", err.Error())
	// 	// 	return // 超时取消，退出 for
	// 	// }
	// }
}

func TestAllowN(t *testing.T) {
	r := rate.Every(1 * time.Millisecond)
	limit := rate.NewLimiter(r, 200)

	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if limit.Allow() {
			fmt.Printf("success，当前时间：%s\n", time.Now().Format("2006-01-02 15:04:05"))
		} else {
			fmt.Printf("success，但是被限流了。。。\n")
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
	})

	fmt.Println("http start ... ")
	_ = http.ListenAndServe(":8080", nil)
}

func TestLimit(t *testing.T) {
	// r.waitChan <- true
	// defer func(r *LimitRule) {
	// 	randomDelay := time.Duration(0)
	// 	if r.RandomDelay != 0 {
	// 		randomDelay = time.Duration(rand.Int63n(int64(r.RandomDelay)))
	// 	}
	// 	time.Sleep(r.Delay + randomDelay)
	// 	<-r.waitChan
	// }(r)
}
