package test

import (
	"fmt"
	"os"
	"runtime/debug"
	"strings"
	"testing"
	"time"

	"tianyan-crawler/internal/common/logger"
)

// TestPanicCapture 测试panic捕获和日志记录功能
func TestPanicCapture(t *testing.T) {
	// logger会在当前工作目录下的logs子目录创建日志文件
	panicLogFile := "logs/panic.log"

	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		t.Fatalf("获取工作目录失败: %v", err)
	}
	t.Logf("当前工作目录: %s", wd)

	// 先调用logger.Panic一次来确保logger初始化并创建日志文件
	logger.Panic("测试初始化")
	time.Sleep(100 * time.Millisecond)

	// 记录初始文件大小
	initialSize := int64(0)
	if info, err := os.Stat(panicLogFile); err == nil {
		initialSize = info.Size()
		t.Logf("初始日志文件大小: %d", initialSize)
	}

	// 测试panic捕获
	func() {
		defer func() {
			if err := recover(); err != nil {
				// 获取完整的堆栈信息
				stack := string(debug.Stack())
				panicMsg := fmt.Sprintf("PANIC: %v\nStack Trace:\n%s", err, stack)

				// 记录到panic日志文件
				logger.Panic(panicMsg)

				t.Logf("成功捕获panic: %v", err)
			}
		}()

		// 触发panic
		triggerPanic()
	}()

	// 等待日志写入
	time.Sleep(500 * time.Millisecond)

	// 验证日志文件大小是否增长
	if info, err := os.Stat(panicLogFile); err == nil {
		finalSize := info.Size()
		t.Logf("最终日志文件大小: %d", finalSize)

		if finalSize <= initialSize {
			t.Errorf("日志文件大小未增长，panic日志可能未被记录。初始: %d, 最终: %d", initialSize, finalSize)
			return
		}
	} else {
		t.Errorf("无法获取日志文件信息: %v", err)
		return
	}

	// 读取并验证日志内容
	content, err := os.ReadFile(panicLogFile)
	if err != nil {
		t.Errorf("读取panic日志文件失败: %v", err)
		return
	}

	logContent := string(content)
	t.Logf("Panic日志内容:\n%s", logContent)

	// 验证日志内容包含关键信息
	expectedKeywords := []string{
		"PANIC:",
		"测试panic捕获",
		"Stack Trace:",
		"triggerPanic",
	}

	for _, keyword := range expectedKeywords {
		if !strings.Contains(logContent, keyword) {
			t.Errorf("日志内容缺少关键字: %s", keyword)
		}
	}

	t.Log("panic捕获和日志记录测试通过")
}

// TestMultiplePanics 测试多次panic的记录
func TestMultiplePanics(t *testing.T) {
	panicLogFile := "logs/panic.log"

	initialSize := int64(0)
	if info, err := os.Stat(panicLogFile); err == nil {
		initialSize = info.Size()
	}

	// 模拟多次panic
	for i := 0; i < 3; i++ {
		func() {
			defer func() {
				if err := recover(); err != nil {
					stack := string(debug.Stack())
					panicMsg := fmt.Sprintf("PANIC: %v\nStack Trace:\n%s", err, stack)
					logger.Panic(panicMsg)
				}
			}()

			panic(fmt.Sprintf("测试panic #%d", i+1))
		}()
	}

	// 等待日志写入
	time.Sleep(200 * time.Millisecond)

	// 验证日志文件大小增长
	if info, err := os.Stat(panicLogFile); err == nil {
		if info.Size() <= initialSize {
			t.Errorf("日志文件大小未增长，可能未成功记录panic信息")
		} else {
			t.Logf("日志文件大小从 %d 增长到 %d 字节", initialSize, info.Size())
		}
	} else {
		t.Errorf("无法获取日志文件信息: %v", err)
	}
}

// TestPanicWithDifferentTypes 测试不同类型的panic
func TestPanicWithDifferentTypes(t *testing.T) {
	testCases := []struct {
		name      string
		panicFunc func()
		keyword   string
	}{
		{
			name: "字符串panic",
			panicFunc: func() {
				panic("这是一个字符串panic")
			},
			keyword: "字符串panic",
		},
		{
			name: "错误panic",
			panicFunc: func() {
				panic(fmt.Errorf("这是一个错误panic"))
			},
			keyword: "错误panic",
		},
		{
			name: "数字panic",
			panicFunc: func() {
				panic(42)
			},
			keyword: "42",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var captured bool
			func() {
				defer func() {
					if err := recover(); err != nil {
						stack := string(debug.Stack())
						panicMsg := fmt.Sprintf("PANIC: %v\nStack Trace:\n%s", err, stack)
						logger.Panic(panicMsg)
						captured = true

						// 验证panic内容
						if !strings.Contains(panicMsg, tc.keyword) {
							t.Errorf("panic消息未包含期望的关键字: %s", tc.keyword)
						}
					}
				}()

				tc.panicFunc()
			}()

			if !captured {
				t.Errorf("未成功捕获panic: %s", tc.name)
			} else {
				t.Logf("成功捕获并记录panic: %s", tc.name)
			}
		})
	}
}

// triggerPanic 触发一个panic用于测试
func triggerPanic() {
	panic("测试panic捕获")
}

// TestLoggerPanicDirectly 直接测试logger.Panic函数
func TestLoggerPanicDirectly(t *testing.T) {
	panicLogFile := "logs/panic.log"

	initialSize := int64(0)
	if info, err := os.Stat(panicLogFile); err == nil {
		initialSize = info.Size()
	}

	// 直接调用logger.Panic
	testMessage := "直接测试panic日志记录功能"
	logger.Panic(testMessage)

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 验证日志文件
	if info, err := os.Stat(panicLogFile); err == nil {
		if info.Size() <= initialSize {
			t.Errorf("日志文件大小未增长")
		} else {
			t.Logf("日志文件大小从 %d 增长到 %d 字节", initialSize, info.Size())
		}

		// 读取并验证日志内容
		content, err := os.ReadFile(panicLogFile)
		if err != nil {
			t.Errorf("读取日志文件失败: %v", err)
		} else {
			if !strings.Contains(string(content), testMessage) {
				t.Errorf("日志内容未包含测试消息: %s", testMessage)
			} else {
				t.Logf("成功在日志中找到测试消息")
			}
		}
	} else {
		t.Errorf("日志文件不存在或无法访问: %v", err)
	}
}
