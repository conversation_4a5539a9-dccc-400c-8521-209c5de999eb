package test

import (
	"context"
	"testing"

	"github.com/segmentio/kafka-go"
)

var KAFKA_HOSTS = []string{
	"**************:9092",
	"**************:9092",
	"**************:9092",
}

func TestKafka(t *testing.T) {
	topic := "spider-task-start-bird"
	w := &kafka.Writer{
		Addr:                   kafka.TCP(KAFKA_HOSTS...),
		Topic:                  topic,
		AllowAutoTopicCreation: true,
	}

	r := kafka.NewReader(kafka.ReaderConfig{
		Brokers: KAFKA_HOSTS,
		Topic:   "spider-task-finished-bird",
		GroupID: "spider-task-finished-bird",
	})

	message, err := r.<PERSON>(context.Background())
	if err != nil {
		t.Error(err)
	}
	t.Logf("message: %s", string(message.Value))

	_ = w.WriteMessages(context.Background(), kafka.Message{Value: []byte("hello world")})
}
