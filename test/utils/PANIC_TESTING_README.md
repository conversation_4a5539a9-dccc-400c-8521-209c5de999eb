# Panic 捕获和日志记录系统

## 概述

这个系统实现了全面的panic捕获和持久化日志记录功能，包括：

1. 在`main.go`中的全局panic捕获
2. 在`logger.go`中的专用panic日志记录方法
3. 完整的测试套件验证功能正确性

## 实现细节

### 1. 日志记录方法 (`internal/common/logger/logger.go`)

```go
func Panic(panicMsg string) {
    if config.Config.Mode == "DEBUG" {
        log.Println("PANIC:", panicMsg)
    }
    PanicLogger.Error(panicMsg)
}
```

### 2. 全局panic捕获 (`main.go`)

```go
defer func() {
    if err := recover(); err != nil {
        // 获取完整的堆栈信息
        stack := string(debug.Stack())
        panicMsg := fmt.Sprintf("PANIC: %v\nStack Trace:\n%s", err, stack)
        
        // 记录到panic日志文件
        logger.Panic(panicMsg)
        
        // 控制台输出（保留原有逻辑）
        log.Printf("出现异常: 全局异常捕获 %v\n", err)
        debug.PrintStack()
    }
}()
```

## 使用方法

### 在应用程序中使用

1. **直接记录panic信息**：
```go
logger.Panic("发生了严重错误")
```

2. **在panic捕获中使用**：
```go
defer func() {
    if err := recover(); err != nil {
        stack := string(debug.Stack())
        panicMsg := fmt.Sprintf("PANIC: %v\nStack Trace:\n%s", err, stack)
        logger.Panic(panicMsg)
    }
}()
```

### 在不同场景中的应用

#### 1. HTTP处理器中的panic捕获
```go
func (h *Handler) SomeAPI(w http.ResponseWriter, r *http.Request) {
    defer func() {
        if err := recover(); err != nil {
            stack := string(debug.Stack())
            panicMsg := fmt.Sprintf("API PANIC: %v\nStack Trace:\n%s", err, stack)
            logger.Panic(panicMsg)
            
            // 返回错误响应
            response := h.errorResponse(StatusServiceError, "服务内部错误")
            h.sendJSONResponse(w, response)
        }
    }()
    
    // API 逻辑...
}
```

#### 2. Goroutine中的panic捕获
```go
go func() {
    defer func() {
        if err := recover(); err != nil {
            stack := string(debug.Stack())
            panicMsg := fmt.Sprintf("GOROUTINE PANIC: %v\nStack Trace:\n%s", err, stack)
            logger.Panic(panicMsg)
        }
    }()
    
    // goroutine 逻辑...
}()
```

## 测试套件

### 运行所有panic测试

```bash
# 运行所有panic相关测试
go test -v ./test/utils/ -run TestPanic

# 运行特定测试
go test -v ./test/utils/ -run TestPanicCapture
go test -v ./test/utils/ -run TestMainPanicSimulation
go test -v ./test/utils/ -run TestPanicInDifferentGoroutines
```

### 测试覆盖的场景

1. **基本panic捕获** (`TestPanicCapture`)
   - 测试基本的panic捕获和日志记录
   - 验证日志文件正确生成和内容

2. **多次panic记录** (`TestMultiplePanics`)
   - 测试连续多次panic的记录
   - 验证日志文件大小增长

3. **不同类型panic** (`TestPanicWithDifferentTypes`)
   - 字符串panic
   - 错误对象panic
   - 数字panic

4. **直接日志记录** (`TestLoggerPanicDirectly`)
   - 直接调用logger.Panic方法
   - 验证日志记录功能

5. **主程序模拟** (`TestMainPanicSimulation`)
   - 模拟main.go中的panic处理场景
   - 测试实际运行时的panic捕获

6. **多goroutine场景** (`TestPanicInDifferentGoroutines`)
   - 测试不同goroutine中的panic捕获
   - 验证并发场景下的日志记录

7. **panic恢复链** (`TestRecoveryChain`)
   - 测试嵌套panic捕获
   - 验证panic重新抛出机制

## 日志文件位置

- **开发环境**: `logs/panic.log`
- **测试环境**: `test/utils/logs/panic.log`

## 日志格式

```
2025/08/25 10:08:47 [ERROR] PANIC: 错误信息
Stack Trace:
goroutine 21 [running]:
runtime/debug.Stack()
    /usr/local/go/src/runtime/debug/stack.go:24 +0x64
...详细堆栈信息...
```

## 最佳实践

1. **总是在goroutine中添加panic捕获**：
   ```go
   go func() {
       defer func() {
           if err := recover(); err != nil {
               // 记录panic信息
           }
       }()
       // 业务逻辑
   }()
   ```

2. **在HTTP处理器中添加panic捕获**：
   ```go
   func handler(w http.ResponseWriter, r *http.Request) {
       defer func() {
           if err := recover(); err != nil {
               // 记录panic并返回错误响应
           }
       }()
       // 处理逻辑
   }
   ```

3. **记录上下文信息**：
   ```go
   panicMsg := fmt.Sprintf("API PANIC [%s %s]: %v\nStack Trace:\n%s", 
       r.Method, r.URL.Path, err, stack)
   ```

4. **定期检查panic日志**：
   - 监控panic.log文件大小和内容
   - 设置日志告警机制
   - 分析panic模式和频率

## 故障排除

### 日志文件未生成
- 检查logs目录权限
- 确认logger包正确初始化
- 验证工作目录路径

### 日志内容不完整
- 增加等待时间确保日志写入完成
- 检查日志缓冲区配置
- 验证panic信息格式化是否正确

### 性能考虑
- panic日志记录是异步的，不会阻塞应用程序
- 大量panic可能影响性能，需要修复根本原因
- 定期清理或轮转panic日志文件
