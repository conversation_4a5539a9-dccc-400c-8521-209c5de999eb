package test

// import (
// 	"fmt"
// 	"testing"
// 	"time"

// 	v8 "rogchap.com/v8go"
// )

// func TestV8Go(t *testing.T) {
// 	for {
// 		// iso := v8.NewIsolate()     // creates a new JavaScript VM
// 		// ctx1 := v8.NewContext(iso) // new context within the VM
// 		// ctx1.RunScript("const multiply = (a, b) => a * b", "math.js")

// 		// ctx2 := v8.NewContext(iso) // another context on the same VM
// 		// if _, err := ctx2.RunScript("multiply(3, 4)", "main.js"); err != nil {
// 		// 	// this will error as multiply is not defined in this context
// 		// 	fmt.Println(err)
// 		// }

// 		ctx := v8.NewContext()                                  // creates a new V8 context with a new Isolate aka VM
// 		ctx.RunScript("const add = (a, b) => a + b", "math.js") // executes a script on the global context
// 		ctx.RunScript("const result = add(3, 4)", "main.js")    // any functions previously added to the context can be called
// 		val, _ := ctx.RunScript("result", "value.js")           // return a value in JavaScript back to Go
// 		fmt.Printf("addition result: %s", val)
// 		time.Sleep(5 * time.Second)
// 	}
// }
