package test

import (
	"bufio"
	"errors"
	"fmt"
	"io"
	"os/exec"
	"testing"
)

func GetOutput(stdio io.ReadCloser, ch chan<- cmdOutput) {
	stdioReader := bufio.NewReader(stdio)
	cmdOutput := cmdOutput{}
	for {
		line, err := stdioReader.ReadBytes('\n')
		if string(line) == "errrrrrr\n" {
			cmdOutput.err = errors.New("errrrrrr")
			ch <- cmdOutput
			break
		}
		// if err != nil {
		// 	fmt.Println(err)
		// 	cmdOutput.err = err
		// 	ch <- cmdOutput
		// 	break
		// }
		if io.EOF == err {
			break
		}
		cmdOutput.content = string(line)
		ch <- cmdOutput
	}
}

type cmdOutput struct {
	content string
	err     error
}

func ExecScript(chResult chan<- any) error {
	cmd := exec.Command("python3", "-u", "../../scripts/python3/loop_print.py")

	stdout, err := cmd.StdoutPipe()
	if err != nil {
		fmt.Println("stdout err:", err)
	}
	cmd.Stderr = cmd.Stdout

	if err = cmd.Start(); err != nil {
		fmt.Println("start err:", err)
	}

	// scanner := bufio.NewScanner(stdout)
	// for scanner.Scan() {
	// 	fmt.Println(scanner.Text())
	// }

	ch := make(chan cmdOutput)

	// go func() {
	// 	if err = cmd.Wait(); err != nil {
	// 		fmt.Println("wait err:", err)
	// 	}
	// 	close(ch)
	// }()

	go func() {
		GetOutput(stdout, ch)
	}()

	// go func() {
	// 	GetOutput(stderr, ch)
	// }()
	for output := range ch {
		if output.err != nil {
			fmt.Println("error:", output.err)
			cmd.Process.Kill()
		}
		fmt.Println("output:", output.content)
		chResult <- output.content
	}

	// stdoutReader := bufio.NewReader(stdout)
	// stderrReader := bufio.NewReader(stderr)
	// for {
	// 	line, err := stdoutReader.ReadBytes('\n')
	// 	errLine, _ := stderrReader.ReadBytes('\n')
	// 	if err != nil || io.EOF == err {
	// 		fmt.Println(err)
	// 		break
	// 	}
	// 	fmt.Println("stderr:", string(errLine))
	// 	var dd map[string]any
	// 	e := json.Unmarshal(line, &dd)
	// 	if e != nil {
	// 		fmt.Print("stdout: ", string(line))
	// 		continue
	// 	}
	// 	fmt.Println("dd", dd)
	// }
	if err = cmd.Wait(); err != nil {
		fmt.Println("wait err:", err)
	}
	// close(ch)
	return nil
}

func TestExec(t *testing.T) {
	chResult := make(chan any, 1)
	go func() {
		err := ExecScript(chResult)
		if err != nil {
			fmt.Println(err)
			return
		}
		close(chResult)
	}()

	for r := range chResult {
		fmt.Println(r)
	}
	// for r := range chResult {
	// 	fmt.Println(r)
	// }
	// close(chResult)

}
