package test

// import (
// 	"fmt"
// 	"log"
// 	"os"
// 	"path/filepath"
// 	"sync"
// 	"testing"

// 	python3 "github.com/go-python/cpy3"
// )

// func LoadModule(dir string) *python3.PyObject {
// 	path := python3.PyImport_ImportModule("sys").GetAttrString("path")
// 	python3.PyList_Insert(path, 0, python3.PyUnicode_FromString(dir))
// 	return python3.PyImport_ImportModule(filepath.Base(dir))
// }

// func Convert(input string) string {
// 	module := LoadModule(filepath.Join("../../", "scripts", "py3", "convert"))
// 	function := module.GetAttrString("Convert")
// 	args := python3.PyTuple_New(1)
// 	python3.PyTuple_SetItem(args, 0, python3.PyUnicode_FromString(input))
// 	return python3.PyUnicode_AsUTF8(function.Call(args, python3.Py_None))
// }

// func TestPY3(t *testing.T) {
// 	content, err := os.ReadFile(filepath.Join("../../", "scripts", "py3", "app.py"))
// 	if err != nil {
// 		log.Fatal(err)
// 	}
// 	fmt.Println(string(content))

// 	python3.Py_Initialize()
// 	if !python3.Py_IsInitialized() {
// 		log.Fatalln("Failed to initialize Python environment")
// 	}

// 	wg := sync.WaitGroup{}
// 	wg.Add(1)
// 	go func() {
// 		python3.PyRun_SimpleString("print('Hello World')")
// 		// ret := Convert("Dr. Juan Q. Xavier de la Vega III (Doc Vega)")
// 		wg.Done()
// 	}()
// 	wg.Wait()
// 	// fmt.Println(ret)
// 	python3.Py_Finalize()
// }
