package test

import (
	"fmt"
	"runtime/debug"
	"testing"

	"tianyan-crawler/internal/common/logger"
)

// TestMainPanicSimulation 模拟main.go中的panic捕获场景
func TestMainPanicSimulation(t *testing.T) {
	// 这个测试模拟了main.go中的panic处理逻辑

	// 模拟main函数的defer panic捕获
	func() {
		defer func() {
			if err := recover(); err != nil {
				// 获取完整的堆栈信息
				stack := string(debug.Stack())
				panicMsg := fmt.Sprintf("PANIC: %v\nStack Trace:\n%s", err, stack)

				// 记录到panic日志文件
				logger.Panic(panicMsg)

				// 控制台输出（保留原有逻辑）
				t.Logf("出现异常: 全局异常捕获 %v\n", err)
				t.Logf("堆栈信息已写入panic.log")
			}
		}()

		// 模拟应用程序中可能出现的panic
		simulateApplicationPanic()
	}()

	t.<PERSON>g("成功捕获并记录了应用程序panic")
}

// simulateApplicationPanic 模拟应用程序中的panic
func simulateApplicationPanic() {
	// 模拟数组越界
	arr := make([]int, 5)
	_ = arr[10] // 这会触发panic
}

// TestPanicInDifferentGoroutines 测试不同goroutine中的panic捕获
func TestPanicInDifferentGoroutines(t *testing.T) {
	done := make(chan bool, 2)

	// Goroutine 1
	go func() {
		defer func() {
			if err := recover(); err != nil {
				stack := string(debug.Stack())
				panicMsg := fmt.Sprintf("GOROUTINE-1 PANIC: %v\nStack Trace:\n%s", err, stack)
				logger.Panic(panicMsg)
				t.Logf("Goroutine 1 panic captured: %v", err)
			}
			done <- true
		}()

		panic("goroutine 1 发生了panic")
	}()

	// Goroutine 2
	go func() {
		defer func() {
			if err := recover(); err != nil {
				stack := string(debug.Stack())
				panicMsg := fmt.Sprintf("GOROUTINE-2 PANIC: %v\nStack Trace:\n%s", err, stack)
				logger.Panic(panicMsg)
				t.Logf("Goroutine 2 panic captured: %v", err)
			}
			done <- true
		}()

		panic("goroutine 2 发生了panic")
	}()

	// 等待两个goroutine完成
	<-done
	<-done

	t.Log("成功捕获了多个goroutine中的panic")
}

// TestRecoveryChain 测试panic恢复链
func TestRecoveryChain(t *testing.T) {
	var panicCount int

	// 外层恢复
	func() {
		defer func() {
			if err := recover(); err != nil {
				panicCount++
				stack := string(debug.Stack())
				panicMsg := fmt.Sprintf("OUTER PANIC: %v\nStack Trace:\n%s", err, stack)
				logger.Panic(panicMsg)
				t.Logf("外层捕获panic: %v", err)
			}
		}()

		// 内层函数
		func() {
			defer func() {
				if err := recover(); err != nil {
					panicCount++
					stack := string(debug.Stack())
					panicMsg := fmt.Sprintf("INNER PANIC: %v\nStack Trace:\n%s", err, stack)
					logger.Panic(panicMsg)
					t.Logf("内层捕获panic: %v", err)

					// 重新抛出panic，让外层也能捕获
					panic(fmt.Sprintf("重新抛出: %v", err))
				}
			}()

			panic("内层函数发生panic")
		}()
	}()

	if panicCount != 2 {
		t.Errorf("期望捕获2次panic，实际捕获%d次", panicCount)
	}

	t.Log("成功测试了panic恢复链")
}
