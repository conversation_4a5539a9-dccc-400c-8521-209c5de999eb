#!/bin/bash
# Custom Go unit test runner which runs all unit tests in parallel except for
# known flaky unit tests.
# Flaky unit tests are run sequentially in the second phase and retried up to
# three times.

# Why are there flaky unit tests?
#
# Some of the Go unit tests are inherently flaky e.g. because they use the
# real timer implementation and might fail when things take longer as usual.
# In particular, this happens when the system is under load and threads do not
# get scheduled as fast as usual. Then, the expected timings do not match.

# Set GO_PARALLEL variable in the same way as the Makefile does.
# We repeat this here because this script is called directly by test.go
# and not via the Makefile.
# GO_PARALLEL=

# All Go packages with test files.
# Output per line: <full Go package name> <all _test.go files in the package>*
packages_with_tests=$(go list -f '{{if len .TestGoFiles}}{{.ImportPath}} {{join .TestGoFiles " "}}{{end}}{{if len .XTestGoFiles}}{{.ImportPath}} {{join .XTestGoFiles " "}}{{end}}' ./... | sort)

# Flaky tests have the suffix "_flaky_test.go".
# Exclude endtoend tests
all_tests=$(echo "$packages_with_tests" | cut -d" " -f1 | grep -v "endtoend")
# echo $all_tests

# Run non-flaky tests.
echo "$all_tests" | xargs go test $GO_PARALLEL -v -count=1
if [ $? -ne 0 ]; then
    echo "ERROR: Go unit tests failed. See above for errors."
    echo
    echo "This should NOT happen. Did you introduce a unit test?"
    exit 1
fi
