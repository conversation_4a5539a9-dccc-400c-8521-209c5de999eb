# Panic 测试手册

## 概述
在 `main.go` 中添加了完整的panic测试功能，可以通过环境变量手动触发不同类型的panic来测试panic捕获和日志记录系统。

## 使用方法

### 1. 通过环境变量触发panic

```bash
TEST_PANIC=<panic类型> go run main.go
```

### 2. 使用测试脚本

```bash
./test_panic.sh <panic类型>
```

## 支持的Panic类型

| 类型 | 描述 | 触发的错误 |
|------|------|------------|
| `string` | 字符串panic | 自定义字符串错误 |
| `slice` | 数组越界panic | runtime error: index out of range |
| `nil` | 空指针panic | runtime error: invalid memory address |
| `goroutine` | goroutine中的panic | 测试并发场景下的panic处理 |
| `nested` | 嵌套函数panic | 测试深层调用栈的panic跟踪 |
| `default` | 默认panic | 简单的测试panic |

## 测试示例

### 1. 测试字符串panic
```bash
TEST_PANIC=string go run main.go
```

**预期输出**:
```
检测到TEST_PANIC环境变量: string，将在3秒后触发panic进行测试...
准备触发字符串panic...
出现异常: 全局异常捕获 这是一个手动触发的字符串panic，用于测试panic捕获功能
```

### 2. 测试嵌套函数panic
```bash
TEST_PANIC=nested go run main.go
```

**预期输出**:
```
检测到TEST_PANIC环境变量: nested，将在3秒后触发panic进行测试...
准备触发嵌套函数panic...
进入嵌套函数1...
进入嵌套函数2...
进入嵌套函数3，即将panic...
出现异常: 全局异常捕获 这是在深层嵌套函数中触发的panic，可以测试堆栈跟踪
```

**堆栈跟踪会显示完整的调用链**:
```
main.nestedFunction3()
main.nestedFunction2()
main.nestedFunction1()
main.triggerNestedPanic()
main.checkTestPanic()
main.main()
```

### 3. 测试数组越界panic
```bash
TEST_PANIC=slice go run main.go
```

**预期输出**:
```
检测到TEST_PANIC环境变量: slice，将在3秒后触发panic进行测试...
准备触发数组越界panic...
出现异常: 全局异常捕获 runtime error: index out of range [10] with length 5
```

### 4. 测试goroutine中的panic
```bash
TEST_PANIC=goroutine go run main.go
```

这个测试会触发两个panic:
- 一个在goroutine中（会被goroutine内的defer捕获）
- 一个在主线程中（会被main函数的defer捕获）

## 查看panic日志

### 查看最新的panic记录
```bash
tail -20 logs/panic.log
```

### 搜索特定panic
```bash
grep -A 10 -B 2 "PANIC:" logs/panic.log
```

### 查看今天的panic记录
```bash
grep "$(date '+%Y/%m/%d')" logs/panic.log
```

## 日志格式说明

panic日志的格式为:
```
2025/08/25 10:17:15 [ERROR] PANIC: <错误信息>
Stack Trace:
<详细的堆栈跟踪信息>
```

堆栈跟踪包含:
- Goroutine状态
- 调用栈中每个函数的文件路径和行号
- 函数调用关系

## 实际测试结果示例

### 字符串panic日志
```
2025/08/25 10:16:51 [ERROR] PANIC: 这是一个手动触发的字符串panic，用于测试panic捕获功能
Stack Trace:
goroutine 1 [running]:
runtime/debug.Stack()
        /usr/local/go/src/runtime/debug/stack.go:24 +0x64
main.main.func2()
        /Users/<USER>/Projects/tianyan-crawler/main.go:125 +0x38
panic({0x10273f4e0?, 0x102803320?})
        /usr/local/go/src/runtime/panic.go:914 +0x218
main.triggerStringPanic()
        /Users/<USER>/Projects/tianyan-crawler/main.go:54 +0x88
main.checkTestPanic()
        /Users/<USER>/Projects/tianyan-crawler/main.go:37 +0x17c
main.main()
        /Users/<USER>/Projects/tianyan-crawler/main.go:142 +0xac
```

## 注意事项

1. **程序会在检测到TEST_PANIC环境变量后3秒触发panic**
2. **panic会导致程序终止，这是正常的测试行为**
3. **所有panic信息都会被记录到 `logs/panic.log` 文件中**
4. **在生产环境中不要设置TEST_PANIC环境变量**
5. **测试完成后检查panic日志确认记录正确**

## 快速测试命令

```bash
# 测试所有类型的panic
for panic_type in string slice nil nested default; do
    echo "=== 测试 $panic_type panic ==="
    TEST_PANIC=$panic_type go run main.go &
    sleep 5
    kill %1 2>/dev/null || true
    echo ""
done

# 查看所有测试结果
echo "=== Panic日志记录 ==="
tail -50 logs/panic.log
```

## 故障排除

### 如果panic没有被触发
- 检查TEST_PANIC环境变量是否正确设置
- 确认程序运行时间足够长（至少4秒）

### 如果panic日志没有记录
- 检查logs目录是否存在
- 确认logger包初始化正常
- 检查文件权限

### 如果堆栈信息不完整
- 确认使用的是debug.Stack()获取堆栈
- 检查程序是否在panic后立即退出
