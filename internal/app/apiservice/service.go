package apiservice

import (
	"encoding/json"
	"fmt"
	"net/http"

	"tianyan-crawler/internal/app/apiservice/router"
	"tianyan-crawler/internal/source/dynamic"
	dynamicApi "tianyan-crawler/internal/source/dynamic/api"
)

type HTTPService struct {
	Router *router.Router
}

// Handler 处理器结构体，包含所有接口处理逻辑
type Handler struct{}

// NewHandler 创建新的处理器实例
func NewHandler() *Handler {
	return &Handler{}
}

const (
	StatusSuccess                       = "10000"
	StatusServiceInProgress             = "20000"
	StatusServiceError                  = "30000"
	StatusServiceInsufficientPermission = "40000"
	StatusParamsMissing                 = "51000"
	StatusParamsInvalid                 = "52000"
	StatusBizError                      = "60000"
	StatusTinyBootError                 = "70000"
)

func StatusText(code string) string {
	switch code {
	case StatusSuccess:
		return "成功"
	case StatusServiceInProgress:
		return "服务处理中"
	case StatusServiceError:
		return "服务异常"
	case StatusServiceInsufficientPermission:
		return "服务权限不足"
	case StatusParamsMissing:
		return "缺少必选参数"
	case StatusParamsInvalid:
		return "无效的参数"
	case StatusBizError:
		return "业务处理失败"
	case StatusTinyBootError:
		return "微平台错误"
	default:
		return ""
	}
}

type Response struct {
	Code       string `json:"code"`
	Message    string `json:"msg"`
	SubCode    string `json:"subCode"`
	SubMessage string `json:"subMsg"`
	Success    bool   `json:"success"`
	Data       any    `json:"data"`
}

func (r *Response) ToJSON() ([]byte, error) {
	j, err := json.Marshal(r)
	if err != nil {
		return []byte{}, err
	}
	return j, nil
}

// 通用响应处理方法
func (h *Handler) sendJSONResponse(w http.ResponseWriter, response Response) {
	w.Header()["Content-Type"] = []string{"application/json; charset=utf-8"}
	msg, _ := response.ToJSON()
	fmt.Fprint(w, string(msg))
}

// 创建成功响应
func (h *Handler) successResponse(data any) Response {
	return Response{
		Code:       StatusSuccess,
		Message:    StatusText(StatusSuccess),
		SubCode:    "",
		SubMessage: "",
		Success:    true,
		Data:       data,
	}
}

// 创建错误响应
func (h *Handler) errorResponse(code, subMessage string) Response {
	return Response{
		Code:       code,
		Message:    StatusText(code),
		SubCode:    "",
		SubMessage: subMessage,
		Success:    false,
	}
}

// 健康检查接口处理
func (h *Handler) HealthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header()["Content-Type"] = []string{"text/plain; charset=utf-8"}
	fmt.Fprint(w, "OK")
}

// Python脚本检查接口处理
func (h *Handler) CheckPythonScript(w http.ResponseWriter, r *http.Request) {
	scriptUrl := r.URL.Query().Get("scriptUrl")
	if scriptUrl == "" {
		response := h.errorResponse(StatusParamsMissing, "参数 scriptUrl 缺失")
		h.sendJSONResponse(w, response)
		return
	}

	api := dynamicApi.New(dynamic.CrawlTargetDesc)
	script, err := api.GetDynamicScript(scriptUrl)
	if err != nil {
		response := h.errorResponse(StatusServiceError, err.Error())
		h.sendJSONResponse(w, response)
		return
	}

	err = api.CheckScript(script)
	if err != nil {
		response := h.errorResponse(StatusServiceError, err.Error())
		h.sendJSONResponse(w, response)
		return
	}

	response := h.successResponse(nil)
	h.sendJSONResponse(w, response)
}

// 注册所有路由
func (h *Handler) RegisterRoutes(r *router.Router) {
	// 健康检查接口
	r.HandleFunc("GET", "/health/check", h.HealthCheck)

	// 脚本检查接口
	r.HandleFunc("GET", "/scripts/check/python", h.CheckPythonScript)

	// 添加新接口的步骤：
	// 1. 在 Handler 中添加新的处理方法 (参考 ExampleAPI)
	// 2. 在这里注册路由: r.HandleFunc("方法", "路径", h.你的处理方法)
}

func NewHTTPService() *HTTPService {
	r := router.NewRouter()
	handler := NewHandler()

	// 注册所有路由
	handler.RegisterRoutes(r)

	return &HTTPService{Router: r}
}
