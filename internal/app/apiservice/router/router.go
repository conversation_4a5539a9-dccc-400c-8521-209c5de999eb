package router

import "net/http"

type Router struct {
	Routes map[string]map[string]http.HandlerFunc
}

func (r *Router) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	handler, ok := r.Routes[req.Method][req.URL.Path]
	if !ok {
		http.NotFound(w, req)
	} else if ok {
		handler(w, req)
	} else {
		http.Error(w, http.StatusText(http.StatusMethodNotAllowed), http.StatusMethodNotAllowed)
	}
}

func (r *Router) HandleFunc(method, path string, handler http.HandlerFunc) {
	if r.Routes == nil {
		r.Routes = make(map[string]map[string]http.HandlerFunc)
	}
	if _, ok := r.Routes[method]; !ok {
		r.Routes[method] = make(map[string]http.HandlerFunc)
	}
	r.Routes[method][path] = handler
}

func NewRouter() *Router {
	return new(Router)
}
