package sender

import (
	"context"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/scheduler"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"

	"github.com/riete/errors"
	"github.com/segmentio/kafka-go"
)

var (
	taskRespTopic = config.Config.TaskRespTopic
)

type Sender struct {
	w *kafka.Writer

	ch     <-chan *task.TaskResponseMessage
	ctx    context.Context
	cancel context.CancelFunc
}

func (s Sender) send(message kafka.Message) {
	if err := s.w.WriteMessages(context.Background(), message); err != nil {
		logger.Error(errors.NewFromErr(err).Trace("发送爬取结果失败"))
	}
}

func (s Sender) Send() {
	for {
		select {
		case <-s.ctx.Done():
			return
		case d := <-s.ch:
			go s.send(kafka.Message{Value: d.ToByte()})
		}
	}
}

func (s Sender) Close() {
	s.cancel()
	_ = s.w.Close()
}

func NewTaskRespSender(ch <-chan *task.TaskResponseMessage) scheduler.TaskSender {
	ctx, cancel := context.WithCancel(context.Background())
	return &Sender{
		w: &kafka.Writer{
			Addr:                   kafka.TCP(config.Config.KafkaHosts...),
			Topic:                  taskRespTopic,
			Logger:                 logger.KafkaLogger,
			RequiredAcks:           kafka.RequireOne,
			AllowAutoTopicCreation: true,
		},
		ch:     ch,
		ctx:    ctx,
		cancel: cancel,
	}
}
