package receiver

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/riete/errors"
	"github.com/segmentio/kafka-go"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/scheduler"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
	"tianyan-crawler/internal/common/util"

	_ "tianyan-crawler/internal/source/cdw"
	_ "tianyan-crawler/internal/source/dkcloud"
	_ "tianyan-crawler/internal/source/dynamic"
	_ "tianyan-crawler/internal/source/echarge"
	_ "tianyan-crawler/internal/source/ecz"
	_ "tianyan-crawler/internal/source/gqenergy"
	_ "tianyan-crawler/internal/source/hfcharge"
	_ "tianyan-crawler/internal/source/huolala"
	_ "tianyan-crawler/internal/source/nio"
	_ "tianyan-crawler/internal/source/star"
	_ "tianyan-crawler/internal/source/sycharge"
	_ "tianyan-crawler/internal/source/teld"
	_ "tianyan-crawler/internal/source/wjcloud"
	_ "tianyan-crawler/internal/source/xiaoju"
	_ "tianyan-crawler/internal/source/yuncharge"
	_ "tianyan-crawler/internal/source/yunhui"
)

const (
	FetchingParamsURL = "/fastdata-platform-admin/crawl/task/client/getDynamicParam"
	PageSize          = 1000
)

var (
	taskStartTopic  = config.Config.StartTaskTopic
	taskCancelTopic = config.Config.CancelTaskTopic
)

type Receiver struct {
	ts     *kafka.Reader
	tc     *kafka.Reader
	mutex  sync.RWMutex
	tasks  map[string]task.TaskRunner
	ch     chan<- *task.TaskResponseMessage
	ctx    context.Context
	cancel context.CancelFunc
}

func (r *Receiver) setTask(taskId string, runner task.TaskRunner) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.tasks[taskId] = runner
}

func (r *Receiver) getTask(taskId string) task.TaskRunner {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	return r.tasks[taskId]
}

func (r *Receiver) deleteTask(taskId string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	delete(r.tasks, taskId)
}

type StartTaskMessageResponse struct {
	Code string `json:"code"`
	Data struct {
		Params []map[string]any `json:"params"`
		Total  int              `json:"total"`
	} `json:"data"`
	Msg     string `json:"msg"`
	SubMsg  string `json:"subMsg"`
	Success bool   `json:"success"`
}

func getStartTaskMessage(pageNo int, templateId string, query map[string]string) (StartTaskMessageResponse, error) {
	options := []request.Option{
		request.WithDefaultClient(),
		request.WithTimeout(60 * time.Second),
		request.WithHeader(map[string]string{
			"c-random": "HM",
		}),
	}
	rClient := request.NewRequest(options...)

	body := map[string]any{
		"templateId": templateId,
		"param":      query,
		"pageNo":     pageNo,
		"pageSize":   PageSize,
	}
	sr := StartTaskMessageResponse{}

	jsonBody, err := json.Marshal(body)
	if err != nil {
		return sr, err
	}

	err = rClient.Post(config.Config.FastDataHost+FetchingParamsURL, jsonBody)
	if err != nil {
		return sr, err
	}

	_ = json.Unmarshal(rClient.Content(), &sr)
	return sr, nil
}

func (r *Receiver) fetchStartTaskMessage(stm task.StartTaskMessage, query map[string]string) ([]map[string]any, error) {
	params := []map[string]any{}
	pageNo := 0
	templateId, ok := stm.GlobalParam["templateId"].(string)
	if !ok {
		return nil, errors.New(fmt.Sprintf("任务ID: %s, templateId must be string", stm.TaskInstanceId))
	}
	for {
		pageNo += 1
		sr, err := getStartTaskMessage(pageNo, templateId, query)
		if err != nil {
			return nil, err
		}
		params = append(params, sr.Data.Params...)
		if sr.Data.Total >= pageNo*PageSize {
			continue
		}
		break
	}
	logger.Info(fmt.Sprintf("任务ID: %s, (templateId: %s, %v) 条件下参数 共 %d 条", stm.TaskInstanceId, templateId, query, len(params)))
	return params, nil
}

func (r *Receiver) splitStartTaskMessage(stm task.StartTaskMessage) []map[string]string {
	var queries []map[string]string
	// 处理参数
	for _, param := range stm.ParamConfigs {
		if param.NeedSplit == "Y" {
			splitValues := strings.Split(param.ParamValue, ",")
			if len(queries) == 0 {
				// 初始化结果列表
				for _, value := range splitValues {
					result := make(map[string]string)
					result[param.ParamCode] = value
					queries = append(queries, result)
				}
			} else {
				// 扩展现有的结果列表
				newResults := []map[string]string{}
				for _, result := range queries {
					for _, value := range splitValues {
						newResult := make(map[string]string)
						for k, v := range result {
							newResult[k] = v
						}
						newResult[param.ParamCode] = value
						newResults = append(newResults, newResult)
					}
				}
				queries = newResults
			}
		} else {
			if len(queries) == 0 {
				// 初始化结果列表
				result := make(map[string]string)
				result[param.ParamCode] = param.ParamValue
				queries = append(queries, result)
			} else {
				// 更新现有的结果列表
				for i := range queries {
					queries[i][param.ParamCode] = param.ParamValue
				}
			}
		}
	}

	return queries
}

func (r *Receiver) doStartTask() {
	for {
		select {
		case <-r.ctx.Done():
			return
		default:
			message, err := r.ts.ReadMessage(context.Background())
			if err != nil {
				logger.Error(errors.NewFromErr(err).Trace(fmt.Sprintf("Read message from topic [%s] error, retry in 5 seconds", taskStartTopic)))
				time.Sleep(5 * time.Second)
				continue
			}
			logger.Info(fmt.Sprintf("获取到新任务请求, 请求数据: %s", util.BytesToString(message.Value)))

			startMsg := task.StartTaskMessage{}
			if err := json.Unmarshal(message.Value, &startMsg); err != nil {
				logger.Error(errors.NewFromErr(err).Trace("解析请求数据失败"))
				continue
			}

			templateId, ok := startMsg.GlobalParam["templateId"].(string)
			if !ok {
				logger.Error(errors.NewFromErr(err).Trace("GlobalParam -> templateId 必须为 string 类型"))
				continue
			}
			startMsg.TemplateId = templateId

			te, err := task.SEMapping.Get(startMsg.Type, startMsg.Target)
			if err != nil {
				logger.Error(errors.NewFromErr(err).Trace("[Task Error]: 不支持的爬虫渠道"))
				continue
			}

			if startMsg.NeedRetry {
				logger.Info(fmt.Sprintf("重试任务, 直接运行, taskId: %s", startMsg.TaskInstanceId))
				startMsg.Params = startMsg.RetryParams
				runner := te.Runner(startMsg, r.ch)
				r.setTask(startMsg.TaskInstanceId, runner)
				go func() {
					defer r.deleteTask(startMsg.TaskInstanceId)
					runner.Exec()
					if startMsg.Target == "yunhui" {
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
					}
					if startMsg.Target == "huolala" {
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
					}
				}()
				continue
			}

			if !startMsg.NeedDynamicParam && !startMsg.NeedRetry {
				logger.Info(fmt.Sprintf("无动态参数, 直接运行任务, taskId: %s", startMsg.TaskInstanceId))
				query := map[string]any{}
				for _, param := range startMsg.ParamConfigs {
					query[param.ParamCode] = param.ParamValue
				}
				startMsg.Params = []map[string]any{query}
				runner := te.Runner(startMsg, r.ch)
				r.setTask(startMsg.TaskInstanceId, runner)
				go func() {
					defer r.deleteTask(startMsg.TaskInstanceId)
					runner.Exec()
					if startMsg.Target == "yunhui" {
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
					}
					if startMsg.Target == "huolala" {
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
					}
				}()
				continue
			}

			queries := r.splitStartTaskMessage(startMsg)
			if len(queries) == 0 {
				// paramConfigs 为空
				logger.Info(fmt.Sprintf("任务参数为空, 直接运行任务, taskId: %s", startMsg.TaskInstanceId))
				runner := te.Runner(startMsg, r.ch)
				r.setTask(startMsg.TaskInstanceId, runner)
				go func() {
					defer r.deleteTask(startMsg.TaskInstanceId)
					runner.Exec()
					if startMsg.Target == "yunhui" {
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
					}
					if startMsg.Target == "huolala" {
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
					}
				}()
				continue
			}

			paramCh := make(chan []map[string]any, len(queries))
			wg := &sync.WaitGroup{}
			wg.Add(len(queries))
			for _, query := range queries {
				go func(q map[string]string) {
					defer wg.Done()
					params, err := r.fetchStartTaskMessage(startMsg, q)
					if err != nil {
						bizType, ok := startMsg.GlobalParam["bizType"].(string)
						if !ok {
							bizType = ""
						}
						logger.PostErrorLog(logger.ErrorLogMessage{
							RequestNo:   startMsg.TaskInstanceId,
							RequestData: nil,
							BizType:     bizType,
							Channel:     startMsg.Target,
							StackTrace:  errors.NewFromErr(err).Trace(fmt.Sprintf("任务ID: %s, 获取参数列表失败, query: [%v]", startMsg.TaskInstanceId, q)).Stack(),
							ErrorMsg:    fmt.Sprintf("任务ID: %s, 获取参数列表失败, query: [%v]", startMsg.TaskInstanceId, q),
						})
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
						logger.Error(errors.NewFromErr(err).Trace(fmt.Sprintf("任务ID: %s, 获取参数列表失败, query: [%v]", startMsg.TaskInstanceId, q)))
						return
					}
					if len(params) == 0 && startMsg.Type != string(task.Dynamic) {
						// 动态脚本即使返回的参数列表为空也要执行一次
						// bizType, ok := startMsg.GlobalParam["bizType"].(string)
						// if !ok {
						// 	bizType = ""
						// }
						// logger.PostErrorLog(logger.ErrorLogMessage{
						// 	RequestNo:   startMsg.TaskInstanceId,
						// 	RequestData: nil,
						// 	BizType:     bizType,
						// 	Channel:     startMsg.Target,
						// 	ErrorMsg:    fmt.Sprintf("任务ID: %s, 任务参数为空, query: [%v]", startMsg.TaskInstanceId, q),
						// })

						// 动态参数列表列表为空，直接结束任务
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
						logger.Info(fmt.Sprintf("任务ID: %s, 任务参数为空, query: [%v]", startMsg.TaskInstanceId, q))
						return
					}
					paramCh <- params
				}(query)
			}
			wg.Wait()
			close(paramCh)

			for params := range paramCh {
				startMsg.Params = params
				runner := te.Runner(startMsg, r.ch)
				r.setTask(startMsg.TaskInstanceId, runner)
				go func() {
					defer r.deleteTask(startMsg.TaskInstanceId)
					runner.Exec()
					if startMsg.Target == "yunhui" {
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
					}
					if startMsg.Target == "huolala" {
						task.PostFinishMessage(startMsg.TaskInstanceId, startMsg.TemplateId)
					}
				}()
			}
		}
	}
}

func (r *Receiver) doCancelTask() {
	for {
		select {
		case <-r.ctx.Done():
			return
		default:
			message, err := r.tc.ReadMessage(context.Background())
			if err != nil {
				logger.Error(errors.NewFromErr(err).Trace(fmt.Sprintf("read message from topic [%s] error, retry in 5 seconds", taskCancelTopic)))
				time.Sleep(5 * time.Second)
				continue
			}
			logger.Info(fmt.Sprintf("获取到任务取消请求, 请求数据: %s", util.BytesToString(message.Value)))
			var req task.CancelTaskMessage
			if err := json.Unmarshal(message.Value, &req); err != nil {
				logger.Error(errors.NewFromErr(err).Trace("解析请求数据失败"))
				continue
			}
			runner := r.getTask(req.TaskInstanceId)
			if runner != nil {
				runner.Cancel()
				r.deleteTask(req.TaskInstanceId)
			}
		}
	}
}

func (r *Receiver) Receive() {
	go r.doCancelTask()
	r.doStartTask()
}

func (r *Receiver) Close() {
	r.cancel()
	_ = r.ts.Close()
	_ = r.tc.Close()
}

func newReadConfig(topic, consumerGroupId string) kafka.ReaderConfig {
	return kafka.ReaderConfig{
		Brokers:  config.Config.KafkaHosts,
		GroupID:  consumerGroupId,
		Topic:    topic,
		MaxBytes: 10e6,
		Logger:   logger.KafkaLogger,
	}
}

func NewTaskReceiver(ch chan<- *task.TaskResponseMessage) scheduler.TaskReceiver {
	ctx, cancel := context.WithCancel(context.Background())
	return &Receiver{
		ts:     kafka.NewReader(newReadConfig(taskStartTopic, taskStartTopic)),
		tc:     kafka.NewReader(newReadConfig(taskCancelTopic, taskCancelTopic)),
		tasks:  make(map[string]task.TaskRunner),
		ch:     ch,
		ctx:    ctx,
		cancel: cancel,
	}
}
