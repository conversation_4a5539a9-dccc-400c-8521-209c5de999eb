package scheduler

// - 负责任务在应用中的传递，所以基础调度单元为「任务」
// - 控制接收器消费并发任务消费速率
// - 控制执行器并发速率
// - 控制任务的状态，在各环节负责消息发送
// - 类似于任务取消，重试等，需要通过调度器进行

type TaskReceiver interface {
	Receive()
	Close()
}

type TaskSender interface {
	Send()
	Close()
}

type TaskScheduler struct {
	r TaskReceiver
	s TaskSender
}

func (t TaskScheduler) Start() {
	go t.r.Receive()
	go t.s.Send()
}

func (t TaskScheduler) Stop() {
	t.s.Close()
	t.r.Close()
}

func NewTaskScheduler(r TaskReceiver, s TaskSender) *TaskScheduler {
	return &TaskScheduler{r: r, s: s}
}
