package task

import (
	"fmt"
)

type SourceExecutorMapping map[string]TaskExecutor

type TaskExecutor struct {
	Source string
	Runner func(StartTaskMessage, chan<- *TaskResponseMessage) TaskRunner
}

var SEMapping = SourceExecutorMapping{}

func (m SourceExecutorMapping) Register(source string, runner func(StartTaskMessage, chan<- *TaskResponseMessage) TaskRunner) {
	m[source] = TaskExecutor{
		Source: source,
		Runner: runner,
	}
}

func (m SourceExecutorMapping) Exist(source string) bool {
	_, ok := m[source]
	return ok
}

type TemplateType string

const (
	Builtin TemplateType = "PRESET"
	Dynamic TemplateType = "DYNAMIC_SCRIPT"
)

func (m SourceExecutorMapping) Get(templateType, source string) (TaskExecutor, error) {
	if templateType == string(Dynamic) {
		// type 为 DYNAMIC_SCRIPT 时 ececutor 固定为 Dynamic
		source = string(Dynamic)
	} else if templateType != string(Builtin) {
		return TaskExecutor{}, fmt.Errorf("[Executor Error] 未找到类型 [%s] 对应的执行器", templateType)
	}

	if ok := m.Exist(source); !ok {
		return TaskExecutor{}, fmt.Errorf("[Executor Error] 未找到来源 [%s] 对应的执行器", source)
	}
	return m[source], nil
}

func (m SourceExecutorMapping) Remove(source string) (bool, error) {
	if ok := m.Exist(source); !ok {
		return false, fmt.Errorf("[Executor Error] 未找到来源 [%s] 对应的执行器", source)
	}
	delete(m, source)
	return true, nil
}
