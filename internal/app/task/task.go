package task

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
)

type TaskReceiver interface {
	Receive()
	Close()
}

type TaskRunner interface {
	Exec()
	Cancel()
}

type TaskRespSender interface {
	Send()
	Close()
}

type ParamConfig struct {
	NeedSplit  string `json:"needSplit"`
	ParamCode  string `json:"paramCode"`
	ParamValue string `json:"paramValue"`
}

type StartTaskMessage struct {
	TaskInstanceId   string           `json:"taskInstanceId"`
	TemplateId       string           `json:"templateId"`
	Target           string           `json:"target"`
	Type             string           `json:"type"`
	AccountInfo      map[string]any   `json:"accountInfo"`
	GlobalParam      map[string]any   `json:"globalParam"`
	Params           []map[string]any `json:"params"`
	NeedDynamicParam bool             `json:"needDynamicParam"`
	ParamConfigs     []ParamConfig    `json:"paramConfigs"`
	NeedRetry        bool             `json:"needRetry"`
	RetryParams      []map[string]any `json:"retryParams"`
}

type CancelTaskMessage struct {
	TaskInstanceId string `json:"taskInstanceId"`
}

func (t CancelTaskMessage) ToByte() []byte {
	b, _ := json.Marshal(t)
	return b
}

type CrawlingResult struct {
	Param map[string]any `json:"param"`
	Data  any            `json:"data"`
}

func NewCrawlingResult(
	param map[string]any,
	data any,
) CrawlingResult {
	// TODO: 临时方案，后续改掉
	if reflect.TypeOf(data) == reflect.TypeOf(map[string]any{}) {
		data = []any{data}
	}
	return CrawlingResult{
		Param: param,
		Data:  data,
	}
}

type TaskResponseMessage struct {
	TaskInstanceId string         `json:"taskInstanceId"`
	Target         string         `json:"target"`
	GlobalParam    map[string]any `json:"globalParam"`
	Param          map[string]any `json:"param"`
	Data           any            `json:"data"`
	StartAt        string         `json:"startAt"`
	EndAt          string         `json:"endAt"`
}

func (t TaskResponseMessage) ToByte() []byte {
	b, _ := json.Marshal(t)
	return b
}

func (t *TaskResponseMessage) PrepareToSend(result CrawlingResult) {
	t.Param = result.Param
	t.Data = result.Data
	t.EndAt = time.Now().Format(time.DateTime)
}

const PostFinishedMessageURL = "/fastdata-platform-admin/crawl/task/client/complete"

func PostFinishMessage(taskInstanceId, templateId string) error {
	options := []request.Option{
		request.WithDefaultClient(),
		request.WithTimeout(60 * time.Second),
		request.WithHeader(map[string]string{
			"c-random": "HM",
		}),
	}
	rClient := request.NewRequest(options...)

	body := map[string]any{
		"taskInstanceId": taskInstanceId,
		"templateId":     templateId,
	}

	jsonBody, err := json.Marshal(body)
	if err != nil {
		return err
	}

	err = rClient.Post(config.Config.FastDataHost+PostFinishedMessageURL, jsonBody)
	if err != nil {
		return err
	}

	logger.Info("任务完成 >>> ", "TaskId", taskInstanceId, "TemplateId", templateId)
	return nil
}

const RefreshAccountInfoURL = "/fastdata-platform-admin/crawl/task/client/getEffectiveAccount"

type AccountInfoResp struct {
	Success bool   `json:"success"`
	Data    string `json:"data"`
	SubMsg  string `json:"subMsg"`
}

func RefreshAccountInfo(channelCode, channelDesc string) ([]byte, error) {
	options := []request.Option{
		request.WithDefaultClient(),
		request.WithTimeout(60 * time.Second),
		request.WithHeader(map[string]string{
			"c-random": "HM",
		}),
	}
	rClient := request.NewRequest(options...)

	body := map[string]any{
		"channelCode": channelCode,
	}

	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	err = rClient.Post(config.Config.FastDataHost+RefreshAccountInfoURL, jsonBody)
	if err != nil {
		return nil, err
	}

	logger.Info(fmt.Sprintf("渠道: %s, 更新账户信息: %s", channelDesc, rClient.ContentToString()))
	air := AccountInfoResp{}
	_ = json.Unmarshal(rClient.Content(), &air)
	if !air.Success {
		return nil, fmt.Errorf("渠道: %s, 更新账户信息失败: %s", channelDesc, air.SubMsg)
	}
	return []byte(air.Data), nil
}

func NewTaskResponseMessage(stm StartTaskMessage) *TaskResponseMessage {
	return &TaskResponseMessage{
		TaskInstanceId: stm.TaskInstanceId,
		Target:         stm.Target,
		// NeedRetry:      stm.NeedRetry,
		GlobalParam: stm.GlobalParam,
		StartAt:     time.Now().Format(time.DateTime),
	}
}
