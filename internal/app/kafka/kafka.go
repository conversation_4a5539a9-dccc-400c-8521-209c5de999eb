package kafka

func Reader() {

}

func Writer() {

}

// func NewKafkaClient(groupID, topic string) (*KafkaClient, error) {
// 	rc := kafka.ReaderConfig{
// 		Brokers:  []string{"172.16.255.225:9092", "172.16.255.227:9092", "172.16.255.228:9092"},
// 		GroupID:  groupID,
// 		Topic:    topic,
// 		MaxBytes: 10e6,
// 		Logger:   logger.KafkaLogger,
// 	}
// 	if err != nil {
// 		return nil, err
// 	}

// 	producer, err := kafka.NewProducer(&kafka.ConfigMap{"bootstrap.servers": brokers})
// 	if err != nil {
// 		return nil, err
// 	}

// 	err = consumer.Subscribe(topic, nil)
// 	if err != nil {
// 		return nil, err
// 	}

// 	return &KafkaClient{
// 		Consumer: consumer,
// 		Producer: producer,
// 	}, nil
// }

// func (kc *KafkaClient) Close() {
// 	kc.Consumer.Close()
// 	kc.Producer.Close()
// }

// func (kc *KafkaClient) SendMessage(topic, message string) error {
// 	return kc.Producer.Produce(&kafka.Message{
// 		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
// 		Value:          []byte(message),
// 	}, nil)
// }
