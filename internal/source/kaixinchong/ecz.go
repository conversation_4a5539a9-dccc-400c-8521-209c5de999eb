package ecz

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/riete/errors"
	"github.com/riete/gpool"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/source/ecz/api"
)

const (
	CrawlTarget     = "ecz"
	CrawlTargetDesc = "E充站"
	DelayTime       = 1 * time.Second
)

var pool = gpool.NewLongTermPool(config.Config.ECZMaxConcurrence)

type ApiCommonResp struct {
	param api.Param
	data  []map[string]any
	err   errors.Error
}

type StationListApiResp struct {
	ApiCommonResp
}

type StationDetailApiResp struct {
	ApiCommonResp
}

type StationPriceApiResp struct {
	ApiCommonResp
}

type ChargingGunApiResp struct {
	ApiCommonResp
}

type Task struct {
	originMessage task.StartTaskMessage
	message       api.TaskMessage

	api    api.ECZApi
	ch     chan<- *task.TaskResponseMessage
	ctx    context.Context
	cancel context.CancelFunc

	chStationList   chan StationListApiResp
	chStationDetail chan StationDetailApiResp
	chStationPrice  chan StationPriceApiResp
	chChargingGun   chan ChargingGunApiResp
	existStations   map[string]bool

	mutex              sync.Mutex
	detailSuccess      int
	detailFailed       int
	chargingGunSuccess int
	chargingGunFailed  int
}

func (t *Task) fetchStationList(p api.Param) {
	for {
		select {
		case <-t.ctx.Done():
			return
		default:
			acr := ApiCommonResp{param: p}
			stations, err := t.api.GetStationList(p)
			if err != nil {
				logger.Error(err.Trace(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下站点信息爬取失败", t.message.TaskInstanceId, p.Lat, p.Lgn)))
				logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下, 站点信息爬取失败", t.message.TaskInstanceId, p.Lat, p.Lgn))
				acr.err = err
				t.chStationList <- StationListApiResp{ApiCommonResp: acr}
				return
			}

			acr.data = stations
			t.chStationList <- StationListApiResp{ApiCommonResp: acr}
			logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下总计%d个站点", t.message.TaskInstanceId, p.Lat, p.Lgn, len(stations)))
			return
		}
	}
}

func (t *Task) CrawlingStationList() {
	select {
	case <-t.ctx.Done():
		return
	default:
		totalTask := len(t.message.Params)
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(totalTask)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.fetchStationList(p)
				}(curParam)
				time.Sleep(DelayTime)
			}
			wg.Wait()
			close(t.chStationList)
		}()

		for i := range t.chStationList {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    fmt.Sprintf("(%s, %s) 经纬度下站点列表爬取失败", i.param.Lat, i.param.Lgn),
				})
				continue
			}

			if len(i.data) == 0 {
				logger.Info(fmt.Sprintf("任务ID: %s, 站点列表爬取结束, 站点列表为空", t.message.TaskInstanceId))
				continue
			}

			for _, siteInfo := range i.data {
				stationId := siteInfo["stationId"].(string)
				if stationId == "" {
					logger.PostErrorLog(logger.ErrorLogMessage{
						RequestNo:   t.message.TaskInstanceId,
						RequestData: i.param,
						BizType:     t.message.GlobalParam.BizType,
						Channel:     t.message.Target,
						StackTrace:  errors.New(fmt.Sprintf("任务ID: %s, 站点ID解析失败, [%v]", t.message.TaskInstanceId, siteInfo)).Stack(),
						ErrorMsg:    fmt.Sprintf("任务ID: %s, 站点ID解析失败, [%v]", t.message.TaskInstanceId, siteInfo),
					})
					continue
				}

				if t.existStations[stationId] {
					continue
				}

				t.existStations[stationId] = true
				resp := task.NewTaskResponseMessage(t.originMessage)
				resp.PrepareToSend(task.NewCrawlingResult(
					api.Param{}.ToAnyMap(),
					siteInfo,
				))
				t.ch <- resp
			}
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 站点列表爬取结束, 总计: %d 个", t.message.TaskInstanceId, len(t.existStations)))
	}
}

func (t *Task) fetchStationDetail(p api.Param) ([]map[string]any, errors.Error) {
	detail, err := t.api.GetStationDetail(p)
	if err != nil {
		t.mutex.Lock()
		t.detailFailed += 1
		t.mutex.Unlock()
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点详情失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	} else {
		t.mutex.Lock()
		t.detailSuccess += 1
		t.mutex.Unlock()
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点详情成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	}
	return detail, err
}

func (t *Task) executeStationDetailCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		logger.Info(fmt.Sprintf("任务ID: %s, 取消, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		return
	default:
		detail, err := t.fetchStationDetail(p)
		t.chStationDetail <- StationDetailApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  detail,
				err:   err,
			},
		}
	}
}

func (t *Task) CrawlingStationDetail() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点详情, 去重后总计%d个站点", t.message.TaskInstanceId, total))
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeStationDetailCrawl(p)
				}(curParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chStationDetail)
		}()

		for i := range t.chStationDetail {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点详情失败",
				})
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点详情爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.detailSuccess, t.detailFailed, total))
	}
}

func (t *Task) fetchChargingGun(p api.Param) ([]map[string]any, errors.Error) {
	chargingGun, err := t.api.GetChargingGunList(p)
	if err != nil {
		t.mutex.Lock()
		t.chargingGunFailed += 1
		t.mutex.Unlock()
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点充电枪列表失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	} else {
		t.mutex.Lock()
		t.chargingGunSuccess += 1
		t.mutex.Unlock()
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点充电枪列表成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	}
	return chargingGun, err
}

func (t *Task) executeChargingGunCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		logger.Info(fmt.Sprintf("任务ID: %s, 取消, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		return
	default:
		acr := ApiCommonResp{param: p}
		chargingGun, err := t.fetchChargingGun(p)
		if err != nil {
			logger.Info(fmt.Sprintf("任务ID: %s, 站点ID: %s, 获取充电枪列表失败", t.message.TaskInstanceId, p.StationID))
			logger.Error(err.Trace(fmt.Sprintf("任务ID: %s, 站点ID: %s, 获取充电枪列表失败", t.message.TaskInstanceId, p.StationID)))
			acr.err = err
			t.chChargingGun <- ChargingGunApiResp{ApiCommonResp: acr}
			return
		}

		acr.data = chargingGun
		t.chChargingGun <- ChargingGunApiResp{ApiCommonResp: acr}
		logger.Info(fmt.Sprintf("任务ID: %s, 站点ID: %s, 总计%d个站点", t.message.TaskInstanceId, p.StationID, len(chargingGun)))
	}
}

func (t *Task) CrawlingChargingGun() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点充电枪列表, 去重后总计%d个站点", t.message.TaskInstanceId, total))
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeChargingGunCrawl(p)
				}(curParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chChargingGun)
		}()

		totalMsg := 0
		for i := range t.chChargingGun {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点充电枪列表失败",
				})
				continue
			}
			if len(i.data) == 0 {
				continue
			}

			totalMsg += 1
			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点充电枪列表爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.chargingGunSuccess, t.chargingGunFailed, totalMsg))
	}
}

func (t *Task) Exec() {
	crawlingType := t.message.GlobalParam.BizType
	if crawlingType == "" {
		logger.Error(errors.New("[Task Error]: BizType 参数缺失"))
		return
	}

	switch crawlingType {
	case "STATION":
		t.CrawlingStationList()
	case "DETAIL":
		t.CrawlingStationDetail()
	// case "PRICE":
	// t.CrawlingStationPrice()
	case "CHARGING_GUN":
		t.CrawlingChargingGun()
	default:
		logger.Error(errors.New("[Task Error]: 不支持的爬虫类型"))
	}
	// fmt.Println("here")
	// // retry 逻辑
	// if t.message.NeedRetry {
	// 	t.message.NeedRetry = false
	// 	retryTask := NewEChargeCrawlingTask(t.originMessage, t.message, t.ch)
	// 	retryTask.Exec()
	// }
}

func (t *Task) Cancel() {
	t.cancel()
}

func NewEChargeCrawlingTask(stm task.StartTaskMessage, atmsg api.TaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	ctx, cancel := context.WithCancel(context.Background())
	return &Task{
		originMessage:   stm,
		message:         atmsg,
		ctx:             ctx,
		cancel:          cancel,
		ch:              ch,
		api:             api.New(CrawlTargetDesc, atmsg),
		chStationList:   make(chan StationListApiResp),
		chStationDetail: make(chan StationDetailApiResp),
		chStationPrice:  make(chan StationPriceApiResp),
		chChargingGun:   make(chan ChargingGunApiResp),
		existStations:   make(map[string]bool),
	}
}

func executor(stm task.StartTaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	msg := api.TaskMessage{}
	err := msg.Marshal(stm)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("[Task Error]: 解析任务参数错误 <%s>, %+v", err.Error(), stm)))
	}

	return NewEChargeCrawlingTask(stm, msg, ch)
}

func init() {
	task.SEMapping.Register(CrawlTarget, executor)
}
