package api

import (
	"encoding/json"

	"github.com/riete/errors"

	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/util"
)

type ECZApi interface {
	GetStationList(param Param) ([]map[string]any, errors.Error)
	GetStationDetail(param Param) ([]map[string]any, errors.Error)
	// GetStationPrice(param Param, gunId, chargeType string) ([]map[string]any, errors.Error)
	GetChargingGunList(param Param) ([]map[string]any, errors.Error)
}

type Api struct {
	targetDesc string
	credential Credential
	gParam     GlobalParam
}

type Credential struct{}

type GlobalParam struct {
	TaskId     string `json:"taskId"`
	TemplateId string `json:"templateId"`
	Channel    string `json:"channel"`
	ScriptUrl  string `json:"scriptUrl"`
	BizType    string `json:"bizType"`
	Province   string `json:"province"`
	City       string `json:"city"`
}

func (gp GlobalParam) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(gp)
}

type Param struct {
	StationID   string `json:"station_id,omitempty"`
	StationName string `json:"station_name,omitempty"`
	CITY        string `json:"CITY,omitempty"`
	City        string `json:"city,omitempty"`
	CityCode    string `json:"CITY_CODE,omitempty"`
	Lat         string `json:"LAT,omitempty"`
	Lgn         string `json:"LGN,omitempty"`
	Latitude    string `json:"lat,omitempty"`
	Longitude   string `json:"lon,omitempty"`
	FastGunId   string `json:"fast_gun_id,omitempty"`
	SlowGunId   string `json:"slow_gun_id,omitempty"`
}

func (p Param) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(p)
}

type TaskMessage struct {
	TaskInstanceId string      `json:"taskInstanceId"`
	Target         string      `json:"target"`
	Credential     Credential  `json:"accountInfo"`
	GlobalParam    GlobalParam `json:"globalParam"`
	Params         []Param     `json:"params"`
	NeedRetry      bool        `json:"needRetry"`
}

func (msg *TaskMessage) Marshal(stmsg task.StartTaskMessage) error {
	s, err := json.Marshal(stmsg)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(s, msg); err != nil {
		return err
	}
	return nil
}

func New(targetDesc string, tm TaskMessage) Api {
	return Api{
		targetDesc: targetDesc,
		credential: tm.Credential,
		gParam:     tm.GlobalParam,
	}
}
