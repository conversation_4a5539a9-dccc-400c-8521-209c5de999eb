package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/riete/errors"

	"tianyan-crawler/internal/common/request"
	"tianyan-crawler/internal/source/ecz/api/common"
)

const (
	StationListURL = "https://wxapp.e-chong.com/map/station/search"
)

type StationListData struct {
	StationId   int64  `json:"chargingStationId"`
	StationName string `json:"stationName"`
	Latitude    string `json:"latitude"`
	Longitude   string `json:"longitude"`
}

type StationListResp struct {
	Code int               `json:"code"`
	Data []StationListData `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)

	for _, d := range slr.Data {
		ac = append(ac, map[string]any{
			"channel":     channel,
			"city":        city,
			"stationId":   strconv.FormatInt(d.StationId, 10),
			"stationName": d.Station<PERSON>ame,
			"lat":         d.Latitude,
			"lon":         d.Longitude,
			"runTime":     ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p Param) ([]map[string]any, errors.Error) {
	body := map[string]any{
		"latitude":  p.Lat,
		"longitude": p.Lgn,
		"pageNum":   "1",
		"pageSize":  "200",
		"stationConditionList": []map[string]string{
			{"dictDesc": "位置刷选", "dictValue": "150km"},
		},
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化站点列表 Body 失败")
	}

	deviceId := uuid.New().String()
	headers := map[string]string{
		"PLAT_TYPE":      "wx",
		"PLAT_INFO":      "3.8.7",
		"REQ_ID":         strconv.Itoa(int(time.Now().UnixMilli())),
		"APP_VER":        "2.0.1",
		"USER_DEVICE_ID": deviceId,
		"CURRENT_CITY":   url.QueryEscape(p.CITY),
		"USER_TOKEN":     "",
	}
	r, err := common.DoRequest(StationListURL, jsonBody, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := StationListResp{}
	_ = json.Unmarshal(r.Content(), &sr)
	if sr.Code != 200 {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.targetDesc, p.CITY), nil
}
