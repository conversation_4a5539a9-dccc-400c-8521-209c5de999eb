package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/riete/errors"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/ecz/api/common"
)

const (
	ChargingGunURL = "https://wxapp.e-chong.com/map/station/pile/list"
)

type ChargingGunData struct {
	GunId         string `json:"pileCode"`
	IsQuickCharge int    `json:"isQuickCharge"`
	Power         string `json:"maxPower"`
	RatedCurrent  string `json:"maxCurrent"`
}

type ChargingGunResp struct {
	Code int               `json:"code"`
	Data []ChargingGunData `json:"data"`
}

func (cgr *ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	r := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, d := range cgr.Data {
		gunType := ""
		if d.IsQuickCharge == 0 {
			gunType = "交流"
		} else if d.IsQuickCharge == 1 {
			gunType = "直流"
		}

		r = append(r, map[string]any{
			"channel":      targetDesc,
			"id":           rd,
			"jobId":        rd,
			"stationId":    stationId,
			"stationName":  stationName,
			"city":         city,
			"gunId":        d.GunId,
			"gunType":      gunType,
			"power":        d.Power,
			"ratedCurrent": d.RatedCurrent,
			"runTime":      ct,
		})
	}

	return r, nil
}

func (a Api) GetChargingGunList(p Param) ([]map[string]any, errors.Error) {
	body := map[string]string{
		"stationId": p.StationID,
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化充电枪列表 Body 失败")
	}

	deviceId := uuid.New().String()
	headers := map[string]string{
		"PLAT_TYPE":      "wx",
		"PLAT_INFO":      "3.8.7",
		"REQ_ID":         strconv.Itoa(int(time.Now().UnixMilli())),
		"APP_VER":        "2.0.1",
		"USER_DEVICE_ID": deviceId,
		"CURRENT_CITY":   url.QueryEscape(p.City),
		"USER_TOKEN":     "",
	}
	r, err := common.DoRequest(ChargingGunURL, jsonBody, 6, request.WithHeader(headers))
	if err != nil {
		return []map[string]any{}, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return []map[string]any{}, errors.New(status).Trace(fmt.Sprintf("获取站点充电枪数据失败, http响应: %s", status))
	}

	cgr := ChargingGunResp{}
	_ = json.Unmarshal(r.Content(), &cgr)
	if cgr.Code != 200 {
		return []map[string]any{}, errors.New(fmt.Sprintf("获取站点充电枪数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}

	return result, nil
}
