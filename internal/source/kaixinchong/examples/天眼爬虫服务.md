---
title: 天眼爬虫服务
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 天眼爬虫服务

Base URLs:

# Authentication

# 开鑫充电

## POST 站点列表

POST /cminiproject/station/page/list

> Body 请求参数

```json
{
  "pageNo": 2,
  "pageSize": 10,
  "sortType": 1,
  "centerLng": 120.36434173583984,
  "centerLat": 31.490549087524414
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|env|header|string| 是 |none|
|xweb_xhr|header|string| 是 |none|
|content-type|header|string| 是 |none|
|loginmerchantid|header|string| 是 |none|
|isoperator|header|string| 是 |none|
|priority|header|string| 是 |none|
|body|body|object| 否 |none|
|» pageNo|body|integer| 是 |none|
|» pageSize|body|integer| 是 |none|
|» sortType|body|integer| 是 |none|
|» centerLng|body|number| 是 |none|
|» centerLat|body|number| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": "10000",
  "title": "提示",
  "msg": "OK",
  "data": {
    "pageObject": [
      {
        "fullStationId": "MAD4EBRC0MAC671GU2_74",
        "stationId": 5601,
        "merchantId": 27,
        "merchantName": null,
        "stationName": "【i玖充电】朱家角全季酒店站",
        "detailAddress": "上海市青浦区朱家角镇全季酒店(上海青浦朱家角店)",
        "cityId": "4",
        "stationType": 1,
        "parkingFeeType": 0,
        "parkingFeeDesc": "车辆充电时长不超过2小时的，充电期间免收占位费用（24小时之内仅限减免一次），超过2小时部分按场地方标准收费（需在启动充电前在小程序绑定保存车牌号）。",
        "longitude": "121.05429",
        "latitude": "31.103884",
        "gunInfo": {
          "gunNum": 4,
          "idleGunNum": 2,
          "fastGunNum": 4,
          "fastIdleGunNum": 2,
          "slowGunNum": 0,
          "slowIdleGunNum": 0
        },
        "businessHours": "24小时",
        "stationService": null,
        "currentPrice": "1.2500",
        "priceLabel": 0,
        "vipCurrentPrice": null,
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 4,
        "isSuperPowerStation": false,
        "maxPower": 120,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 78.3963615748904
      },
      {
        "fullStationId": "MA1MDQAJX_6008",
        "stationId": 6008,
        "merchantId": 30,
        "merchantName": null,
        "stationName": "【开鑫】太仓宝洁园区慢充电桩",
        "detailAddress": "江苏省苏州市太仓市太仓宝洁分销中心(东北门)",
        "cityId": "23",
        "stationType": 255,
        "parkingFeeType": 3,
        "parkingFeeDesc": "",
        "longitude": "121.180461",
        "latitude": "31.621885",
        "gunInfo": {
          "gunNum": 6,
          "idleGunNum": 4,
          "fastGunNum": 0,
          "fastIdleGunNum": 0,
          "slowGunNum": 6,
          "slowIdleGunNum": 4
        },
        "businessHours": "{\"start\":\"00:00\",\"end\":\"23:59\"}",
        "stationService": [
          {
            "labelId": 3,
            "labelName": "雨棚",
            "labelIcon": "https://view.didistatic.com/static/dcms/gh2h10jlxlw4yaxqe_144x144.png",
            "labelOrder": 7,
            "sort": 6
          }
        ],
        "currentPrice": "2.0000",
        "priceLabel": 0,
        "vipCurrentPrice": null,
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 3,
        "isSuperPowerStation": false,
        "maxPower": 7,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 78.69580995846488
      },
      {
        "fullStationId": "MAD4EBRC0MA1MY0GF9_99323",
        "stationId": 148,
        "merchantId": 20,
        "merchantName": null,
        "stationName": "香花桥镇北青公路充电站",
        "detailAddress": "上海市青浦区北青公路8865号近香花桥东路（交警四中队交通安全教育学校正对面）",
        "cityId": "4",
        "stationType": 1,
        "parkingFeeType": 0,
        "parkingFeeDesc": "充满电请及时驶离休息，便于其它车辆充",
        "longitude": "121.13979",
        "latitude": "31.185166",
        "gunInfo": {
          "gunNum": 20,
          "idleGunNum": 12,
          "fastGunNum": 20,
          "fastIdleGunNum": 12,
          "slowGunNum": 0,
          "slowIdleGunNum": 0
        },
        "businessHours": "全天开放",
        "stationService": null,
        "currentPrice": "1.3100",
        "priceLabel": 0,
        "vipCurrentPrice": "1.2880",
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 4,
        "isSuperPowerStation": false,
        "maxPower": 120,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 81.09805410314574
      },
      {
        "fullStationId": "MAD4EBRC0MAC671GU2_13",
        "stationId": 5583,
        "merchantId": 26,
        "merchantName": null,
        "stationName": "【i玖充电】叶城路站",
        "detailAddress": "上海上海市嘉定区叶城路565号",
        "cityId": "4",
        "stationType": 1,
        "parkingFeeType": 0,
        "parkingFeeDesc": "7元/小时",
        "longitude": "121.258651",
        "latitude": "31.366985",
        "gunInfo": {
          "gunNum": 29,
          "idleGunNum": 22,
          "fastGunNum": 4,
          "fastIdleGunNum": 3,
          "slowGunNum": 25,
          "slowIdleGunNum": 19
        },
        "businessHours": "全天24小时",
        "stationService": null,
        "currentPrice": "1.5000",
        "priceLabel": 0,
        "vipCurrentPrice": null,
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 4,
        "isSuperPowerStation": false,
        "maxPower": 120,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 85.95825522406216
      },
      {
        "fullStationId": "MAD4EBRC0MAC671GU2_133",
        "stationId": 5593,
        "merchantId": 27,
        "merchantName": null,
        "stationName": "【i玖充电】青浦东庄村站",
        "detailAddress": "上海市青浦区练塘镇村民大礼堂",
        "cityId": "4",
        "stationType": 1,
        "parkingFeeType": 0,
        "parkingFeeDesc": "停车免费",
        "longitude": "121.063428",
        "latitude": "30.977647",
        "gunInfo": {
          "gunNum": 2,
          "idleGunNum": 2,
          "fastGunNum": 2,
          "fastIdleGunNum": 2,
          "slowGunNum": 0,
          "slowIdleGunNum": 0
        },
        "businessHours": "24小时",
        "stationService": null,
        "currentPrice": "1.3500",
        "priceLabel": 0,
        "vipCurrentPrice": null,
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 4,
        "isSuperPowerStation": false,
        "maxPower": 120,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 87.5815672462847
      },
      {
        "fullStationId": "MAD4EBRC0MA1MY0GF9_97580",
        "stationId": 79,
        "merchantId": 20,
        "merchantName": null,
        "stationName": "闵行区纪王莱尚充电站",
        "detailAddress": "上海市闵行区纪王街道(镇)纪翟路2791号",
        "cityId": "4",
        "stationType": 1,
        "parkingFeeType": 0,
        "parkingFeeDesc": "免费2小时",
        "longitude": "121.268695",
        "latitude": "31.244294",
        "gunInfo": {
          "gunNum": 14,
          "idleGunNum": 6,
          "fastGunNum": 10,
          "fastIdleGunNum": 5,
          "slowGunNum": 4,
          "slowIdleGunNum": 1
        },
        "businessHours": "全天开放",
        "stationService": null,
        "currentPrice": "1.4500",
        "priceLabel": 0,
        "vipCurrentPrice": "1.4425",
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 4,
        "isSuperPowerStation": true,
        "maxPower": 480,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 90.12258230758744
      },
      {
        "fullStationId": "MAD4EBRC0_4895",
        "stationId": 4895,
        "merchantId": 1,
        "merchantName": null,
        "stationName": "【开鑫】上海嘉定宝安公路2815号充电站",
        "detailAddress": "上海市嘉定区上海世纪模具有限公司",
        "cityId": "4",
        "stationType": 1,
        "parkingFeeType": 2,
        "parkingFeeDesc": "入场充电车辆，可享受停车减免2小时，提前绑定车牌，免费停车2小时，超出时间按照停车场收费标准收费。",
        "longitude": "121.30454",
        "latitude": "31.338062",
        "gunInfo": {
          "gunNum": 16,
          "idleGunNum": 13,
          "fastGunNum": 16,
          "fastIdleGunNum": 13,
          "slowGunNum": 0,
          "slowIdleGunNum": 0
        },
        "businessHours": "{\"start\":\"00:00\",\"end\":\"23:59\"}",
        "stationService": [
          {
            "labelId": 1,
            "labelName": "卫生间",
            "labelIcon": "https://view.didistatic.com/static/dcms/gh2hwb6slw4yafye_144x144.png",
            "labelOrder": 2,
            "sort": 1
          }
        ],
        "currentPrice": "1.3500",
        "priceLabel": 0,
        "vipCurrentPrice": "1.2246",
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 1,
        "isSuperPowerStation": true,
        "maxPower": 250,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 90.8176490865809
      },
      {
        "fullStationId": "MAD4EBRC0MA1MY0GF9_145898",
        "stationId": 83,
        "merchantId": 20,
        "merchantName": null,
        "stationName": "澄光翠云馆军民融合充电站",
        "detailAddress": "上海市闵行区华漕镇纪友路98号",
        "cityId": "4",
        "stationType": 1,
        "parkingFeeType": 0,
        "parkingFeeDesc": "超出时间按0.3元/分钟收取占位费",
        "longitude": "121.28435",
        "latitude": "31.23842",
        "gunInfo": {
          "gunNum": 6,
          "idleGunNum": 3,
          "fastGunNum": 6,
          "fastIdleGunNum": 3,
          "slowGunNum": 0,
          "slowIdleGunNum": 0
        },
        "businessHours": "全天开放",
        "stationService": null,
        "currentPrice": "0.9888",
        "priceLabel": 0,
        "vipCurrentPrice": "0.9588",
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 4,
        "isSuperPowerStation": false,
        "maxPower": 120,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 91.73984387382392
      },
      {
        "fullStationId": "MAD4EBRC0_117",
        "stationId": 117,
        "merchantId": 1,
        "merchantName": null,
        "stationName": "【开鑫】上海银涛高尔夫球场充电场内部站",
        "detailAddress": "上海市青浦区上海银涛高尔夫俱乐部",
        "cityId": "4",
        "stationType": 50,
        "parkingFeeType": 3,
        "parkingFeeDesc": "",
        "longitude": "121.253412",
        "latitude": "31.162778",
        "gunInfo": {
          "gunNum": 8,
          "idleGunNum": 4,
          "fastGunNum": 0,
          "fastIdleGunNum": 0,
          "slowGunNum": 8,
          "slowIdleGunNum": 4
        },
        "businessHours": "{\"start\":\"00:00\",\"end\":\"23:59\"}",
        "stationService": null,
        "currentPrice": "1.5000",
        "priceLabel": 0,
        "vipCurrentPrice": null,
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 1,
        "isSuperPowerStation": false,
        "maxPower": 7,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 91.97677543715025
      },
      {
        "fullStationId": "MAD4EBRC0MAC671GU2_42",
        "stationId": 5602,
        "merchantId": 27,
        "merchantName": null,
        "stationName": "【i玖充电】北青公路站",
        "detailAddress": "上海市闵行区北青公路1068号（地面车位和地库B1层都有快充车位）",
        "cityId": "4",
        "stationType": 1,
        "parkingFeeType": 0,
        "parkingFeeDesc": "车辆充电时长不超过2小时的，充电期间免收占位费用（24小时之内仅限减免一次），超过2小时部分按场地方标准收费（需在启动充电前在小程序绑定保存车牌号），交流电桩充电订单不享受减免停车费。",
        "longitude": "121.286282",
        "latitude": "31.212225",
        "gunInfo": {
          "gunNum": 48,
          "idleGunNum": 40,
          "fastGunNum": 44,
          "fastIdleGunNum": 37,
          "slowGunNum": 4,
          "slowIdleGunNum": 3
        },
        "businessHours": "全天24小时",
        "stationService": null,
        "currentPrice": "1.3500",
        "priceLabel": 0,
        "vipCurrentPrice": null,
        "activityTags": null,
        "status": 1,
        "stationStatus": 2,
        "supportOccupyFee": false,
        "isDisplayGroundLock": false,
        "location": 1,
        "unitOccupyPricePerMinute": null,
        "occupyCappedPrice": null,
        "freeOccupyDuration": null,
        "occupyPriceDetail": null,
        "stationTypeLabel": 4,
        "isSuperPowerStation": false,
        "maxPower": 120,
        "userCouponList": null,
        "activityInfos": null,
        "historyCoupon": null,
        "directDistance": 92.85587364281467
      }
    ],
    "pageIndex": 2,
    "pageSize": 10,
    "totalPage": 25,
    "totalCount": 247,
    "hasNext": false
  },
  "traceId": "1ae9ee72a5034bf3b298ad9301a387e6",
  "status": 0,
  "success": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none||none|
|» title|string|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» pageObject|[object]|true|none||none|
|»»» fullStationId|string|true|none||none|
|»»» stationId|integer|true|none||none|
|»»» merchantId|integer|true|none||none|
|»»» merchantName|null|true|none||none|
|»»» stationName|string|true|none||none|
|»»» detailAddress|string|true|none||none|
|»»» cityId|string|true|none||none|
|»»» stationType|integer|true|none||none|
|»»» parkingFeeType|integer|true|none||none|
|»»» parkingFeeDesc|string|true|none||none|
|»»» longitude|string|true|none||none|
|»»» latitude|string|true|none||none|
|»»» gunInfo|object|true|none||none|
|»»»» gunNum|integer|true|none||none|
|»»»» idleGunNum|integer|true|none||none|
|»»»» fastGunNum|integer|true|none||none|
|»»»» fastIdleGunNum|integer|true|none||none|
|»»»» slowGunNum|integer|true|none||none|
|»»»» slowIdleGunNum|integer|true|none||none|
|»»» businessHours|string|true|none||none|
|»»» stationService|[object]¦null|true|none||none|
|»»»» labelId|integer|true|none||none|
|»»»» labelName|string|true|none||none|
|»»»» labelIcon|string|true|none||none|
|»»»» labelOrder|integer|true|none||none|
|»»»» sort|integer|true|none||none|
|»»» currentPrice|string|true|none||none|
|»»» priceLabel|integer|true|none||none|
|»»» vipCurrentPrice|string¦null|true|none||none|
|»»» activityTags|null|true|none||none|
|»»» status|integer|true|none||none|
|»»» stationStatus|integer|true|none||none|
|»»» supportOccupyFee|boolean|true|none||none|
|»»» isDisplayGroundLock|boolean|true|none||none|
|»»» location|integer|true|none||none|
|»»» unitOccupyPricePerMinute|null|true|none||none|
|»»» occupyCappedPrice|null|true|none||none|
|»»» freeOccupyDuration|null|true|none||none|
|»»» occupyPriceDetail|null|true|none||none|
|»»» stationTypeLabel|integer|true|none||none|
|»»» isSuperPowerStation|boolean|true|none||none|
|»»» maxPower|integer|true|none||none|
|»»» userCouponList|null|true|none||none|
|»»» activityInfos|null|true|none||none|
|»»» historyCoupon|null|true|none||none|
|»»» directDistance|number|true|none||none|
|»» pageIndex|integer|true|none||none|
|»» pageSize|integer|true|none||none|
|»» totalPage|integer|true|none||none|
|»» totalCount|integer|true|none||none|
|»» hasNext|boolean|true|none||none|
|» traceId|string|true|none||none|
|» status|integer|true|none||none|
|» success|boolean|true|none||none|

## POST 站点详情_包含价格

POST /cminiproject/station/detail

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|fullStationId|query|string| 是 |none|
|env|header|string| 是 |none|
|xweb_xhr|header|string| 是 |none|
|content-type|header|string| 是 |none|
|loginmerchantid|header|string| 是 |none|
|isoperator|header|string| 是 |none|
|priority|header|string| 是 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": "10000",
  "title": "提示",
  "msg": "OK",
  "data": {
    "fullStationId": "MAD4EBRC0MAC671GU2_42",
    "stationName": "【i玖充电】北青公路站",
    "status": 1,
    "businessStatus": 1,
    "merchantName": "i 玖充电-瑜珩（互联接入）",
    "phone": "4001600259",
    "detailAddress": "上海市闵行区北青公路1068号（地面车位和地库B1层都有快充车位）",
    "parkingFeeType": 0,
    "parkingFeeDesc": "车辆充电时长不超过2小时的，充电期间免收占位费用（24小时之内仅限减免一次），超过2小时部分按场地方标准收费（需在启动充电前在小程序绑定保存车牌号），交流电桩充电订单不享受减免停车费。",
    "location": 1,
    "longitude": "121.286282",
    "latitude": "31.212225",
    "gunInfo": {
      "gunNum": 48,
      "idleGunNum": 40,
      "fastGunNum": 44,
      "fastIdleGunNum": 37,
      "slowGunNum": 4,
      "slowIdleGunNum": 3
    },
    "businessHours": "全天24小时",
    "stationService": null,
    "currentPrice": "1.3500",
    "priceLabel": 0,
    "priceDetail": [
      {
        "startTime": "00:00",
        "endTime": "06:00",
        "elecLabel": "",
        "elecPrice": 0.3044,
        "servicePrice": 0.6456,
        "finalElecPrice": 0.3044,
        "finalServicePrice": 0.6456,
        "vipElecPrice": null,
        "vipServicePrice": null,
        "priceStrategyList": []
      },
      {
        "startTime": "06:00",
        "endTime": "08:00",
        "elecLabel": "",
        "elecPrice": 0.6718,
        "servicePrice": 0.4782,
        "finalElecPrice": 0.6718,
        "finalServicePrice": 0.4782,
        "vipElecPrice": null,
        "vipServicePrice": null,
        "priceStrategyList": []
      },
      {
        "startTime": "08:00",
        "endTime": "15:00",
        "elecLabel": "",
        "elecPrice": 1.2169,
        "servicePrice": 0.1331,
        "finalElecPrice": 1.2169,
        "finalServicePrice": 0.1331,
        "vipElecPrice": null,
        "vipServicePrice": null,
        "priceStrategyList": []
      },
      {
        "startTime": "15:00",
        "endTime": "18:00",
        "elecLabel": "",
        "elecPrice": 0.6718,
        "servicePrice": 0.4782,
        "finalElecPrice": 0.6718,
        "finalServicePrice": 0.4782,
        "vipElecPrice": null,
        "vipServicePrice": null,
        "priceStrategyList": []
      },
      {
        "startTime": "18:00",
        "endTime": "21:00",
        "elecLabel": "",
        "elecPrice": 1.2169,
        "servicePrice": 0.1331,
        "finalElecPrice": 1.2169,
        "finalServicePrice": 0.1331,
        "vipElecPrice": null,
        "vipServicePrice": null,
        "priceStrategyList": []
      },
      {
        "startTime": "21:00",
        "endTime": "22:00",
        "elecLabel": "",
        "elecPrice": 0.6718,
        "servicePrice": 0.4782,
        "finalElecPrice": 0.6718,
        "finalServicePrice": 0.4782,
        "vipElecPrice": null,
        "vipServicePrice": null,
        "priceStrategyList": []
      },
      {
        "startTime": "22:00",
        "endTime": "23:59",
        "elecLabel": "",
        "elecPrice": 0.3044,
        "servicePrice": 0.6456,
        "finalElecPrice": 0.3044,
        "finalServicePrice": 0.6456,
        "vipElecPrice": null,
        "vipServicePrice": null,
        "priceStrategyList": []
      }
    ],
    "activityDetail": null,
    "unitOccupyPricePerMinute": null,
    "occupyCappedPrice": null,
    "freeOccupyDuration": 20,
    "occupyPriceDetail": [],
    "supportOccupyFee": false,
    "isDisplayGroundLock": false,
    "equipmentDetailResponses": null,
    "pictures": [
      {
        "stationId": null,
        "tenantId": null,
        "customerOperatorId": null,
        "equipmentOperatorId": null,
        "picCategory": "0",
        "isBg": 0,
        "picUrl": "https://www.i9rht.com/imgs/1702276438647_Za3tTFju.jpg",
        "isRemoved": null,
        "createTime": null,
        "updateTime": null
      },
      {
        "stationId": null,
        "tenantId": null,
        "customerOperatorId": null,
        "equipmentOperatorId": null,
        "picCategory": "0",
        "isBg": 0,
        "picUrl": "https://www.i9rht.com/imgs/1734666432432_Frtofp3J.jpg",
        "isRemoved": null,
        "createTime": null,
        "updateTime": null
      },
      {
        "stationId": null,
        "tenantId": null,
        "customerOperatorId": null,
        "equipmentOperatorId": null,
        "picCategory": "0",
        "isBg": 0,
        "picUrl": "https://www.i9rht.com/imgs/1734666436376_YQ8T29Ce.jpg",
        "isRemoved": null,
        "createTime": null,
        "updateTime": null
      }
    ],
    "parkingStructInfo": {
      "fullStationId": "MAD4EBRC0MAC671GU2_42",
      "stationName": "【i玖充电】北青公路站",
      "operatorId": null,
      "operatorName": null,
      "freeType": "3",
      "freeTime": null,
      "finalWaiverNote": "车辆充电时长不超过2小时的，充电期间免收占位费用（24小时之内仅限减免一次），超过2小时部分按场地方标准收费（需在启动充电前在小程序绑定保存车牌号），交流电桩充电订单不享受减免停车费。",
      "withParkingSystem": null,
      "labelStatus": null,
      "note": null,
      "feeRule": null,
      "waiverType": null,
      "temporaryPlateWaiver": null,
      "addInfo": null,
      "duration": null,
      "threshold": null
    },
    "isSuperPowerStation": false,
    "maxPower": 120,
    "stationTypeLabel": 4,
    "minProductFee": 0.99,
    "userCouponList": null,
    "activityInfos": null,
    "historyCoupon": null,
    "stationFavoriteFlag": false
  },
  "traceId": "5fd689481144488ab450ece79f4606fd",
  "status": 0,
  "success": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none||none|
|» title|string|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» fullStationId|string|true|none||none|
|»» stationName|string|true|none||none|
|»» status|integer|true|none||none|
|»» businessStatus|integer|true|none||none|
|»» merchantName|string|true|none||none|
|»» phone|string|true|none||none|
|»» detailAddress|string|true|none||none|
|»» parkingFeeType|integer|true|none||none|
|»» parkingFeeDesc|string|true|none||none|
|»» location|integer|true|none||none|
|»» longitude|string|true|none||none|
|»» latitude|string|true|none||none|
|»» gunInfo|object|true|none||none|
|»»» gunNum|integer|true|none||none|
|»»» idleGunNum|integer|true|none||none|
|»»» fastGunNum|integer|true|none||none|
|»»» fastIdleGunNum|integer|true|none||none|
|»»» slowGunNum|integer|true|none||none|
|»»» slowIdleGunNum|integer|true|none||none|
|»» businessHours|string|true|none||none|
|»» stationService|null|true|none||none|
|»» currentPrice|string|true|none||none|
|»» priceLabel|integer|true|none||none|
|»» priceDetail|[object]|true|none||none|
|»»» startTime|string|true|none||none|
|»»» endTime|string|true|none||none|
|»»» elecLabel|string|true|none||none|
|»»» elecPrice|number|true|none||none|
|»»» servicePrice|number|true|none||none|
|»»» finalElecPrice|number|true|none||none|
|»»» finalServicePrice|number|true|none||none|
|»»» vipElecPrice|null|true|none||none|
|»»» vipServicePrice|null|true|none||none|
|»»» priceStrategyList|[any]|true|none||none|
|»» activityDetail|null|true|none||none|
|»» unitOccupyPricePerMinute|null|true|none||none|
|»» occupyCappedPrice|null|true|none||none|
|»» freeOccupyDuration|integer|true|none||none|
|»» occupyPriceDetail|[any]|true|none||none|
|»» supportOccupyFee|boolean|true|none||none|
|»» isDisplayGroundLock|boolean|true|none||none|
|»» equipmentDetailResponses|null|true|none||none|
|»» pictures|[object]|true|none||none|
|»»» stationId|null|true|none||none|
|»»» tenantId|null|true|none||none|
|»»» customerOperatorId|null|true|none||none|
|»»» equipmentOperatorId|null|true|none||none|
|»»» picCategory|string|true|none||none|
|»»» isBg|integer|true|none||none|
|»»» picUrl|string|true|none||none|
|»»» isRemoved|null|true|none||none|
|»»» createTime|null|true|none||none|
|»»» updateTime|null|true|none||none|
|»» parkingStructInfo|object|true|none||none|
|»»» fullStationId|string|true|none||none|
|»»» stationName|string|true|none||none|
|»»» operatorId|null|true|none||none|
|»»» operatorName|null|true|none||none|
|»»» freeType|string|true|none||none|
|»»» freeTime|null|true|none||none|
|»»» finalWaiverNote|string|true|none||none|
|»»» withParkingSystem|null|true|none||none|
|»»» labelStatus|null|true|none||none|
|»»» note|null|true|none||none|
|»»» feeRule|null|true|none||none|
|»»» waiverType|null|true|none||none|
|»»» temporaryPlateWaiver|null|true|none||none|
|»»» addInfo|null|true|none||none|
|»»» duration|null|true|none||none|
|»»» threshold|null|true|none||none|
|»» isSuperPowerStation|boolean|true|none||none|
|»» maxPower|integer|true|none||none|
|»» stationTypeLabel|integer|true|none||none|
|»» minProductFee|number|true|none||none|
|»» userCouponList|null|true|none||none|
|»» activityInfos|null|true|none||none|
|»» historyCoupon|null|true|none||none|
|»» stationFavoriteFlag|boolean|true|none||none|
|» traceId|string|true|none||none|
|» status|integer|true|none||none|
|» success|boolean|true|none||none|

## GET 充电枪列表

GET /cminiproject/station/equips

> Body 请求参数

```
string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|fullStationId|query|string| 是 |none|
|env|header|string| 是 |none|
|xweb_xhr|header|string| 是 |none|
|content-type|header|string| 是 |none|
|loginmerchantid|header|string| 是 |none|
|isoperator|header|string| 是 |none|
|priority|header|string| 是 |none|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": "10000",
  "title": "提示",
  "msg": "OK",
  "data": {
    "connectorNum": 48,
    "idleConnectorNum": 39,
    "fastConnectorNum": 44,
    "fastIdleConnectorNum": 36,
    "slowConnectorNum": 4,
    "slowIdleConnectorNum": 3,
    "connectorInfoList": [
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000122",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300012",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000121",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300012",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000112",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300011",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000111",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300011",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000102",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300010",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000101",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300010",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000092",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300009",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000091",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300009",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000081",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300008",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000072",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300007",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000071",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300007",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000062",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300006",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000061",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300006",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000052",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300005",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000051",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300005",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000042",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300004",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000032",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300003",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000011",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300001",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750102",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375010",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750101",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375010",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750092",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375009",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750091",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375009",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750082",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375008",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750081",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375008",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750072",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375007",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750071",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375007",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750062",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375006",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750061",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375006",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750052",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375005",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750042",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375004",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750041",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375004",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750032",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375003",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750022",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375002",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750021",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375002",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750012",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375001",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750011",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375001",
        "power": 120,
        "opConnectorStatus": 1,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180010003750141",
        "equipmentId": "MAD4EBRC0MAC671GU20118001000375014",
        "power": 7,
        "opConnectorStatus": 1,
        "connectorType": 2,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180010003750131",
        "equipmentId": "MAD4EBRC0MAC671GU20118001000375013",
        "power": 7,
        "opConnectorStatus": 1,
        "connectorType": 2,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180010003750111",
        "equipmentId": "MAD4EBRC0MAC671GU20118001000375011",
        "power": 7,
        "opConnectorStatus": 1,
        "connectorType": 2,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750031",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375003",
        "power": 120,
        "opConnectorStatus": 3,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000041",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300004",
        "power": 120,
        "opConnectorStatus": 3,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020003750051",
        "equipmentId": "MAD4EBRC0MAC671GU20118002000375005",
        "power": 120,
        "opConnectorStatus": 3,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000082",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300008",
        "power": 120,
        "opConnectorStatus": 3,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000021",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300002",
        "power": 120,
        "opConnectorStatus": 3,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180010003750121",
        "equipmentId": "MAD4EBRC0MAC671GU20118001000375012",
        "power": 7,
        "opConnectorStatus": 3,
        "connectorType": 2,
        "soc": null
      },
      {
        "connectorName": "1号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000031",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300003",
        "power": 120,
        "opConnectorStatus": 3,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000022",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300002",
        "power": 120,
        "opConnectorStatus": 2,
        "connectorType": 1,
        "soc": null
      },
      {
        "connectorName": "2号枪",
        "connectorId": "MAD4EBRC0MAC671GU201180020043000012",
        "equipmentId": "MAD4EBRC0MAC671GU20118002004300001",
        "power": 120,
        "opConnectorStatus": 2,
        "connectorType": 1,
        "soc": null
      }
    ]
  },
  "traceId": "7e64a8f855a24694b75876d91436b4ba",
  "status": 0,
  "success": true
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|true|none||none|
|» title|string|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» connectorNum|integer|true|none||none|
|»» idleConnectorNum|integer|true|none||none|
|»» fastConnectorNum|integer|true|none||none|
|»» fastIdleConnectorNum|integer|true|none||none|
|»» slowConnectorNum|integer|true|none||none|
|»» slowIdleConnectorNum|integer|true|none||none|
|»» connectorInfoList|[object]|true|none||none|
|»»» connectorName|string|true|none||none|
|»»» connectorId|string|true|none||none|
|»»» equipmentId|string|true|none||none|
|»»» power|integer|true|none||none|
|»»» opConnectorStatus|integer|true|none||none|
|»»» connectorType|integer|true|none||none|
|»»» soc|null|true|none||none|
|» traceId|string|true|none||none|
|» status|integer|true|none||none|
|» success|boolean|true|none||none|

# 数据模型

