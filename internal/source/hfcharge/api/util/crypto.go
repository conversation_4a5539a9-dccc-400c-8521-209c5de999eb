package util

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/logger"

	"github.com/riete/errors"
	"golang.org/x/text/encoding/charmap"
	"golang.org/x/text/encoding/simplifiedchinese"
)

type UVERPair struct {
	UTS  string `json:"UTS,omitempty"`
	UVER string `json:"UVER,omitempty"`
}

type ParamField struct {
	UVERPair
	Data string `json:"Data"`
}

const (
	// 刷新 token 使用
	REFRESH_TOKEN_KEY = "7fb498553e3c462988c3b9573692bd5f"
	REFRESH_TOKEN_IV  = "98d71fe589499967"
	// 业务接口使用
	BIZ_KEY = "ErYu78ijuVaM7Y0UqwvpO738uNC9ALF7"
	BIZ_IV  = "Ol9mqvZ6ijnytr7O"
)

var bizAes = AesBase64{key: []byte(BIZ_KEY), iv: []byte(BIZ_IV)}

type PriceListParams struct {
	StationID string `json:"stationID"`
}

type ApiRequestParams[T any] struct {
	originParams T
	TELDAppID    string `json:"TELDAppID"`
	XToken       string `json:"X-Token"`
	SSDI         string `json:"SSDI"`
	SCOI         string `json:"SCOI"`
	SCOL         string `json:"SCOL"`
	SRS          string `json:"SRS"`
	STS          string `json:"STS"`
	SVER         string `json:"SVER"`
	Param        string `json:"param"`
}

func (arp *ApiRequestParams[T]) GenerateParams() (*ApiRequestParams[T], error) {
	if err := arp.encrypt(); err != nil {
		return nil, err
	}

	return arp, nil
}

func (arp *ApiRequestParams[T]) generateUVERPair() (UVERPair, error) {
	pair := UVERPair{}

	UTS := arp.STS
	pair.UTS = UTS

	UVER, err := bizAes.Encrypt([]byte(UTS))
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("UVER 参数构造失败 %s", err)))
		return pair, err
	}
	pair.UVER = UVER
	// fmt.Println("pair", pair)
	return pair, err
}

func (arp *ApiRequestParams[T]) encrypt() error {
	// bizAes := AesBase64{key: []byte(BIZ_KEY), iv: []byte(BIZ_IV)}
	// UTS := strconv.Itoa(int(time.Now().UnixMilli())) + "uts"
	// UTS := "1702021346554" + "uts"
	uverPair, err := arp.generateUVERPair()
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("UVER 参数构造失败 %s", err)))
		return err
	}
	// fmt.Println("enc", uverPair)

	// 编码转化 utf8 -> latin1(ISO8859_1)
	uverGBK, err := simplifiedchinese.GBK.NewEncoder().Bytes([]byte(uverPair.UVER[:16]))
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("uverGBK 编码转化失败 %s", err)))
		return err
	}
	bizIV, err := charmap.ISO8859_1.NewDecoder().Bytes(uverGBK)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("bizIV 编码转化失败 %s", err)))
		return err
	}
	// fmt.Println(string(bizIV))

	utsGBK, err := simplifiedchinese.GBK.NewEncoder().Bytes([]byte(uverPair.UTS))
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("utsGBK 编码转化失败 %s", err)))
		return err
	}
	bizKey, err := charmap.ISO8859_1.NewDecoder().Bytes(utsGBK)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("bizKey 编码转化失败 %s", err)))
		return err
	}
	// fmt.Println(string(bizKey))

	jsonStr, err := json.Marshal(arp.originParams)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("jsonStr 序列化失败 %s", err)))
		return err
	}

	paramAes := AesBase64{key: bizKey, iv: bizIV}
	encryptParams, err := paramAes.Encrypt(jsonStr)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("encryptParams 加密失败 %s", err)))
		return err
	}
	// fmt.Println("encryptParams", encryptParams)

	result := ParamField{
		UVERPair: UVERPair{UTS: uverPair.UTS, UVER: uverPair.UVER[:16]},
		Data:     encryptParams,
	}
	resultJSONStr, err := json.Marshal(result)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("ParamField 序列化失败 %s", err)))
		return err
	}

	arp.Param = string(resultJSONStr)
	return nil
}

type DecryptResponse struct {
	Data string `json:"Data"`
	UTS  string `json:"UTS"`
	UVER string `json:"UVER"`
}

func DecryptData(d string) (string, error) {
	// 站点列表返回值解密
	// var oriText string = "wrowfIVrHSKPnRDL5ICuVVKmhKh90+Gz/18Btxy0mJi4OFjqFBoU7+q5KAULqAMOST+HcTQrm94Cct8frAf8dOoZnxPIo3kx+uG0BewQCT+LPHnAeDiEiYH/VpWnx0bJjaN84uPvPxDFKUm7gkhd+I1BVGScF/RIPDd6lVG37BIgEcPVuId0vdl71pFDfLEBi9Z9a1ezGj0ezHgwelP1KTbrwMn1GENSusPbsr5Dtlsmz6RO1XuDGovgS/qrWg+iwlRLId4tCZHxbRCCd/G4N2pREtcbYkpZB0rX5zhUUhhU97bQawvZbMTFH12GIalE2XCvm4Qwr4MyqAccCMLiMT0uLyEibXIUwUUfe/dfhcCnwzRK3eMHbEOVtAdliTCYXlGbWQi56qQYiDIeHOXfqUt3fOmLjbb/bndEGrvqsImcXhR1pLzSSkf776063lhtG+bzcDG9xPWFz10P9ZtfuDzCg+SGiByVvwsdsIsvB4Rtojmf3lVmOpLdfJNjU/ifMO0BYtXQ3bPCSgA2DERszr9QvzLoZxVkt9Wi27JR2QT9RUjS7RoT9W8gNW1TlOBLwTrLORooHy5gd/rlQV/JvwGOrOwKnjPX93pHls0FWMZmmVLbXOpoS9tQXXECyLwDcYBFJ437CuUF+d0J8wDh154zGT6+b2sMkkxDe0a1MI5J6TKv/eR1Arw0EkAzoM/asdLaEzTTcSx+r+aMcAtAwF3rrKOH25DBx//6pQ1pjUdnu5AfEsNGq5U54mpYUqGwqnEwUrIQb4Ui6rkzmxpj5HOqCuwkMOGXtU9YbOkqCYsiYuURkWeccZuAm3c5c8SGAc7klOExU1FXHNPSknjE7cbW6ZkRh/e3K0uw5483Q+lRCT8sgpHb4Nl67wtQ0y7b75tEuikjlckvnefEGUy0XLdhZJAlT7CQ0y4gp+qYVyUhttj2LMkoudTUNVmhBlQ7Z3eJ+oq8WW5AoNQ8SRoTo8tLBg1JScAHDkqSfRDCZSeo6piwhJIbmKAfDMBkJF4c0S/6rft+WDH7oGdG71Eo7hD4yjXM0/zlSW08paWoYM2RT7k8Is185qz3Tjfui5q9rwjoy35HD7iK+S3Lc+c4MG7kskg/uq2Yx3bsD7x9jIZeKyq+PRI4Cbz4sV8yv/ZCqfEiOelbywFugGpPvdFYyES/uSz8PRiyCnW8F0grjCnEDXlXHOJm0qpMAKu6r4FW0oBFXbxE4gd6CId12C66BKiXOGvSWmXIGztoa4RadvHVI2LURFIgz/rZMl607BmUZ305pfJ+R/1lhjo7u1vXrqprqrsTS/so77cUFrPBS6cx+MPr+VI2hLWonEGAxgQax3lW8XhXPFYeMxt60KV8yABkrfuYhJ0LSjaH3whwLDls3qom0RoKKzLPQcbgOwcPPQTZbJv/ajUOKOuJ0x8DhkBNoyurUdHiRO+E/FthGfa3DrcjOaD7m8ymEJKRyrNdDyUMiu6Al3w43g0ogy8RRCQurbU15xZp7cHraTvzvVONBiQFsmuWrUG/DYL10yN73uLQSzIfRoLHxV901gQdczee4CVbc3K69/D+PlefmbIcdnlYuEvI30qc1mEVUuBmqyHsyZv0ynVqBa32gZG+RTy5L7izyGQ8AmQhxdhEYJre9aWbOxVD1fS+3hLa0070S/VtmA2+sN420NAEgGu4g/QGTDN/9Fuk/IxtwhJHgkPxLF5ZRR8KS6X4cKHR7UgzG9pfeXRSNThUU1SoCMIUOxTB8jCnym78wXfedGkK8SKEc4VzMtxn8MIj6FKxrHhuEAN5P5xWRd0oKhv1csP/o2VliPk/vfnWrz6TiXqFvcAPdbaeulZbaCKjg8MoWWhIcamzIcFu9FpEzWZPPj6DlUc2UxaPHB5ETrM5g57TgdDUGC06nux4p1PD6Fw1H1bOGU3EmZH8HlnFyGSquTSM2yBYCVYTPsZCfKS9cOD8b44tZgqwKcX+cPS0a6BclVwBkoEDUrOhxq8cs+LzHRT8MmYyZlcM/VvP4VaV/3c3dIjUNH0YGsgOPFaeow/nBXB3RJ8AUCMltel3ESXy76eMSZcVK8SooRF6seZS1EdK+i7dLKZeqO3pA2hRkBkmBB8bBuE801+WCx9qLSt74Dq5SG6UwTR0C+qmN7ixauovlQkysangFHj9+ZHZfX3JSYh870gl6lqJ6tAJTn9O2uZMruK5iXjeFDB+fmcNNQ5Ao+LD4bahsNDtbGmd0LWJJHNlBCXyQ2jdnyaPqhzxXQrqbhpscVJKINWA80VXxnDhYKhw3HU3ygGXb1VwRKsnezi6q7hZ81lq5Ah269/Qy73iBH6ADLjU7/Fkh6F3VxBnAEg4bb3qqpODec+OMAzkW9M032qf+tq/nsUaDUljfsqAClajYKOLIdJd/jpu/DI1zRs0LziQhUtaHO+NH5PaMBlxqKdiMTMLAvdfygtCtirfPO8Nd5xsn+XVUhDeIN1biaLSMOz/5y4k14JkLvOVHnntenPnecK8j6P12HXeKh5YpsKA5TwerNB83jBJzGnVZlZm7RHsTRFkuvAH3m5acrzzYm8tQ15sRHLjxmlJz/GRNlP5RRaW7yw7wCQq5lmj7wdNqV5Oa+fa5dD8JjPGxYL8tcM8z81W85SCd8hGv5y4gLOvGMCALVbqBtUucRT17bTl1My7A0Dn5l82dyJhYUkI9NP+PPhdhiA6kCvm8pjokGUg70m5eF1NXvbUo0/HIzj29DLd8yYJ3V7qrktNpjyhIac+5hFRNuVmX+aF1cVD2XJ7R619RPGiAjkxVZRQjEt3AvT/z1whjAYIzaKGlP28xZZZoQABLORchEI16bFGVYKnQ8vns3LCNGLVBmBrdodrh/YL1fNfSUwQAMzNSxcMa28aymH38X4c8yVAzYUaZlAlPYs7XAxNc1pGkLgy5PRNandt09uLDH6K8LsmvJxZzCRd2xtfIiWwV9zHYHNOrIJWhP41I1xqUxQtICeW7imBFyl8dPnACxMfzRZZ5QD3zJM3VNn9LCY0DpyNQNn7h5Ky3vlA3Xcp8vabPwhn0kj0W9ds55Rq2FP12HRx03NZR4RWWy7Rhh+IY5eM+pEiRPM8EJWxWFYojd01GOwl/eBSmL95yEMaJPtpDAbccKj73txJWFYtmq02fQNhP2ayNJVZjeV/I9deq7wO9U7cTgNXRYmgi4tU4xuh5pBzO+1He62vfXnqlLzq9xcRH/5sZvSevXThJEocmAoOqYLCC0I="
	decryptData, err := bizAes.Decrypt(d)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("decryptData 解密失败 %s", err)))
		return "", err
	}
	// fmt.Println(string(decryptData))

	dRes := DecryptResponse{}
	json.Unmarshal(decryptData, &dRes)
	// fmt.Println(dRes)

	resAes := AesBase64{key: []byte(dRes.UTS), iv: []byte(dRes.UVER)}
	actualRes, err := resAes.Decrypt(dRes.Data)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("actualRes 解密失败 %s", err)))
		return "", err
	}
	// fmt.Println(string(actualRes))

	return string(actualRes), nil
}

func GenerateSVER(ts int) string {
	var iv = []byte("t9TEPqji86aMVuUE")
	var key = []byte("IJL9qaZ7")

	aes := AesBase64{key: key, iv: iv}
	sver, err := aes.DesEncrypt([]byte(strconv.Itoa(ts)))
	if err != nil {
		logger.Error(errors.New("SVER 生成失败"))
	}

	return sver
}

func NewRequestParams[T any](originParams T, t string, d string) *ApiRequestParams[T] {
	// Generate STS & SVER
	ts := int(time.Now().Unix())
	// ts := 1702971298
	return &ApiRequestParams[T]{
		originParams: originParams,
		XToken:       t,
		SSDI:         d,
		STS:          strconv.Itoa(ts),
		SVER:         GenerateSVER(ts),
		SRS:          "SP",
	}
}
