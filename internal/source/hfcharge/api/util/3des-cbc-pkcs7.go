package util

import (
	"crypto/cipher"
	"crypto/des"
	"encoding/base64"
)

func (s AesBase64) DesEncrypt(text []byte) (string, error) {
	if len(text) == 0 {
		return "", nil
	}
	//生成cipher.Block 数据块
	// s.key = s.pad(s.key, 16)
	block, err := des.NewCipher(s.key)
	if err != nil {
		return "", err
	}
	//填充内容，如果不足16位字符
	blockSize := block.BlockSize()
	originData := s.pad(text, blockSize)
	//加密方式
	blockMode := cipher.NewCBCEncrypter(block, s.iv[:8])
	//加密，输出到[]byte数组
	crypted := make([]byte, len(originData))
	blockMode.CryptBlocks(crypted, originData)
	return base64.StdEncoding.EncodeToString(crypted), nil
}
