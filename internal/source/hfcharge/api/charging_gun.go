package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"time"

	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/hfcharge/api/common"
	"tianyan-crawler/internal/source/hfcharge/api/util"

	"github.com/riete/errors"
)

const (
	ChargingGunURL = "https://sgi.hfcdgs.com/api/invoke?SID=BaseApi-App0401_SGetTerminalOfStation"
	PowerRegExp    = `(\d+)`
	VoltageRegExp  = `(\d+)`
)

type ChargingGunParams struct {
	StationID string `json:"stationID"`
}

type ChargingGunData struct {
	GunId       string `json:"terminalCode"`
	GunType     string `json:"terminalType"`
	OriginPower string `json:"power"`
	Voltage     string `json:"voltage"`
	ParkingNo   string `json:"park"`
}

type ChargingGunResp struct {
	State   string `json:"state"`
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Data    string `json:"data"`
}

func (cgr ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	if cgr.Data == "" {
		return nil, errors.New(cgr.Data).Trace("响应结果 Data 为空")
	}
	r, err := util.DecryptData(cgr.Data)
	if err != nil {
		return nil, errors.New(cgr.Data).Trace(fmt.Sprintf("响应 Data 解密失败: %s", r))
	}

	cgd := []ChargingGunData{}
	json.Unmarshal([]byte(r), &cgd)

	guns := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgd {
		power := ""
		originPower := ""
		re := regexp.MustCompile(PowerRegExp)
		if gun.OriginPower != "" {
			matches := re.FindAllString(gun.OriginPower, -1)
			if len(matches) == 1 {
				power = matches[0]
				originPower = matches[0]
			} else if len(matches) >= 2 {
				power = matches[1]
				originPower = matches[0] + "-" + matches[1]
			} else {
				return guns, errors.New(fmt.Sprintf("解析充电枪功率字段失败，未找到匹配项: %s", gun.OriginPower))
			}
		}

		// terminalType : "交流单相7kW"
		if power == "" {
			matches := re.FindAllString(gun.GunType, -1)
			if len(matches) >= 1 {
				power = matches[0]
			}
			// terminalType : "交流单相" power留空
		}

		voltageLowerLimit := ""
		voltageUpperLimit := ""
		if gun.Voltage != "" {
			re := regexp.MustCompile(VoltageRegExp)
			matches := re.FindAllString(gun.Voltage, -1)
			if len(matches) == 1 {
				voltageLowerLimit = matches[0]
				voltageUpperLimit = matches[0]
			} else if len(matches) >= 2 {
				voltageLowerLimit = matches[0]
				voltageUpperLimit = matches[1]
			} else {
				return guns, errors.New(fmt.Sprintf("解析充电枪电压字段失败，未找到匹配项: %s", gun.Voltage))
			}
		}

		guns = append(guns, map[string]any{
			"channel":           targetDesc,
			"id":                rd,
			"jobId":             rd,
			"stationId":         stationId,
			"stationName":       stationName,
			"city":              city,
			"gunId":             gun.GunId,
			"gunType":           gun.GunType,
			"originPower":       originPower,
			"power":             power,
			"voltageLowerLimit": voltageLowerLimit,
			"voltageUpperLimit": voltageUpperLimit,
			"parkNo":            gun.ParkingNo,
			"runTime":           ct,
		})
	}

	return guns, nil
}

func (a *Api) GetChargingGunList(p Param) ([]map[string]any, errors.Error) {
	originParams := ChargingGunParams{
		StationID: p.StationID,
	}

	formData := util.NewRequestParams(originParams, a.accountInfo.Token, a.accountInfo.SSDI)

	reqParams, err := formData.GenerateParams()
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密失败")
	}

	reqJSON, err := json.Marshal(reqParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	slParams := make(map[string]string)
	json.Unmarshal([]byte(reqJSON), &slParams)

	r, err := common.DoRequest(ChargingGunURL, slParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	cgr := new(ChargingGunResp)
	_ = json.Unmarshal(r.Content(), cgr)

	// token 重试逻辑
	if cgr.State == "0" && cgr.ErrCode == TOKEN_INVALID_CODE {
		var refreshErr error
		a.mutex.Lock()
		times := 0
		for {
			if times < MAX_REFRESH_TOKEN_TIMES {
				newAccRes, err := task.RefreshAccountInfo(a.target, a.targetDesc)
				if err != nil {
					times++
					logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
					refreshErr = err
					continue
				}

				newAccountInfo := AccountInfo{}
				err = json.Unmarshal(newAccRes, &newAccountInfo)
				if err != nil {
					times++
					logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
					refreshErr = err
					continue
				}
				a.accountInfo = &newAccountInfo
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   a.taskInstanceId,
					RequestData: p,
					BizType:     a.gParam.BizType,
					Channel:     a.gParam.Channel,
					StackTrace:  cgr.ErrMsg,
					ErrorMsg:    fmt.Sprintf("用户凭证更新为: [SSDI: %s], [Token: %s]", a.accountInfo.SSDI, a.accountInfo.Token),
					Level:       logger.ERROR_LOG_MESSAGE_MAP.INFO.Type,
				})
				break
			} else {
				break
			}
		}
		a.mutex.Unlock()
		if refreshErr != nil {
			return nil, errors.New(fmt.Sprintf("重新获取 Token 失败 %s", refreshErr.Error()))
		}

		return a.GetChargingGunList(p)
	}

	if cgr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取充电枪列表数据失败, 对端返回: %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}
	return result, nil
}
