package api

import (
	"encoding/json"
	"sync"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/util"

	"github.com/riete/errors"
)

const (
	MAX_REFRESH_TOKEN_TIMES = 3
	TOKEN_INVALID_CODE      = "TTP-SG-1011"
)

type AccountInfo struct {
	Token string `json:"token"`
	SSDI  string `json:"ssdi"`
}

type GlobalParam struct {
	TemplateId string `json:"templateId"`
	Channel    string `json:"channel"`
	ScriptUrl  string `json:"scriptUrl"`
	BizType    string `json:"bizType"`
	Province   string `json:"province"`
	City       string `json:"city"`
}

func (gp GlobalParam) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(gp)
}

type Param struct {
	StationID   string `json:"station_id,omitempty"`
	StationName string `json:"station_name,omitempty"`
	CITY        string `json:"CITY,omitempty"`
	City        string `json:"city,omitempty"`
	CityCode    string `json:"CITY_CODE,omitempty"`
	Lat         string `json:"LAT,omitempty"`
	Lgn         string `json:"LGN,omitempty"`
	Latitude    string `json:"lat,omitempty"`
	Longitude   string `json:"lon,omitempty"`
}

func (p Param) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(p)
}

type TaskMessage struct {
	TaskInstanceId string      `json:"taskInstanceId"`
	Target         string      `json:"target"`
	AccountInfo    AccountInfo `json:"accountInfo"`
	GlobalParam    GlobalParam `json:"globalParam"`
	Params         []Param     `json:"params"`
}

func (msg *TaskMessage) Marshal(stmsg task.StartTaskMessage) error {
	s, err := json.Marshal(stmsg)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(s, msg); err != nil {
		return err
	}
	return nil
}

type HFChargeApi interface {
	GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error)
	GetStationDetail(p Param) ([]map[string]any, errors.Error)
	GetStationPrice(p Param) ([]map[string]any, errors.Error)
	GetChargingGunList(p Param) ([]map[string]any, errors.Error)
}

type Api struct {
	target         string
	targetDesc     string
	taskInstanceId string
	accountInfo    *AccountInfo
	gParam         GlobalParam
	mutex          sync.Mutex
}

func New(target, targetDesc, taskInstanceId string, tm *TaskMessage) *Api {
	return &Api{
		target:         target,
		targetDesc:     targetDesc,
		taskInstanceId: taskInstanceId,
		accountInfo:    &tm.AccountInfo,
		gParam:         tm.GlobalParam,
	}
}
