package common

import (
	"net/http"
	"time"

	"tianyan-crawler/internal/common/proxy"
	"tianyan-crawler/internal/common/request"
)

var commonHeader = map[string]string{
	"Accept":       "*/*",
	"Connection":   "keep-alive",
	"xweb_xhr":     "1",
	"TELDAppID":    "",
	"Content-Type": "application/x-www-form-urlencoded",
	"User-Agent":   "Mozilla/5.0 (Linux; Android 10; SEA-AL10 Build/HUAWEISEA-AL10; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/86.0.4240.99 XWEB/4313 MMWEBSDK/20220805 Mobile Safari/537.36 MMWEBID/9538 MicroMessenger/8.0.27.2220(0x28001B53) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm6",
}

func NewRequest(proxyIp proxy.ProxyIp, options ...request.Option) *request.Request {
	options = append(
		[]request.Option{
			request.WithDefaultClient(),
			request.WithTimeout(5 * time.Second),
			request.WithProxyFunc(http.ProxyURL(proxyIp.ProxyUrl())),
			request.WithHeader(commonHeader),
		},
		options...,
	)
	return request.NewRequest(options...)
}

func DoRequest(url string, data map[string]string, retry int, options ...request.Option) (*request.Request, error) {
	var r *request.Request
	var err error
	for i := 0; i < retry; i++ {
		r = NewRequest(proxy.GetIp(), options...)
		err = r.PostForm(url, data)
		if err != nil {
			time.Sleep(3 * time.Second)
		} else {
			break
		}
	}
	return r, err
}
