package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/hfcharge/api/common"
	"tianyan-crawler/internal/source/hfcharge/api/util"

	"github.com/riete/errors"
)

const (
	StationDetailURL = "https://sgi.hfcdgs.com/api/invoke?SID=BaseApi-App0401_SGetStationDetails"
)

type StationDetailParams struct {
	StationID      string  `json:"stationID"`
	CoordinateType string  `json:"coordinateType"`
	Lat            float64 `json:"lat"`
	Lng            float64 `json:"lng"`
	// Source         string   `json:"source"`
	// TagInfo        []string `json:"tagInfo"`
}

type StationDetailData struct {
	StationId    string  `json:"stationId"`
	StationName  string  `json:"stationName"`
	StationAddr  string  `json:"stationAddress"`
	OperatorName string  `json:"operatorName"`
	OperatorTel  string  `json:"operatorPhone"`
	SiteOperator string  `json:"sceneManageName"`
	FastCharge   int     `json:"fastTerminalNum"`
	SlowCharge   int     `json:"slowTerminalNum"`
	Lon          float64 `json:"lng"`
	Lat          float64 `json:"lat"`
	BusinessTime string  `json:"businessHours"`
	ParkFeeDesc  string  `json:"parkFee"`
	TagsNew      []struct {
		TagLabel string `json:"tagLabel"`
	} `json:"tagsNew"`
}

type StationDetailResp struct {
	State   string `json:"state"`
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Data    string `json:"data"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) ([]map[string]any, error) {
	if sdr.Data == "" {
		return nil, errors.New(sdr.Data).Trace("响应结果 Data 为空")
	}
	r, err := util.DecryptData(sdr.Data)
	if err != nil {
		return nil, errors.New(sdr.Data).Trace(fmt.Sprintf("响应 Data 解密失败: %s", r))
	}

	sdd := StationDetailData{}
	json.Unmarshal([]byte(r), &sdd)

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	stationLocation := "地上"
	openRemark := "不对外开放"
	operatorType := ""
	tips := []string{}
	for _, tag := range sdd.TagsNew {
		if tag.TagLabel != "" {
			if tag.TagLabel == "非露天" {
				stationLocation = "地下"
			}
			if tag.TagLabel == "对外开放" {
				openRemark = "对外开放"
			}
			if tag.TagLabel == "非自营" {
				operatorType = "非自营"
			} else if tag.TagLabel == "自营" {
				operatorType = "自营"
			} else if tag.TagLabel == "互联" {
				operatorType = "互联"
			}
			tips = append(tips, tag.TagLabel)
		}
	}

	am := []map[string]any{
		{
			"id":              rd,
			"jobId":           rd,
			"channel":         channel,
			"city":            city,
			"province":        province,
			"stationId":       sdd.StationId,
			"stationName":     sdd.StationName,
			"stationAddr":     sdd.StationAddr,
			"operatorName":    sdd.OperatorName,
			"operatorTel":     sdd.OperatorTel,
			"operatorType":    operatorType,
			"siteOperator":    sdd.SiteOperator,
			"fastCharge":      sdd.FastCharge,
			"slowCharge":      sdd.SlowCharge,
			"lon":             sdd.Lon,
			"lat":             sdd.Lat,
			"stationLocation": stationLocation, // 标识地上地下
			"businessTime":    sdd.BusinessTime,
			"parkFeeDesc":     sdd.ParkFeeDesc,
			"tips":            strings.Join(tips, ","),
			"openRemark":      openRemark,
			"runTime":         ct,
		},
	}
	return am, nil
}

func (a *Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	lt, _ := strconv.ParseFloat(p.Latitude, 64)
	lg, _ := strconv.ParseFloat(p.Longitude, 64)

	originParams := StationDetailParams{
		StationID:      p.StationID,
		CoordinateType: "gaode",
		Lat:            lt,
		Lng:            lg,
	}

	formData := util.NewRequestParams(originParams, a.accountInfo.Token, a.accountInfo.SSDI)

	reqParams, err := formData.GenerateParams()
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密失败")
	}

	reqJSON, err := json.Marshal(reqParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	slParams := make(map[string]string)
	json.Unmarshal([]byte(reqJSON), &slParams)

	r, err := common.DoRequest(StationDetailURL, slParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	json.Unmarshal(r.Content(), &sdr)

	// token 重试逻辑
	if sdr.State == "0" && sdr.ErrCode == TOKEN_INVALID_CODE {
		var refreshErr error
		a.mutex.Lock()
		times := 0
		for {
			if times < MAX_REFRESH_TOKEN_TIMES {
				newAccRes, err := task.RefreshAccountInfo(a.target, a.targetDesc)
				if err != nil {
					times++
					logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
					refreshErr = err
					continue
				}

				newAccountInfo := AccountInfo{}
				err = json.Unmarshal(newAccRes, &newAccountInfo)
				if err != nil {
					times++
					logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
					refreshErr = err
					continue
				}
				a.accountInfo = &newAccountInfo
				break
			} else {
				break
			}
		}
		a.mutex.Unlock()
		if refreshErr != nil {
			return nil, errors.New(fmt.Sprintf("重新获取 Token 失败 %s", refreshErr.Error()))
		}

		return a.GetStationDetail(p)
	}

	if sdr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString()))
	}

	result, err := sdr.convert(a.targetDesc, a.gParam.Province, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点详情数据转换失败, 对端返回 %s", r.ContentToString()))
	}
	return result, nil
}
