package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/source/hfcharge/api/common"
	"tianyan-crawler/internal/source/hfcharge/api/util"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://sgi.hfcdgs.com/api/invoke?SID=BaseApi-App0401_SSearchStation"
)

type StationListParams struct {
	CityCode            string  `json:"citycode"`
	CoordinateType      string  `json:"coordinateType"`
	KeyWord             string  `json:"keyword"`
	Lat                 float64 `json:"lat"`
	Latitude            float64 `json:"latitude"`
	Lng                 float64 `json:"lng"`
	Longitude           float64 `json:"longitude"`
	LocationFilterType  string  `json:"locationFilterType"`
	LocationFilterValue int     `json:"locationFilterValue"`
	PageNum             int     `json:"pageNum"`
	ItemNumPerPage      int     `json:"itemNumPerPage"`
	SortType            string  `json:"sortType"`
	StationType         string  `json:"stationType"`
}

type StationData struct {
	StationId      string `json:"id"`
	StationName    string `json:"name"`
	Latitude       string `json:"lat"`
	Longitude      string `json:"lng"`
	FastCharge     int    `json:"fastTerminalNum"`
	FreeFastCharge string `json:"fastTerminalIdleNum"`
	SlowCharge     int    `json:"slowTerminalNum"`
	FreeSlowCharge string `json:"slowTerminalIdleNum"`
}

type StationListData struct {
	CurrentPage string        `json:"currentPage"`
	ItemCount   string        `json:"itemCount"`
	PageCount   string        `json:"pageCount"`
	Stations    []StationData `json:"stations"`
}

type StationListResp struct {
	State   string `json:"state"`
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Data    string `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) ([]map[string]any, error) {
	if slr.Data == "" {
		return nil, errors.New(slr.Data).Trace("响应结果 Data 为空")
	}
	r, err := util.DecryptData(slr.Data)
	if err != nil {
		return nil, errors.New(slr.Data).Trace(fmt.Sprintf("响应 Data 解密失败: %s", r))
	}

	sld := StationListData{}
	if err := json.Unmarshal([]byte(r), &sld); err != nil {
		return nil, errors.New(slr.Data).Trace(fmt.Sprintf("Data 反序列化失败: %s", err.Error()))
	}

	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range sld.Stations {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac, nil
}

func (a *Api) GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error) {
	lt, _ := strconv.ParseFloat(p.Lat, 64)
	lg, _ := strconv.ParseFloat(p.Lgn, 64)

	if len(p.CityCode) < 4 {
		return nil, errors.NewFromErr(nil).Trace("CityCode 长度非法: " + p.CityCode)
	}

	originParams := StationListParams{
		CityCode:            p.CityCode[:4],
		CoordinateType:      "gaode",
		KeyWord:             "",
		Lat:                 lt,
		Latitude:            lt,
		Lng:                 lg,
		Longitude:           lg,
		LocationFilterType:  "1",
		LocationFilterValue: 50,
		PageNum:             pageNum,
		ItemNumPerPage:      20,
		SortType:            "1",
		StationType:         "2",
	}

	formData := util.NewRequestParams(originParams, a.accountInfo.Token, a.accountInfo.SSDI)

	reqParams, err := formData.GenerateParams()
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密失败")
	}

	reqJSON, err := json.Marshal(reqParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	slParams := make(map[string]string)
	json.Unmarshal([]byte(reqJSON), &slParams)

	r, err := common.DoRequest(StationListURL, slParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	slr := StationListResp{}
	json.Unmarshal(r.Content(), &slr)

	// token 重试逻辑
	if slr.State == "0" && slr.ErrCode == TOKEN_INVALID_CODE {
		var refreshErr error
		a.mutex.Lock()
		times := 0
		for {
			if times < MAX_REFRESH_TOKEN_TIMES {
				newAccRes, err := task.RefreshAccountInfo(a.target, a.targetDesc)
				if err != nil {
					times++
					logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
					refreshErr = err
					continue
				}

				newAccountInfo := AccountInfo{}
				err = json.Unmarshal(newAccRes, &newAccountInfo)
				if err != nil {
					times++
					logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
					refreshErr = err
					continue
				}
				a.accountInfo = &newAccountInfo
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   a.taskInstanceId,
					RequestData: p,
					BizType:     a.gParam.BizType,
					Channel:     a.gParam.Channel,
					StackTrace:  slr.ErrMsg,
					ErrorMsg:    fmt.Sprintf("用户凭证更新为: [SSDI: %s], [Token: %s]", a.accountInfo.SSDI, a.accountInfo.Token),
					Level:       logger.ERROR_LOG_MESSAGE_MAP.INFO.Type,
				})
				break
			} else {
				break
			}
		}
		a.mutex.Unlock()
		if refreshErr != nil {
			return nil, errors.New(fmt.Sprintf("重新获取 Token 失败 %s", refreshErr.Error()))
		}

		return a.GetStationList(p, pageNum)
	}

	if slr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := slr.convert(a.targetDesc, p.CITY)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点列表数据转换失败, 对端返回 %s", r.ContentToString()))
	}
	return result, nil
}
