package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/hfcharge/api/common"
	"tianyan-crawler/internal/source/hfcharge/api/util"

	"github.com/riete/errors"
)

const (
	StationPriceURL = "https://sgi.hfcdgs.com/api/invoke?SID=BaseApi-App0401_SGetPriceInfoBySta"
)

type StationPriceParams struct {
	StationID string `json:"stationID"`
}

type PriceData struct {
	TimeRange    string `json:"timeRange"`
	StartTime    string
	StopTime     string
	ElecPrice    float64 `json:"electricPrice,string"`
	ServicePrice float64 `json:"servicePrice,string"`
}

func (pd *PriceData) handleTime() error {
	times := strings.Split(pd.TimeRange, "-")
	if len(times) != 2 {
		return errors.New(fmt.Sprintf("price.TimeRange字符串格式不正确: %s", pd.TimeRange))
	}
	pd.StartTime = times[0]
	pd.StopTime = times[1]
	return nil
}

type StationPriceData struct {
	RangePrice []PriceData `json:"rangePrice"`
}

func (spd StationPriceData) handle(
	hoursPerDay int,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	for _, price := range spd.RangePrice {
		err := price.handleTime()
		if err != nil {
			return pd, err
		}

		startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
		if err != nil {
			return pd, err
		}
		stopTime, _ := time.Parse(time.TimeOnly, price.StopTime+":00")
		// 24:00:00 无法处理，所以错误忽略
		// if err != nil {
		// 	return pd, err
		// }

		start := startTime.Add(30 * time.Minute).Hour()
		end := stopTime.Add(30 * time.Minute).Hour()
		if end == 0 {
			end = hoursPerDay
		}

		totalPrice := price.ElecPrice + price.ServicePrice
		for i := start; i < end; i++ {
			pd[i] = map[string]any{
				"id":           rd,
				"jobId":        rd,
				"stationId":    stationId,
				"stationName":  stationName,
				"city":         city,
				"province":     province,
				"stationLat":   latitude,
				"stationLng":   longitude,
				"accountType":  "普通用户",
				"crawlDay":     time.Now().Format("********"),
				"crawlTime":    time.Now().Format(time.TimeOnly),
				"channel":      targetDesc,
				"operName":     targetDesc,
				"pileType":     "3",
				"timeInterval": fmt.Sprintf("%02d", i),
				"elecPrice":    price.ElecPrice,
				"servicePrice": price.ServicePrice,
				"totalPrice":   strconv.FormatFloat(totalPrice, 'f', 4, 64),
			}
		}
	}
	return pd, nil
}

type StationPriceResp struct {
	State   string `json:"state"`
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Data    string `json:"data"`
}

func (spr StationPriceResp) convert(
	targetDesc,
	province,
	city,
	stationId,
	stationName,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	if spr.Data == "" {
		return nil, errors.New(spr.Data).Trace("响应结果 Data 为空")
	}
	r, err := util.DecryptData(spr.Data)
	if err != nil {
		return nil, errors.New(spr.Data).Trace(fmt.Sprintf("响应 Data 解密失败: %s", r))
	}

	spd := []StationPriceData{}
	json.Unmarshal([]byte(r), &spd)

	const hoursPerDay = 24
	result := []map[string]any{}
	// fastGun
	for _, pd := range spd {
		if len(pd.RangePrice) != 0 {
			prices, err := pd.handle(
				hoursPerDay,
				targetDesc,
				stationId,
				stationName,
				city,
				province,
				latitude,
				longitude,
			)
			if err != nil {
				return result, err
			}
			result = append(result, prices...)
		}
	}

	return result, nil
}

func (a *Api) GetStationPrice(p Param) ([]map[string]any, errors.Error) {
	originParams := StationPriceParams{
		StationID: p.StationID,
	}

	formData := util.NewRequestParams(originParams, a.accountInfo.Token, a.accountInfo.SSDI)

	reqParams, err := formData.GenerateParams()
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密失败")
	}

	reqJSON, err := json.Marshal(reqParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	slParams := make(map[string]string)
	json.Unmarshal([]byte(reqJSON), &slParams)

	r, err := common.DoRequest(StationPriceURL, slParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	spr := StationPriceResp{}
	_ = json.Unmarshal(r.Content(), &spr)

	// token 重试逻辑
	if spr.State == "0" && spr.ErrCode == TOKEN_INVALID_CODE {
		var refreshErr error
		a.mutex.Lock()
		times := 0
		for {
			if times < MAX_REFRESH_TOKEN_TIMES {
				newAccRes, err := task.RefreshAccountInfo(a.target, a.targetDesc)
				if err != nil {
					times++
					logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
					refreshErr = err
					continue
				}

				newAccountInfo := AccountInfo{}
				err = json.Unmarshal(newAccRes, &newAccountInfo)
				if err != nil {
					times++
					logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
					refreshErr = err
					continue
				}
				a.accountInfo = &newAccountInfo
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   a.taskInstanceId,
					RequestData: p,
					BizType:     a.gParam.BizType,
					Channel:     a.gParam.Channel,
					StackTrace:  spr.ErrMsg,
					ErrorMsg:    fmt.Sprintf("用户凭证更新为: [SSDI: %s], [Token: %s]", a.accountInfo.SSDI, a.accountInfo.Token),
					Level:       logger.ERROR_LOG_MESSAGE_MAP.INFO.Type,
				})
				break
			} else {
				break
			}
		}
		a.mutex.Unlock()
		if refreshErr != nil {
			return nil, errors.New(fmt.Sprintf("重新获取 Token 失败 %s", refreshErr.Error()))
		}

		return a.GetStationPrice(p)
	}

	if spr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回: %s", r.ContentToString()))
	}

	result, err := spr.convert(
		a.targetDesc,
		a.gParam.Province,
		p.City,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
