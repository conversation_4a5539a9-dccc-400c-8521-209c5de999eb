package common

import (
	"net/http"
	"time"

	proxy "tianyan-crawler/internal/common/proxy"
	"tianyan-crawler/internal/common/request"
)

var commonHeader = map[string]string{
	"accesstoken":     "",
	"tokenfrom":       "2",
	"channelfrom":     "carelife",
	"user-agent":      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF MacWechat/3.8.10(0x13080a10) XWEB/1227",
	"content-type":    "application/json",
	"accept":          "application/json, text/plain, */*",
	"channelid":       "10101",
	"ref":             "xcx_transferb",
	"accept-language": "zh-CN,zh;q=0.9",
}

func NewRequest(proxyIp proxy.ProxyIp, options ...request.Option) *request.Request {
	options = append(
		[]request.Option{
			request.WithDefaultClient(),
			request.WithTimeout(5 * time.Second),
			request.WithProxyFunc(http.ProxyURL(proxyIp.ProxyUrl())),
			request.WithHeader(commonHeader),
		},
		options...,
	)
	return request.NewRequest(options...)
}

func DoRequest(url string, data []byte, retry int, options ...request.Option) (*request.Request, error) {
	var r *request.Request
	var err error
	for i := 0; i < retry; i++ {
		r = NewRequest(proxy.GetIp(), options...)
		err = r.Post(url, data)
		if err != nil {
			time.Sleep(3 * time.Second)
		} else {
			break
		}
	}
	return r, err
}
