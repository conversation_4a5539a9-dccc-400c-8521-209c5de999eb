package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"tianyan-crawler/internal/source/huolala/api/common"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://charge-internal-api.huolala.cn/api/home.getStationList/"
)

type StationListData struct {
	PageNo       int    `json:"pageNo"`
	PageSize     int    `json:"pageSize"`
	Latitude     string `json:"latitude"`
	Longitude    string `json:"longitude"`
	Distance     string `json:"distance"`
	SortType     int    `json:"sortType"`
	DeviceType   string `json:"deviceType"`
	QueryVip     bool   `json:"queryVip"`
	FilterTtemTd string `json:"filterTtemTd"`
}

type StationListParams struct {
	Data StationListData `json:"data"`
}

type StationListResp struct {
	Ret  int    `json:"ret"`
	Code any    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		TotalCount  int  `json:"totalCount"`
		PageSize    int  `json:"pageSize"`
		PageNo      int  `json:"pageNo"`
		LastPage    bool `json:"lastPage"`
		NextPage    int  `json:"nextPage"`
		PrePage     int  `json:"prePage"`
		FirstPage   bool `json:"firstPage"`
		FirstResult int  `json:"firstResult"`
		List        []struct {
			StationId             string  `json:"stationId"`
			OperatorId            string  `json:"operatorId"`
			OperatorName          string  `json:"operatorName"`
			StationName           string  `json:"stationName"`
			CountryCode           string  `json:"countryCode"`
			AreaCode              string  `json:"areaCode"`
			Address               string  `json:"address"`
			StationLng            float64 `json:"stationLng"`
			StationLat            float64 `json:"stationLat"`
			BusineHours           string  `json:"busineHours"`
			Distances             float64 `json:"distances"`
			ElectricityFee        float64 `json:"electricityFee"`
			ServiceFee            float64 `json:"serviceFee"`
			ChargeFee             float64 `json:"chargeFee"`
			ParkFee               string  `json:"parkFee"`
			QuickTotal            int     `json:"quickTotal"`
			QuickIdleCount        int     `json:"quickIdleCount"`
			SlowTotal             int     `json:"slowTotal"`
			SlowIdleCount         int     `json:"slowIdleCount"`
			QuickGunCount         int     `json:"quickGunCount"`
			SlowGunCount          int     `json:"slowGunCount"`
			StationStatus         int     `json:"stationStatus"`
			CanUserRechargeAmount bool    `json:"canUserRechargeAmount"`
			SettlementType        int     `json:"settlementType"`
		} `json:"list"`
	} `json:"data"`
	TraceId string `json:"traceId"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Data.List {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"businessId":     d.OperatorId,
			"lat":            d.StationLat,
			"lon":            d.StationLng,
			"fastCharge":     d.QuickTotal,
			"freeFastCharge": d.QuickIdleCount,
			"slowCharge":     d.SlowTotal,
			"freeSlowCharge": d.SlowIdleCount,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error) {
	slp := StationListParams{
		Data: StationListData{
			PageNo:       pageNum,
			PageSize:     20,
			Latitude:     p.Lat,
			Longitude:    p.Lgn,
			Distance:     "50",
			SortType:     3,
			DeviceType:   "",
			QueryVip:     false,
			FilterTtemTd: "",
		},
	}

	reqJSON, err := json.Marshal(slp)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	r, err := common.DoRequest(StationListURL, reqJSON, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := new(StationListResp)
	if err := json.Unmarshal(r.Content(), sr); err != nil {
		return nil, errors.NewFromErr(err).Trace("解析响应数据失败")
	}

	if sr.Ret != 0 {
		return nil, errors.New(sr.Msg).Trace("API返回错误")
	}

	return sr.convert(a.targetDesc, p.CITY), nil
}
