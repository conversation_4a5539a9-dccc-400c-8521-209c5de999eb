package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/wjcloud/api/common"

	"github.com/riete/errors"
)

const (
	ChargingGunURL = "https://charge-internal-api.huolala.cn/api/elec.equipment.list/"
)

type ChargingGunData struct {
	OperatorId string `json:"operatorId"`
	StationId  string `json:"stationId"`
	PageNo     int    `json:"pageNo"`
	PageSize   int    `json:"pageSize"`
}

type ChargingGunParams struct {
	Data ChargingGunData `json:"data"`
}

type GunData struct {
	StationId         string  `json:"stationId"`
	EquipmentId       string  `json:"equipmentId"`
	EquipmentTypeName string  `json:"equipmentTypeName"`
	Power             float64 `json:"power"`
	Type              string  `json:"type"`
	TypeName          string  `json:"typeName"`
	StatusName        string  `json:"statusName"`
	ParkNo            string  `json:"parkNo"`
}

type ChargingGunResp struct {
	Ret  int    `json:"ret"`
	Code any    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		List []GunData `json:"list"`
	} `json:"data"`
	TraceId string `json:"traceId"`
}

func (cgr *ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	r := []map[string]any{}

	for _, gun := range cgr.Data.List {
		r = append(r, map[string]any{
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"channel":     targetDesc,
			"gunId":       gun.EquipmentId,
			"gunType":     gun.EquipmentTypeName + gun.TypeName,
			"power":       gun.Power,
			"status":      gun.StatusName,
			"parkingNo":   gun.ParkNo,
			"runTime":     time.Now().Format(time.DateTime),
		})
	}

	return r, nil
}

func (a Api) GetChargingGunList(p Param) ([]map[string]any, errors.Error) {
	params := ChargingGunParams{
		Data: ChargingGunData{
			OperatorId: p.BusinessId,
			StationId:  p.StationID,
			PageNo:     1,
			PageSize:   9999,
		},
	}

	reqJSON, err := json.Marshal(params)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	r, err := common.DoRequest(ChargingGunURL, reqJSON, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取充电枪数据失败, http响应: %s", status))
	}

	cgr := new(ChargingGunResp)
	if err := json.Unmarshal(r.Content(), cgr); err != nil {
		return nil, errors.NewFromErr(err).Trace("解析响应数据失败")
	}

	if cgr.Ret != 0 {
		return nil, errors.New(fmt.Sprintf("获取充电枪数据失败, 对端返回: %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("充电枪数据处理失败, %s", err.Error()))
	}

	return result, nil
}
