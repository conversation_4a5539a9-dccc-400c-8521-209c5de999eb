package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/huolala/api/common"

	"github.com/riete/errors"
)

const (
	StationDetailURL = "https://charge-internal-api.huolala.cn/api/elec.station.info.detail.get/"
)

type StationDetailData struct {
	OperatorId string  `json:"operatorId"`
	StationId  string  `json:"stationId"`
	Longitude  float64 `json:"longitude"`
	Latitude   float64 `json:"latitude"`
}

type StationDetailParams struct {
	Data StationDetailData `json:"data"`
}

type ConnectorInfo struct {
	ConnectorID        string  `json:"ConnectorID"`
	Power              float64 `json:"Power"`
	VoltageUpperLimits float64 `json:"VoltageUpperLimits"`
	VoltageLowerLimits float64 `json:"VoltageLowerLimits"`
	ConnectorType      float64 `json:"ConnectorType"`
}

type StationDetailResp struct {
	Ret  int `json:"ret"`
	Data struct {
		ID                    int     `json:"id"`
		StationId             string  `json:"stationId"`
		OperatorId            string  `json:"operatorId"`
		OperatorName          string  `json:"operatorName"`
		EquipmentOwnerId      string  `json:"equipmentOwnerId"`
		StationName           string  `json:"stationName"`
		CountryCode           string  `json:"countryCode"`
		AreaCode              string  `json:"areaCode"`
		Address               string  `json:"address"`
		StationTel            string  `json:"stationTel"`
		ServiceTel            string  `json:"serviceTel"`
		StationType           int     `json:"stationType"`
		StationStatus         int     `json:"stationStatus"`
		StationLng            float64 `json:"stationLng,string"`
		StationLat            float64 `json:"stationLat,string"`
		BusineHours           string  `json:"busineHours"`
		CurrentPeriod         string  `json:"currentPeriod"`
		ElectricityFee        string  `json:"electricityFee"`
		CurrentElectricityFee float64 `json:"currentElectricityFee"`
		ServiceFee            string  `json:"serviceFee"`
		CurrentServiceFee     float64 `json:"currentServiceFee"`
		ParkFee               string  `json:"parkFee"`
		Payment               string  `json:"payment"`
		QuickTotal            int     `json:"quickTotal"`
		QuickIdleCount        int     `json:"quickIdleCount"`
		SlowTotal             int     `json:"slowTotal"`
		SlowIdleCount         int     `json:"slowIdleCount"`
		Power                 int     `json:"power"`
		Distance              float64 `json:"distance"`
	} `json:"data"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) []map[string]any {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	operatorTel := sdr.Data.StationTel
	if operatorTel == "" {
		operatorTel = sdr.Data.ServiceTel
	}

	am := []map[string]any{
		{
			"id":           rd,
			"jobId":        rd,
			"channel":      channel,
			"city":         city,
			"province":     province,
			"stationId":    sdr.Data.StationId,
			"stationName":  sdr.Data.StationName,
			"stationAddr":  sdr.Data.Address,
			"operatorId":   sdr.Data.OperatorId,
			"operatorName": sdr.Data.OperatorName,
			"operatorTel":  operatorTel,
			"fastCharge":   sdr.Data.QuickTotal,
			"slowCharge":   sdr.Data.SlowTotal,
			"lon":          sdr.Data.StationLng,
			"lat":          sdr.Data.StationLat,
			"businessTime": sdr.Data.BusineHours,
			"parkFeeDesc":  sdr.Data.ParkFee,
			"runTime":      ct,
		},
	}
	return am
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Latitude, 64)
	lng, _ := strconv.ParseFloat(p.Longitude, 64)

	params := StationDetailParams{
		Data: StationDetailData{
			OperatorId: p.BusinessId,
			StationId:  p.StationID,
			Latitude:   lat,
			Longitude:  lng,
		},
	}

	reqJSON, err := json.Marshal(params)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	r, err := common.DoRequest(StationDetailURL, reqJSON, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := new(StationDetailResp)
	if err := json.Unmarshal(r.Content(), sdr); err != nil {
		return nil, errors.NewFromErr(err).Trace("解析响应数据失败")
	}

	if sdr.Ret != 0 {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString()))
	}

	return sdr.convert(a.targetDesc, a.gParam.Province, p.City), nil
}
