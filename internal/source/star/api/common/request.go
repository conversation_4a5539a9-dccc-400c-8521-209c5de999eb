package common

import (
	"net/http"
	"time"

	"tianyan-crawler/internal/common/proxy"
	"tianyan-crawler/internal/common/request"
)

var commonHeader = map[string]string{
	"Accept":          "*/*",
	"Connection":      "keep-alive",
	"User-Agent":      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF MacWechat/3.8.6(0x13080610) XWEB/1156",
	"sec-fetch-site":  "cross-site",
	"sec-fetch-mode":  "cors",
	"sec-fetch-dest":  "empty",
	"accept-language": "zh-CN,zh;q=0.9",
}

func NewRequest(proxyIp proxy.ProxyIp, options ...request.Option) *request.Request {
	options = append(
		[]request.Option{
			request.WithDefaultClient(),
			request.WithTimeout(5 * time.Second),
			request.WithProxyFunc(http.ProxyURL(proxyIp.ProxyUrl())),
			request.WithHeader(commonHeader),
		},
		options...,
	)
	return request.NewRequest(options...)
}

func DoRequest(url string, data []byte, retry int, options ...request.Option) (*request.Request, error) {
	var r *request.Request
	var err error
	for i := 0; i < retry; i++ {
		r = NewRequest(proxy.GetIp(), options...)
		// r.Request().Header["x-uid"] = []string{"2088722596083532"}
		r.Request().Header["appVersion"] = []string{"********"}
		err = r.Post(url, data)
		if err != nil {
			time.Sleep(3 * time.Second)
		} else {
			break
		}
	}
	return r, err
}

func GetRequest(url string, query map[string]string, retry int, options ...request.Option) (*request.Request, error) {
	var r *request.Request
	var err error
	for i := 0; i < retry; i++ {
		r = NewRequest(proxy.GetIp(), options...)
		err = r.Get(url, query)
		if err != nil {
			time.Sleep(3 * time.Second)
		} else {
			break
		}
	}
	return r, err
}
