package common

import (
	"fmt"
	"log"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"tianyan-crawler/internal/common/util"
	"time"
)

func GenerateSign[T interface{}](body T, ts int64) (string, string) {
	keys := reflect.TypeOf(body)
	vals := reflect.ValueOf(body)
	pairArr := [][]string{}
	for i := 0; i < keys.NumField(); i++ {
		field := keys.Field(i)
		value := vals.Field(i)

		var v string
		switch value.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			v = strconv.FormatInt(value.Int(), 10)
		case reflect.Float32, reflect.Float64:
			v = strconv.FormatFloat(value.Float(), 'f', -1, 64)
		default:
			v = value.String()
		}

		pairArr = append(pairArr, []string{field.Tag.Get("json"), v})
	}

	sort.SliceStable(pairArr, func(i, j int) bool {
		return strings.Compare(pairArr[i][0], pairArr[j][0]) < 0
	})

	s := ""
	for i, pair := range pairArr {
		if i != 0 {
			s += "&"
		}
		s += pair[0] + "=" + pair[1]
	}
	return s, strings.ToUpper(util.MD5(util.MD5(s) + strconv.FormatInt(ts, 10)))
}

func BelongCalendar(from, to, hour string) bool {
	result := false

	// 获取当前日期
	now := time.Now()
	format := now.Format("2006-01-02")

	// 构建时间字符串
	timeStr := fmt.Sprintf("%s %s:00", format, hour)
	tBeginStr := fmt.Sprintf("%s %s", format, from)
	tEndStr := fmt.Sprintf("%s %s", format, to)

	// 解析时间
	tBegin, err := time.Parse("2006-01-02 15:04", tBeginStr)
	if err != nil {
		log.Fatalf("解析开始时间失败: %v", err)
	}
	tEnd, err := time.Parse("2006-01-02 15:04", tEndStr)
	if err != nil {
		log.Fatalf("解析结束时间失败: %v", err)
	}
	timeDate, err := time.Parse("2006-01-02 15:04", timeStr)
	if err != nil {
		log.Fatalf("解析比较时间失败: %v", err)
	}

	// 检查时间范围
	if (timeDate.After(tBegin) || timeDate.Equal(tBegin)) && timeDate.Before(tEnd) {
		result = true
	}

	return result
}
