package alipay

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"tianyan-crawler/internal/common/request"
	"time"

	"github.com/google/uuid"

	"github.com/riete/errors"

	"tianyan-crawler/internal/source/star/api/common"
	"tianyan-crawler/internal/source/star/api/types"
)

const (
	StationListURL = "https://gateway.star-charge.com/apph5/xcxApiV2/ali/stubGroup/list/query/noUser"
)

type StationListBody struct {
	AllStation     string  `json:"allStation"`
	EquipmentType  string  `json:"equipmentType"`
	Lat            float64 `json:"lat"`
	Lng            float64 `json:"lng"`
	Nonce          string  `json:"nonce"`
	OrderType      int     `json:"orderType"`
	Page           int     `json:"page"`
	Pagecount      int     `json:"pagecount"`
	Radius         int     `json:"radius"`
	SearchScene    int     `json:"searchScene"`
	StubGroupTypes string  `json:"stubGroupTypes"`
	Timestamp      int64   `json:"timestamp"`
}

type StationListData struct {
	StationId      string  `json:"id"`
	StationName    string  `json:"name"`
	Latitude       float32 `json:"gisGcj02Lat"`
	Longitude      float32 `json:"gisGcj02Lng"`
	FastCharge     int     `json:"dcCnt"`
	FreeFastCharge int     `json:"dcIdleCnt"`
	SlowCharge     int     `json:"acCnt"`
	FreeSlowCharge int     `json:"acIdleCnt"`
}

type StationListResp struct {
	Code      string            `json:"code"`
	Text      string            `json:"text"`
	PageLimit PageLimit         `json:"pageLimit"`
	Action    int               `json:"action"`
	Data      []StationListData `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Data {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p types.Param, pageNum int, stationType string) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)
	nonce := uuid.New()
	ts := time.Now().UnixMilli()

	body := StationListBody{
		AllStation:     "1",
		EquipmentType:  stationType,
		Lat:            lat,
		Lng:            lng,
		Nonce:          nonce.String(),
		OrderType:      1,
		Page:           pageNum,
		Pagecount:      10,
		Radius:         50000,
		SearchScene:    1,
		StubGroupTypes: "0",
		Timestamp:      ts,
	}

	data, signature := common.GenerateSign(body, ts)

	headers := map[string]string{
		"x-ca-signature": signature,
		"referer":        "https://****************.hybrid.alipay-eco.com/****************/0.2.**********.55/index.html#pages/index/index",
		"appVersion":     "********",
		"x-uid":          a.AccountInfo.Token,
		"channel-id":     "101",
		"x-release-type": "ONLINE",
		"userid":         "",
		"sid":            strconv.FormatInt(ts, 10),
		"authorization":  "",
		"x-ca-timestamp": strconv.FormatInt(ts, 10),
		"positcity":      p.CityCode,
		"content-type":   "application/x-www-form-urlencoded",
		"did":            "",
	}

	r, err := common.DoRequest(StationListURL, []byte(data), 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := new(StationListResp)
	_ = json.Unmarshal(r.Content(), sr)

	if sr.Code != "200" {
		return []map[string]any{}, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.TargetDesc, p.CITY), nil
}
