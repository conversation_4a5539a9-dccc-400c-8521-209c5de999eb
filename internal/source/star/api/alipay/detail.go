package alipay

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"tianyan-crawler/internal/common/request"
	"time"

	"github.com/google/uuid"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/star/api/common"
	"tianyan-crawler/internal/source/star/api/types"
)

const (
	StationDetailURL = "https://gateway.starcharge.com/apph5/xcxApiV2/ali/stubGroup/stubGroupDetailNew/base/find/noUser"
)

type StationDetailBody struct {
	GisType   int     `json:"gisType"`
	Id        string  `json:"id"`
	Lat       float64 `json:"lat"`
	Lng       float64 `json:"lng"`
	Nonce     string  `json:"nonce"`
	Timestamp int64   `json:"timestamp"`
}

type StationDetailData struct {
	StationId              string           `json:"id"`
	StationName            string           `json:"name"` // 站点名称
	StationAddr            string           `json:"address"`
	StationStatus          int              `json:"stubGroupStatus"`
	OperatorId             string           `json:"equipmentOperatorId"`   // 运营商手机号码
	OperatorName           string           `json:"equipmentOperatorName"` // 运营商名称
	FastCharge             int              `json:"dcCnt"`
	SlowCharge             int              `json:"acCnt"`
	Lon                    float32          `json:"gisGcj02Lng"`
	Lat                    float32          `json:"gisGcj02Lat"`
	BusinessTime           string           `json:"serviceTime"` // 营运时间
	ParkFeeDesc            string           `json:"parkingFeeInfo"`
	OperatorTel            string           `json:"adminTel"`
	SiteOperator           string           `json:"adminName"`
	OperateCategory        string           `json:"operateCategory"`
	MaxKw                  string           `json:"maxKw"`
	SupportPnp             bool             `json:"supportPnp"`
	StubGroupScore         string           `json:"stubGroupScore"`
	ServiceProvider        string           `json:"cspName"`
	GuideInfo              map[string]any   `json:"guideInfo"`
	SupportingFacilityList []map[string]any `json:"supportingFacilityList"`
}

type StationDetailResp struct {
	Code   string            `json:"code"`
	Text   string            `json:"text"`
	Result StationDetailData `json:"data"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) map[string]any {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	stationStatus := ""
	if sdr.Result.StationStatus == 1 {
		stationStatus = "营业中"
	} else if sdr.Result.StationStatus == 2 {
		stationStatus = "已下线"
	} else {
		stationStatus = strconv.Itoa(sdr.Result.StationStatus)
	}

	am := map[string]any{
		"id":                   rd,
		"jobId":                rd,
		"channel":              channel,
		"city":                 city,
		"province":             province,
		"lon":                  sdr.Result.Lon,
		"lat":                  sdr.Result.Lat,
		"tips":                 getTips(sdr.Result),
		"stationId":            sdr.Result.StationId,
		"stationName":          sdr.Result.StationName,
		"operatorId":           sdr.Result.OperatorId,
		"operatorName":         sdr.Result.OperatorName,
		"operatorType":         getOperatorType(sdr.Result.OperateCategory),
		"operatorTel":          sdr.Result.OperatorTel,
		"siteOperator":         sdr.Result.SiteOperator,
		"stationStatus":        stationStatus,
		"fastCharge":           sdr.Result.FastCharge,
		"slowCharge":           sdr.Result.SlowCharge,
		"businessTime":         sdr.Result.BusinessTime,
		"parkFeeDesc":          sdr.Result.ParkFeeDesc,
		"stationAddr":          sdr.Result.StationAddr,
		"stationLocation":      getStationLocation(sdr.Result),
		"supportingFacilities": getSupportingFacility(sdr.Result.SupportingFacilityList),
		"serviceProvider":      sdr.Result.ServiceProvider,
		"runTime":              ct,
	}
	return am
}

func (a Api) GetStationDetail(p types.Param) (map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Latitude, 64)
	lng, _ := strconv.ParseFloat(p.Longitude, 64)
	nonce := uuid.New()
	ts := time.Now().UnixMilli()

	body := StationDetailBody{
		GisType:   1,
		Id:        p.StationID,
		Lat:       lat,
		Lng:       lng,
		Nonce:     nonce.String(),
		Timestamp: ts,
	}

	data, signature := common.GenerateSign(body, ts)

	headers := map[string]string{
		"xweb_xhr":       "1",
		"authorization":  "",
		"x-release-type": "ONLINE",
		"sid":            strconv.FormatInt(ts, 10),
		"X-Ca-Signature": signature,
		"X-Ca-Timestamp": strconv.FormatInt(ts, 10),
		"x-uid":          a.AccountInfo.Token,
		"positcity":      p.CityCode,
		"channel-id":     "101",
		"did":            "",
		"content-type":   "application/x-www-form-urlencoded",
	}

	r, err := common.DoRequest(StationDetailURL, []byte(data), 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sr := new(StationDetailResp)
	_ = json.Unmarshal(r.Content(), sr)

	if sr.Code != "200" {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.TargetDesc, a.GParam.Province, p.City), nil
}

func getSupportingFacility(supportingFacilityList []map[string]any) string {
	var supportingFacility strings.Builder
	for _, m := range supportingFacilityList {
		supportingFacility.WriteString(m["name"].(string))
		supportingFacility.WriteString(",")
	}
	return strings.TrimSuffix(supportingFacility.String(), ",")
}

func getTips(detail StationDetailData) string {
	var result strings.Builder

	if detail.SupportPnp {
		result.WriteString("即插即充,")
	}

	if detail.StubGroupScore != "" {
		result.WriteString(detail.StubGroupScore)
		result.WriteString("分,")
	}

	floorLevel, ok := detail.GuideInfo["floorLevel"].(string)
	if !ok {
		floorLevel = ""
	}

	floorSplit := strings.Split(floorLevel, ",")

	for _, f := range floorSplit {
		f = strings.TrimSpace(f)
		if f != "" {
			floor, err := strconv.Atoi(f)
			if err != nil {
				// Handle error appropriately; for simplicity, ignoring here.
				continue
			}

			if floor < 0 {
				result.WriteString("地下B")
				result.WriteString(strconv.Itoa(abs(floor)))
				result.WriteString(",")
			} else {
				result.WriteString("地面F")
				result.WriteString(strconv.Itoa(abs(floor)))
				result.WriteString(",")
			}
		}
	}

	if detail.MaxKw != "" {
		result.WriteString("最快")
		result.WriteString(detail.MaxKw)
		result.WriteString("kW,")
	}

	if detail.OperateCategory != "0" {
		result.WriteString(getOperatorType(detail.OperateCategory))
		result.WriteString(",")
	}

	return strings.TrimSuffix(result.String(), ",")
}

func getOperatorType(operateCategory string) string {
	switch operateCategory {
	case "0":
		return "他营"
	case "1":
		return "联营"
	case "2", "3":
		return "直营"
	case "4":
		return "互联互通"
	default:
		return "共享私桩"
	}
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func getStationLocation(detail StationDetailData) string {
	floorLevel, ok := detail.GuideInfo["floorLevel"].(string)
	if !ok {
		floorLevel = ""
	}

	var floorStr = ""
	if floorLevel == "" {
		return "地上"
	}

	var floorSplit = strings.Split(floorLevel, ",")

	for i := range floorSplit {

		if floorSplit[i] != "" {

			floor, _ := strconv.Atoi(floorSplit[i])

			if floor < 0 {
				floorStr = floorStr + "地上,"
			} else {
				floorStr = floorStr + "地下,"
			}

		}
	}

	return strings.TrimSuffix(floorStr, ",")
}
