package wx

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"tianyan-crawler/internal/common/request"
	"time"

	"github.com/google/uuid"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/star/api/common"
	"tianyan-crawler/internal/source/star/api/types"
)

const (
	PriceListURL = "https://gateway.starcharge.com/apph5/xcxApiV2/wechat/stubGroup/stubGroupFeeInfo/get/noUser"
)

type PriceData struct {
	ElecPrice    float64 `json:"eleAmount"`
	ServicePrice float64 `json:"serviceAmount"`
	TotalPrice   float64 `json:"totalFee"`
}

type StationPriceResp struct {
	Code    string           `json:"code"`
	Message string           `json:"text"`
	Result  StationPriceData `json:"data"`
}

type FeeInfos struct {
	StartTime     string
	StopTime      string
	TimeSlot      string    `json:"time"`
	MemberFeeInfo PriceData `json:"memberFeeInfo"`
	OriginFeeInfo PriceData `json:"originFeeInfo"`
}

func (pd *FeeInfos) handleTime() error {
	times := strings.Split(pd.TimeSlot, "-")
	if len(times) != 2 {
		return errors.New(fmt.Sprintf("price.Time字符串格式不正确: %s", pd.TimeSlot))
	}
	pd.StartTime = times[0]
	pd.StopTime = times[1]
	return nil
}

type StationPriceData struct {
	DetailFeeInfos []FeeInfos `json:"stubGroupDetailFeeInfos"`
}

type PriceListParam struct {
	Id        string `json:"id"`
	StubType  int    `json:"stubType"`
	Nonce     string `json:"nonce"`
	Timestamp int64  `json:"timestamp"`
}

func (spr StationPriceResp) convert(
	targetDesc,
	stationId,
	stationName,
	latitude,
	longitude,
	province,
	city string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	r := []map[string]any{}
	if len(spr.Result.DetailFeeInfos) != 0 {
		dc, err := handlePriceData(
			spr.Result.DetailFeeInfos,
			hoursPerDay,
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return []map[string]any{}, err
		}
		r = append(r, dc...)
	}

	return r, nil
}

func handlePriceData(
	data []FeeInfos,
	hoursPerDay int,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	for _, price := range data {
		err := price.handleTime()
		if err != nil {
			return pd, err
		}

		startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
		if err != nil {
			return pd, err
		}
		stopTime, _ := time.Parse(time.TimeOnly, price.StopTime+":00")
		// 24:00:00 无法处理，所以错误忽略
		// if err != nil {
		// 	return pd, err
		// }

		start := startTime.Add(30 * time.Minute).Hour()
		end := stopTime.Add(30 * time.Minute).Hour()
		if end == 0 {
			end = hoursPerDay
		}

		for i := start; i < end; i++ {
			pd[i] = map[string]any{
				"id":                 rd,
				"jobId":              rd,
				"stationId":          stationId,
				"stationName":        stationName,
				"city":               city,
				"province":           province,
				"stationLat":         latitude,
				"stationLng":         longitude,
				"accountType":        "普通用户",
				"crawlDay":           time.Now().Format("********"),
				"crawlTime":          time.Now().Format(time.TimeOnly),
				"channel":            targetDesc,
				"operName":           targetDesc,
				"pileType":           "3",
				"timeInterval":       fmt.Sprintf("%02d", i),
				"memberElecPrice":    strconv.FormatFloat(price.MemberFeeInfo.ElecPrice, 'f', -1, 64),
				"memberServicePrice": strconv.FormatFloat(price.MemberFeeInfo.ServicePrice, 'f', -1, 64),
				"memberPrice":        strconv.FormatFloat(price.MemberFeeInfo.TotalPrice, 'f', -1, 64),
				"elecPrice":          strconv.FormatFloat(price.OriginFeeInfo.ElecPrice, 'f', -1, 64),
				"servicePrice":       strconv.FormatFloat(price.OriginFeeInfo.ServicePrice, 'f', -1, 64),
				"totalPrice":         strconv.FormatFloat(price.OriginFeeInfo.TotalPrice, 'f', -1, 64),
			}
		}
	}
	return pd, nil
}

func (a Api) GetStationPrice(p types.Param) ([]map[string]any, errors.Error) {
	nonce := uuid.New()
	ts := time.Now().UnixMilli()
	params := PriceListParam{
		Id:        p.StationID,
		StubType:  0,
		Nonce:     nonce.String(),
		Timestamp: ts,
	}

	data, signature := common.GenerateSign[PriceListParam](params, ts)

	query := map[string]string{}
	for _, vars := range strings.Split(data, "&") {
		pair := strings.Split(vars, "=")
		if pair[0] == "timestamp" {
			continue
		}
		query[pair[0]] = pair[1]
	}
	headers := map[string]string{
		"X-Ca-Signature": signature,
		"X-Ca-Timestamp": strconv.FormatInt(ts, 10),
		"x-uid":          a.AccountInfo.Token,
		"channel-id":     "100",
		"Origin":         "https://app-cdn.starcharge.com",
		"sec-fetch-site": "same-site",
	}
	r, err := common.GetRequest(PriceListURL, query, 6, request.WithHeader(headers))
	//r, err := common.DoRequest(PriceListURL, query, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	spr := StationPriceResp{}
	_ = json.Unmarshal(r.Content(), &spr)

	if spr.Code != "200" {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := spr.convert(
		a.TargetDesc,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
		a.GParam.Province,
		a.GParam.City,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
