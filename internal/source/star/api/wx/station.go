package wx

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"tianyan-crawler/internal/common/request"
	"time"

	"github.com/google/uuid"

	"github.com/riete/errors"

	"tianyan-crawler/internal/source/star/api/common"
	"tianyan-crawler/internal/source/star/api/types"
)

const (
	StationListURL = "https://gateway.starcharge.com/apph5/xcxApiV2/wechat/stubGroup/list/query/noUser"
)

type StationListBody struct {
	Citys          string  `json:"citys"`
	EquipmentType  string  `json:"equipmentType"`
	GisType        int     `json:"gisType"`
	Keywords       string  `json:"keywords"`
	Lat            float64 `json:"lat"`
	Lng            float64 `json:"lng"`
	Nonce          string  `json:"nonce"`
	OrderType      int     `json:"orderType"`
	Page           int     `json:"page"`
	PageCount      int     `json:"pagecount"`
	ParkingFeeType string  `json:"parkingFeeType"`
	Radius         int     `json:"radius"`
	SearchType     int     `json:"searchType"`
	StubGroupTypes string  `json:"stubGroupTypes"`
	Timestamp      int64   `json:"timestamp"`
	UserLat        float64 `json:"userLat"`
	UserLng        float64 `json:"userLng"`
}

type StationListData struct {
	StationId   string  `json:"id"`
	StationName string  `json:"name"`
	Latitude    float32 `json:"gisGcj02Lat"`
	Longitude   float32 `json:"gisGcj02Lng"`
}

type StationListResp struct {
	Code      string            `json:"code"`
	Text      string            `json:"text"`
	PageLimit PageLimit         `json:"pageLimit"`
	Action    int               `json:"action"`
	Data      []StationListData `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	for _, d := range slr.Data {
		ac = append(ac, map[string]any{
			"channel":     channel,
			"city":        city,
			"stationId":   d.StationId,
			"stationName": d.StationName,
			"lat":         d.Latitude,
			"lon":         d.Longitude,
			"runTime":     ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p types.Param, pageNum int, stationType string) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)
	nonce := uuid.New()
	ts := time.Now().UnixMilli()

	body := StationListBody{
		Citys:          p.CityCode,
		EquipmentType:  stationType,
		GisType:        1,
		Keywords:       "",
		Lat:            lat,
		Lng:            lng,
		Nonce:          nonce.String(),
		OrderType:      1,
		Page:           pageNum,
		PageCount:      100,
		ParkingFeeType: "",
		Radius:         50000,
		SearchType:     1,
		StubGroupTypes: "0",
		Timestamp:      ts,
		UserLat:        lat,
		UserLng:        lng,
	}

	data, signature := common.GenerateSign(body, ts)

	headers := map[string]string{
		"xweb_xhr":       "1",
		"sid":            strconv.FormatInt(ts, 10),
		"X-Ca-Signature": signature,
		"X-Ca-Timestamp": strconv.FormatInt(ts, 10),
		"x-uid":          a.AccountInfo.Token,
		"positCity":      p.CityCode,
		"channel-id":     "100",
		"content-type":   "application/x-www-form-urlencoded",
	}

	r, err := common.DoRequest(StationListURL, []byte(data), 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := new(StationListResp)
	_ = json.Unmarshal(r.Content(), sr)

	if sr.Code != "200" {
		return []map[string]any{}, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.TargetDesc, p.CITY), nil
}
