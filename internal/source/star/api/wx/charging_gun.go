package wx

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"tianyan-crawler/internal/common/request"
	"time"

	"github.com/google/uuid"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/star/api/common"
	"tianyan-crawler/internal/source/star/api/types"
)

const (
	ChargingGunURL = "https://gateway.starcharge.com/apph5/xcxApiV2/wechat/stubGroup/stubGroupInfo/stubList/find/noUser"
)

type PageLimit struct {
	CurrentPageNo  int64 `json:"currentPageNo"`
	PageLength     int64 `json:"pageLength"`
	TotalCount     int64 `json:"totalCount"`
	TotalPageCount int64 `json:"totalPageCount"`
	HasNextPage    bool  `json:"hasNextPage"`
}
type ChargingGunResp struct {
	Code      string            `json:"code"`
	Result    []ChargingGunData `json:"data"`
	PageLimit PageLimit         `json:"pageLimit"`
	Message   string            `json:"message"`
}

func (cgr *ChargingGunResp) convert(targetDesc, stationId, stationName, city, pileType string) ([]map[string]any, error) {
	var r []map[string]any

	ChargeGunType := map[string]string{
		"fastGun": "直流",
		"slowGun": "交流",
	}

	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgr.Result {
		power := ""
		if gun.Power != 0 {
			gunPowerString := fmt.Sprintf("%f", gun.Power)
			power = gunPowerString[:len(gunPowerString)-2]
		}
		r = append(r, map[string]any{
			"channel":           targetDesc,
			"id":                rd,
			"jobId":             rd,
			"power":             power,
			"city":              city,
			"gunId":             gun.GunId,
			"stationId":         stationId,
			"stationName":       stationName,
			"ratedCurrent":      fmt.Sprintf("%f", gun.RatedCurrent),
			"runTime":           time.Now().Format(time.DateTime),
			"gunType":           ChargeGunType[pileType],
			"voltageUpperLimit": strconv.Itoa(gun.VoltageUpperLimit),
			"voltageLowerLimit": strconv.Itoa(gun.VoltageLowerLimit),
			"parkingNo":         gun.ParkingNo,
		})
	}

	return r, nil
}

type ChargingGunBody struct {
	ID        string `json:"id"`
	Nonce     string `json:"nonce"`
	Page      int    `json:"page"`
	PageCount int    `json:"pagecount"`
	StubType  string `json:"stubType"`
	Timestamp int64  `json:"timestamp"`
}

type ChargingGunData struct {
	ParkingNo         string
	RatedCurrent      float32
	VoltageUpperLimit int
	VoltageLowerLimit int
	GunId             string  `json:"id"`
	Power             float32 `json:"kw"`
}

func (a Api) GetChargingGunList(p types.Param, pageNum int, pileType string) ([]map[string]any, errors.Error) {
	nonce := uuid.New()
	ts := time.Now().UnixMilli()

	body := ChargingGunBody{
		ID:        p.StationID,
		Nonce:     nonce.String(),
		Page:      pageNum,
		PageCount: 50,
		StubType:  PileTypeMap[pileType],
		Timestamp: ts,
	}

	data, signature := common.GenerateSign[ChargingGunBody](body, ts)
	headers := map[string]string{
		"xweb_xhr":       "1",
		"sid":            strconv.FormatInt(ts, 10),
		"X-Ca-Signature": signature,
		"X-Ca-Timestamp": strconv.FormatInt(ts, 10),
		"x-uid":          a.AccountInfo.Token,
		"positCity":      p.CityCode,
		"channel-id":     "100",
		"content-type":   "application/x-www-form-urlencoded",
	}

	r, err := common.DoRequest(ChargingGunURL, []byte(data), 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("充电枪列表数据失败, http响应: %s", status))
	}

	sr := new(ChargingGunResp)
	_ = json.Unmarshal(r.Content(), sr)
	if sr.Code != "200" {
		return nil, errors.New(fmt.Sprintf("充电枪列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := sr.convert(a.TargetDesc, p.StationID, p.StationName, p.City, pileType)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("充电枪列表数据失败, 数据转换失败 %s", err.Error()))
	}

	return result, nil
}
