package api

import (
	"github.com/riete/errors"

	"tianyan-crawler/internal/source/star/api/alipay"
	"tianyan-crawler/internal/source/star/api/types"
	"tianyan-crawler/internal/source/star/api/wx"
)

var AccountType = struct {
	WX     string
	AliPay string
}{
	WX:     "wx",
	AliPay: "alipay",
}

type StarChargeApi interface {
	GetStationList(p types.Param, pageNum int, stationType string) ([]map[string]any, errors.Error)
	GetStationDetail(p types.Param) (map[string]any, errors.Error)
	GetStationPrice(p types.Param) ([]map[string]any, errors.Error)
	GetChargingGunList(p types.Param, pageNum int, pileType string) ([]map[string]any, errors.Error)
}

func New(targetDesc string, tm types.TaskMessage) StarChargeApi {
	if tm.AccountInfo.Type == AccountType.WX {
		return wx.Api{
			TargetDesc:  targetDesc,
			AccountInfo: tm.AccountInfo,
			GParam:      tm.GlobalParam,
		}
	} else if tm.AccountInfo.Type == AccountType.AliPay {
		return alipay.Api{
			TargetDesc:  targetDesc,
			AccountInfo: tm.AccountInfo,
			GParam:      tm.GlobalParam,
		}
	} else {
		return nil
	}
}
