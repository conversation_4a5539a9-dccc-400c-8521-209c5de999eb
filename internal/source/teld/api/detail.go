package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/teld/api/common"
	"tianyan-crawler/internal/source/teld/api/util"

	"github.com/riete/errors"
)

const (
	StationDetailURL     = "https://sg.teld.cn/api/invoke?SID=AAS-App0600_GetStationDetails"
	StationPeripheralURL = "https://sg.teld.cn/api/invoke?SID=CMS-GetStationPeripheralAds"
)

type StationDetailParams struct {
	StationID      string   `json:"stationID"`
	CoordinateType string   `json:"coordinateType"`
	Lat            float64  `json:"lat"`
	Lng            float64  `json:"lng"`
	UserLevel      string   `json:"userLevel"`
	CanDiscount    string   `json:"canDiscount"`
	Source         string   `json:"source"`
	TagInfo        []string `json:"tagInfo"`
}

type StationDetailData struct {
	StationId    string  `json:"stationId"`
	StationName  string  `json:"stationName"`
	StationAddr  string  `json:"stationAddress"`
	OperatorName string  `json:"operatorName"`
	OperatorTel  string  `json:"operatorPhone"`
	SiteOperator string  `json:"invoiceSupport"`
	FastCharge   int     `json:"fastTerminalNum"`
	SlowCharge   int     `json:"slowTerminalNum"`
	Lon          float64 `json:"lng"`
	Lat          float64 `json:"lat"`
	BusinessTime string  `json:"businessHours"`
	ParkFeeDesc  string  `json:"parkFeeDesc"`
	TagsNew      []struct {
		TagLabel string `json:"tagLabel"`
	} `json:"tagsNew"`
	StationService []struct {
		Name string `json:"name"`
	} `json:"stationService"`
}

type StationDetailResp struct {
	State   string `json:"state"`
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Data    string `json:"data"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) ([]map[string]any, error) {
	if sdr.Data == "" {
		return nil, errors.New(sdr.Data).Trace("响应结果 Data 为空")
	}
	r, err := util.DecryptData(sdr.Data)
	if err != nil {
		return nil, errors.New(sdr.Data).Trace(fmt.Sprintf("响应 Data 解密失败: %s", r))
	}

	sdd := StationDetailData{}
	json.Unmarshal([]byte(r), &sdd)

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	stationLocation := ""
	openRemark := "不对外开放"
	operatorType := ""
	tips := []string{}
	for _, tag := range sdd.TagsNew {
		if tag.TagLabel != "" {
			if strings.HasPrefix(tag.TagLabel, "非露天") {
				stationLocation = "地下"
			} else {
				stationLocation = "地上"
			}
			if tag.TagLabel == "对外开放" {
				openRemark = "对外开放"
			}
			if strings.HasPrefix(tag.TagLabel, "自营") {
				operatorType = "自营"
			} else if strings.HasPrefix(tag.TagLabel, "非自营") {
				operatorType = "非自营"
			} else if strings.HasPrefix(tag.TagLabel, "互联") {
				operatorType = "互联"
			}
			tips = append(tips, tag.TagLabel)
		}
	}

	services := []string{}
	for _, service := range sdd.StationService {
		services = append(services, service.Name)
	}

	am := []map[string]any{
		{
			"id":                   rd,
			"jobId":                rd,
			"channel":              channel,
			"city":                 city,
			"province":             province,
			"stationId":            sdd.StationId,
			"stationName":          sdd.StationName,
			"stationAddr":          sdd.StationAddr,
			"operatorName":         sdd.OperatorName,
			"operatorType":         operatorType,
			"operatorTel":          sdd.OperatorTel,
			"siteOperator":         sdd.SiteOperator,
			"fastCharge":           sdd.FastCharge,
			"slowCharge":           sdd.SlowCharge,
			"lon":                  sdd.Lon,
			"lat":                  sdd.Lat,
			"stationLocation":      stationLocation, // 标识地上地下
			"businessTime":         sdd.BusinessTime,
			"parkFeeDesc":          sdd.ParkFeeDesc,
			"tips":                 strings.Join(tips, ","),
			"openRemark":           openRemark,
			"supportingFacilities": strings.Join(services, ","),
			"runTime":              ct,
		},
	}
	return am, nil
}

var periphralTypes = []struct {
	types string
	title string
}{
	{types: "00", title: "美食"},
	{types: "01", title: "便利店"},
	{types: "02", title: "洗手间"},
	{types: "03", title: "洗车"},
	{types: "04", title: "酒店"},
}

func (a *Api) GetStationDetail(p Param, requestOrder int64) ([]map[string]any, errors.Error) {
	lt, _ := strconv.ParseFloat(p.Latitude, 64)
	lg, _ := strconv.ParseFloat(p.Longitude, 64)

	originParams := StationDetailParams{
		StationID:      p.StationID,
		CoordinateType: "gaode",
		Lat:            lt,
		Lng:            lg,
		Source:         "wxsp",
		UserLevel:      "L1",
		CanDiscount:    "1",
		TagInfo:        []string{},
	}

	formData := util.NewRequestParams(originParams, a.accountInfo.Token, a.accountInfo.SSDI)
	reqParams, err := formData.GenerateParams()
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密失败")
	}

	reqJSON, err := json.Marshal(reqParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	sddParams := make(map[string]string)
	json.Unmarshal(reqJSON, &sddParams)

	headers := map[string]string{
		"Teld-RequestID": a.requestId,
		"Teld-RpcID":     "0." + strconv.FormatInt(int64(requestOrder), 10),
	}

	r, err := common.DoRequest(StationDetailURL, sddParams, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	json.Unmarshal(r.Content(), &sdr)

	// Token 无效时，直接返回错误，由上层处理 Token 刷新
	if sdr.State == "0" && sdr.ErrCode == TOKEN_INVALID_CODE {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString()))
	}
	if sdr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString()))
	}

	stationPeripheralAds := []map[string]any{}
	for _, item := range periphralTypes {
		p, err := a.GetStationPeripheralAds(p, requestOrder, item.types)
		if err != nil {
			logger.Error(errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString())))
			p = []map[string]any{}
		}
		stationPeripheralAds = append(stationPeripheralAds, map[string]any{
			"type":  item.title,
			"count": len(p),
		})
	}

	result, err := sdr.convert(a.targetDesc, a.gParam.Province, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点详情数据转换失败, 对端返回 %s", r.ContentToString()))
	}

	periphralJSON, err := json.Marshal(stationPeripheralAds)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点周边数据转换失败, 对端返回 %s", stationPeripheralAds))
	}
	result[0]["stationPeripheralAds"] = string(periphralJSON)
	return result, nil
}

type PeriphralParams struct {
	StationID string  `json:"StationID"`
	Lat       float64 `json:"Lng"`
	Lng       float64 `json:"Lat"`
	Types     string  `json:"Types"`
	ItemNum   int     `json:"ItemNum"`
}

type PeriphralData struct {
	Title    string `json:"Title"`
	Address  string `json:"Address"`
	Distance string `json:"Distance"`
}

type PeriphralResp struct {
	State   string          `json:"state"`
	ErrCode string          `json:"errcode"`
	ErrMsg  string          `json:"errmsg"`
	Data    []PeriphralData `json:"data"`
}

func (a *Api) GetStationPeripheralAds(p Param, requestOrder int64, types string) ([]map[string]any, errors.Error) {
	lt, _ := strconv.ParseFloat(p.Latitude, 64)
	lg, _ := strconv.ParseFloat(p.Longitude, 64)

	originParams := PeriphralParams{
		StationID: p.StationID,
		Lat:       lt,
		Lng:       lg,
		Types:     types,
		ItemNum:   100,
	}

	paramJSON, err := json.Marshal(originParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	formData := util.NewRequestParams(map[string]string{}, a.accountInfo.Token, a.accountInfo.SSDI)
	formData.Filter = string(paramJSON)

	reqJSON, err := json.Marshal(formData)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	spdParams := make(map[string]string)
	json.Unmarshal([]byte(reqJSON), &spdParams)

	headers := map[string]string{
		"Teld-RequestID": a.requestId,
		"Teld-RpcID":     "0." + strconv.FormatInt(int64(requestOrder), 10),
	}

	r, err := common.DoRequest(StationPeripheralURL, spdParams, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := PeriphralResp{}
	json.Unmarshal(r.Content(), &sdr)

	// Token 无效时，直接返回错误，由上层处理 Token 刷新
	if sdr.State == "0" && sdr.ErrCode == TOKEN_INVALID_CODE {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString()))
	}

	if sdr.State == "0" && sdr.ErrCode == INVALID_CONTEXT_CODE {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString()))
	}

	if sdr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString()))
	}

	result := []map[string]any{}
	for _, item := range sdr.Data {
		result = append(result, map[string]any{
			"title":    item.Title,
			"address":  item.Address,
			"distance": item.Distance,
		})
	}

	return result, nil
}
