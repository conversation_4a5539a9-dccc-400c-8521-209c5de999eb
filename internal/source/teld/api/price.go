package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/teld/api/common"
	"tianyan-crawler/internal/source/teld/api/util"

	"github.com/riete/errors"
)

const (
	StationPriceURL = "https://sg.teld.cn/api/invoke?SID=AAS-App0407_GetPriceInfoBySta"
)

type StationPriceParams struct {
	StationID    string   `json:"stationID"`
	RideShareTag string   `json:"rideShareTag"`
	Source       string   `json:"source"`
	TagInfo      []string `json:"tagInfo"`
}

type PriceData struct {
	// "electricPrice": "0.6651",
	// "servicePrice": "0.1849",
	// "plusElectricPrice": "0.6651",
	// "plusServicePrice": "0.1479",
	// "timeRange": "00:00-08:00",
	// "rangeType": "4",
	// "originalPrice": "",
	// "currentRange": false,
	// "originalElectricPrice": "",
	// "originalServicePrice": "",
	// "plusPrice": "",
	// "plusPriceV2": "0.8130",
	// "exclusivePrice": "",
	// "levelDiscount": "",
	// "priceTagName": ""
	TimeRange         string `json:"timeRange"`
	StartTime         string
	StopTime          string
	ElecPrice         float64 `json:"electricPrice,string"`
	ServicePrice      float64 `json:"servicePrice,string"`
	PlusElectricPrice float64 `json:"plusElectricPrice,string"`
	PlusServicePrice  float64 `json:"plusServicePrice,string"`
}

func (pd *PriceData) handleTime() error {
	times := strings.Split(pd.TimeRange, "-")
	if len(times) != 2 {
		return errors.New(fmt.Sprintf("price.TimeRange字符串格式不正确: %s", pd.TimeRange))
	}
	pd.StartTime = times[0]
	pd.StopTime = times[1]
	return nil
}

type StationPriceData struct {
	RangePrice []PriceData `json:"rangePrice"`
}

func (spd StationPriceData) handle(
	hoursPerDay int,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	for _, price := range spd.RangePrice {
		err := price.handleTime()
		if err != nil {
			return pd, err
		}

		startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
		if err != nil {
			return pd, err
		}
		stopTime, _ := time.Parse(time.TimeOnly, price.StopTime+":00")
		// 24:00:00 无法处理，所以错误忽略
		// if err != nil {
		// 	return pd, err
		// }

		start := startTime.Add(30 * time.Minute).Hour()
		end := stopTime.Add(30 * time.Minute).Hour()
		if end == 0 {
			end = hoursPerDay
		}

		totalPrice := price.ElecPrice + price.ServicePrice
		memberPrice := price.PlusElectricPrice + price.PlusServicePrice

		for i := start; i < end; i++ {
			pd[i] = map[string]any{
				"id":                 rd,
				"jobId":              rd,
				"stationId":          stationId,
				"stationName":        stationName,
				"city":               city,
				"province":           province,
				"stationLat":         latitude,
				"stationLng":         longitude,
				"accountType":        "普通用户",
				"crawlDay":           time.Now().Format("********"),
				"crawlTime":          time.Now().Format(time.TimeOnly),
				"channel":            targetDesc,
				"operName":           targetDesc,
				"pileType":           "3",
				"timeInterval":       fmt.Sprintf("%02d", i),
				"elecPrice":          strconv.FormatFloat(price.ElecPrice, 'f', -1, 64),
				"servicePrice":       strconv.FormatFloat(price.ServicePrice, 'f', -1, 64),
				"totalPrice":         strconv.FormatFloat(totalPrice, 'f', 4, 64),
				"memberElecPrice":    strconv.FormatFloat(price.PlusElectricPrice, 'f', -1, 64),
				"memberServicePrice": strconv.FormatFloat(price.PlusServicePrice, 'f', -1, 64),
				"memberPrice":        strconv.FormatFloat(memberPrice, 'f', 4, 64),
			}
		}
	}
	return pd, nil
}

type StationPriceResp struct {
	State   string             `json:"state"`
	ErrCode string             `json:"errcode"`
	ErrMsg  string             `json:"errmsg"`
	Data    []StationPriceData `json:"data"`
}

func (spr StationPriceResp) convert(
	targetDesc,
	province,
	city,
	stationId,
	stationName,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	result := []map[string]any{}
	// fastGun
	for _, pd := range spr.Data {
		if len(pd.RangePrice) != 0 {
			prices, err := pd.handle(
				hoursPerDay,
				targetDesc,
				stationId,
				stationName,
				city,
				province,
				latitude,
				longitude,
			)
			if err != nil {
				return result, err
			}
			result = append(result, prices...)
		}
	}

	return result, nil
}

func (a *Api) GetStationPrice(p Param, requestOrder int64) ([]map[string]any, errors.Error) {
	originParams := StationPriceParams{
		StationID:    p.StationID,
		RideShareTag: "",
		Source:       "wxsp",
		TagInfo:      []string{},
	}

	paramJSON, err := json.Marshal(originParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	formData := util.NewRequestParams(originParams, a.accountInfo.Token, a.accountInfo.SSDI)
	formData.Param = string(paramJSON)

	reqJSON, err := json.Marshal(formData)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	spdParams := make(map[string]string)
	json.Unmarshal(reqJSON, &spdParams)

	headers := map[string]string{
		"Teld-RequestID": a.requestId,
		"Teld-RpcID":     "0." + strconv.FormatInt(int64(requestOrder), 10),
	}

	r, err := common.DoRequest(StationPriceURL, spdParams, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	spr := StationPriceResp{}
	_ = json.Unmarshal(r.Content(), &spr)

	// Token 无效时，直接返回错误，由上层处理 Token 刷新
	if spr.State == "0" && spr.ErrCode == TOKEN_INVALID_CODE {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回: %s", r.ContentToString()))
	}

	if spr.State == "0" && spr.ErrCode == INVALID_CONTEXT_CODE {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回: %s", r.ContentToString()))
	}

	if spr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回: %s", r.ContentToString()))
	}

	result, err := spr.convert(
		a.targetDesc,
		a.gParam.Province,
		p.City,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
