package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/teld/api/common"
	"tianyan-crawler/internal/source/teld/api/util"

	"github.com/riete/errors"
)

const (
	ChargingGunURL = "https://sg.teld.cn/api/invoke?SID=AAS-App0603_GetTerminalOfStation"
	PowerRegExp    = `(\d+(\.\d+)?)`
	VoltageRegExp  = `(\d+)`
)

type ChargingGunParams struct {
	StationID string `json:"stationID"`
}

type ChargingGunData struct {
	GunId       string `json:"terminalId"`
	GunType     string `json:"terminalType"`
	OriginPower string `json:"power"`
	// Voltage     string `json:"voltage"`
	// ParkingNo   string `json:"park"`
}

type ChargingGunListData struct {
	List []ChargingGunData `json:"list"`
}

type ChargingGunResp struct {
	State   string `json:"state"`
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Data    string `json:"data"`
}

func (cgr ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	if cgr.Data == "" {
		return nil, errors.New(cgr.Data).Trace("响应结果 Data 为空")
	}
	r, err := util.DecryptData(cgr.Data)
	if err != nil {
		return nil, errors.New(cgr.Data).Trace(fmt.Sprintf("响应 Data 解密失败: %s", r))
	}

	cgd := ChargingGunListData{}
	json.Unmarshal([]byte(r), &cgd)

	guns := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgd.List {
		power := ""
		originPower := ""
		re := regexp.MustCompile(PowerRegExp)
		if gun.OriginPower != "" {
			matches := re.FindAllString(gun.OriginPower, -1)
			if len(matches) == 1 {
				power = matches[0]
			} else if len(matches) >= 2 {
				power = matches[1]
				originPower = matches[0] + "-" + matches[1]
			} else {
				return guns, errors.New(fmt.Sprintf("解析充电枪功率字段失败，未找到匹配项: %s", gun.OriginPower))
			}
		}

		// terminalType : "交流单相7kW"
		if power == "" {
			matches := re.FindAllString(gun.GunType, -1)
			if len(matches) >= 1 {
				power = matches[0]
			}
			// terminalType : "交流单相" power留空
		}

		// voltageLowerLimit := ""
		// voltageUpperLimit := ""
		// if gun.Voltage != "" {
		// 	re := regexp.MustCompile(VoltageRegExp)
		// 	matches := re.FindAllString(gun.Voltage, -1)
		// 	if len(matches) >= 2 {
		// 		voltageLowerLimit = matches[0]
		// 		voltageUpperLimit = matches[1]
		// 	} else {
		// 		return guns, errors.New(fmt.Sprintf("解析充电枪电压字段失败，未找到匹配项: %s", gun.Voltage))
		// 	}
		// }

		guns = append(guns, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       gun.GunId,
			"gunType":     gun.GunType,
			"originPower": originPower,
			"power":       power,
			"runTime":     ct,
			// "voltageLowerLimit": voltageLowerLimit,
			// "voltageUpperLimit": voltageUpperLimit,
			// "parkNo":            gun.ParkingNo,
		})
	}

	return guns, nil
}

func (a *Api) GetChargingGunList(p Param, requestOrder int64) ([]map[string]any, errors.Error) {
	originParams := ChargingGunParams{
		StationID: p.StationID,
	}

	formData := util.NewRequestParams(originParams, a.accountInfo.Token, a.accountInfo.SSDI)

	reqParams, err := formData.GenerateParams()
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密失败")
	}

	reqJSON, err := json.Marshal(reqParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	cglParams := make(map[string]string)
	json.Unmarshal([]byte(reqJSON), &cglParams)

	headers := map[string]string{
		"Teld-RequestID": a.requestId,
		"Teld-RpcID":     "0." + strconv.FormatInt(int64(requestOrder), 10),
	}

	r, err := common.DoRequest(ChargingGunURL, cglParams, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	cgr := new(ChargingGunResp)
	json.Unmarshal(r.Content(), cgr)

	// Token 无效时，直接返回错误，由上层处理 Token 刷新
	if cgr.State == "0" && cgr.ErrCode == TOKEN_INVALID_CODE {
		return nil, errors.New(fmt.Sprintf("获取充电枪列表数据失败, 对端返回: %s", r.ContentToString()))
	}

	if cgr.State == "0" && cgr.ErrCode == INVALID_CONTEXT_CODE {
		return nil, errors.New(fmt.Sprintf("获取充电枪列表数据失败, 对端返回: %s", r.ContentToString()))
	}

	if cgr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取充电枪列表数据失败, 对端返回: %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}
	return result, nil
}
