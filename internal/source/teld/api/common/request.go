package common

import (
	"net/http"
	"time"

	"tianyan-crawler/internal/common/proxy"
	"tianyan-crawler/internal/common/request"
)

var commonHeader = map[string]string{
	"Accept":          "*/*",
	"Accept-Language": "zh-CN,zh;q=0.9",
	"Connection":      "keep-alive",
	"Host":            "sg.teld.cn",
	"xweb_xhr":        "1",
	"x-sps-v":         "1.0",
	"TELDAppID":       "",
	"Content-Type":    "application/x-www-form-urlencoded",
	"Sec-Fetch-Site":  "cross-site",
	"Sec-Fetch-Mode":  "cors",
	"Sec-Fetch-Dest":  "empty",
	"User-Agent":      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF MacWechat/3.8.7(0x13080710) XWEB/1191",
}

func NewRequest(proxyIp proxy.ProxyIp, options ...request.Option) *request.Request {
	options = append(
		[]request.Option{
			request.WithDefaultClient(),
			request.WithTimeout(5 * time.Second),
			request.WithProxyFunc(http.ProxyURL(proxyIp.ProxyUrl())),
			request.WithHeader(commonHeader),
		},
		options...,
	)
	return request.NewRequest(options...)
}

func DoRequest(url string, data map[string]string, retry int, options ...request.Option) (*request.Request, error) {
	var r *request.Request
	var err error
	for i := 0; i < retry; i++ {
		r = NewRequest(proxy.GetIp(), options...)
		err = r.PostForm(url, data)
		if err != nil {
			time.Sleep(3 * time.Second)
		} else {
			break
		}
	}
	return r, err
}
