package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/request"
	"tianyan-crawler/internal/source/teld/api/common"
	"tianyan-crawler/internal/source/teld/api/util"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://sg.teld.cn/api/invoke?SID=AAS-App0600_SearchStation"
)

type StationListParams struct {
	Lat                 float64 `json:"lat"`
	Lng                 float64 `json:"lng"`
	LocationFilterType  string  `json:"locationFilterType"`
	LocationFilterValue int     `json:"locationFilterValue"`
	PageNum             int     `json:"pageNum"`
	ItemNumPerPage      int     `json:"itemNumPerPage"`
}

type StationData struct {
	StationId      string `json:"id"`
	StationName    string `json:"name"`
	Latitude       string `json:"lat"`
	Longitude      string `json:"lng"`
	FastCharge     int    `json:"fastTerminalNum"`
	FreeFastCharge string `json:"fastTerminalIdleNum"`
	SlowCharge     int    `json:"slowTerminalNum"`
	FreeSlowCharge string `json:"slowTerminalIdleNum"`
}

type StationListData struct {
	CurrentPage string        `json:"currentPage"`
	ItemCount   string        `json:"itemCount"`
	PageCount   string        `json:"pageCount"`
	Stations    []StationData `json:"stations"`
}

type StationListResp struct {
	State   string `json:"state"`
	ErrCode string `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Data    string `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) ([]map[string]any, error) {
	if slr.Data == "" {
		return nil, errors.New(slr.Data).Trace("响应结果 Data 为空")
	}
	r, err := util.DecryptData(slr.Data)
	if err != nil {
		return nil, errors.New(slr.Data).Trace(fmt.Sprintf("响应 Data 解密失败: %s", r))
	}

	sld := StationListData{}
	if err := json.Unmarshal([]byte(r), &sld); err != nil {
		return nil, errors.New(slr.Data).Trace(fmt.Sprintf("Data 反序列化失败: %s", err.Error()))
	}

	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range sld.Stations {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac, nil
}

func (a *Api) GetStationList(p Param, pageNum int, requestOrder int64) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)

	originParams := StationListParams{
		Lat:                 lat,
		Lng:                 lng,
		LocationFilterType:  "1",
		LocationFilterValue: 50,
		PageNum:             pageNum,
		ItemNumPerPage:      50,
	}

	formData := util.NewRequestParams(originParams, a.accountInfo.Token, a.accountInfo.SSDI)
	reqParams, err := formData.GenerateParams()
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密失败")
	}

	reqJSON, err := json.Marshal(reqParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	sldParams := make(map[string]string)
	json.Unmarshal([]byte(reqJSON), &sldParams)

	headers := map[string]string{
		"Teld-RequestID": a.requestId,
		"Teld-RpcID":     "0." + strconv.FormatInt(int64(requestOrder), 10),
	}

	r, err := common.DoRequest(StationListURL, sldParams, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	slr := StationListResp{}
	json.Unmarshal(r.Content(), &slr)

	// Token 无效时，直接返回错误，由上层处理 Token 刷新
	if slr.State == "0" && slr.ErrCode == TOKEN_INVALID_CODE {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	if slr.State == "0" && slr.ErrCode == INVALID_CONTEXT_CODE {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	if slr.State != "1" {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := slr.convert(a.targetDesc, p.CITY)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点列表数据转换失败, 对端返回 %s", r.ContentToString()))
	}
	return result, nil
}
