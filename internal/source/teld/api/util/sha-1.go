package util

import (
	"crypto/sha1"
	"encoding/hex"
	"strconv"
)

// TESDecrypt 加密拆解，核心为 sha-1
// 主要是明文和key的取法，拆解方法之后得出结果
// 明文为时间戳的话，那key结构为
// "yBb6fQbbiHx3g6Me" + "3ej8aNhjndsfj5Kwe/+" + X + "M3tGfMA0GiSqGlI" + 时间戳Str
// 其中 X 为，时间戳 与 时间戳各位之和 「取余」 作为 「索引」
// 「索引」的取值数组为
// [ 66, 65, 104, 100, 105, 50, 51, 56, 102, 106, 115, 107, 81, 85, 114, 55, 65, 65, 52, 71, 104, 48, 78, 65, 68, 67, 102, 106, 66, 105, 81, 75, 66, 103, 81, 97, 51, 68, 74, 53, 86, 118, 120, 69, 81, 99, 53, 110, 55, 100, 85, 74, 97, 56, 106, 71, 90, 101, 78, 119, 84, 117, 105, 66, 55, 78, 109, 122, 87, 107, 119, 70, 106, 108, 76, 106, 54, 117, 79, 108, 76, 100, 104, 57, 103, 50, 122, 55, 111, 51, 68, 54, 109, 122, 79, 57, 79, 118, 115, 67, 70, 55, 78, 109, 122, 87, 107, 122, 55, 111, 51, 68, 106, 119, 70, 106, 108, 76, 106, 54, 117, 79, 108, 76, 100, 104, 57, 103, 50, 122, 55, 111, 51, 68, 106, 54, 117, 79, 108, 54, 109, 122, 79, 57, 79, 118, 115, 67, 70 ]
// 将取出来的值作为 String.fromCharCode 的参数，取 ascii 对应字符

var KEY_MAP = [149]rune{66, 65, 104, 100, 105, 50, 51, 56, 102, 106, 115, 107, 81, 85, 114, 55, 65, 65, 52, 71, 104, 48, 78, 65, 68, 67, 102, 106, 66, 105, 81, 75, 66, 103, 81, 97, 51, 68, 74, 53, 86, 118, 120, 69, 81, 99, 53, 110, 55, 100, 85, 74, 97, 56, 106, 71, 90, 101, 78, 119, 84, 117, 105, 66, 55, 78, 109, 122, 87, 107, 119, 70, 106, 108, 76, 106, 54, 117, 79, 108, 76, 100, 104, 57, 103, 50, 122, 55, 111, 51, 68, 54, 109, 122, 79, 57, 79, 118, 115, 67, 70, 55, 78, 109, 122, 87, 107, 122, 55, 111, 51, 68, 106, 119, 70, 106, 108, 76, 106, 54, 117, 79, 108, 76, 100, 104, 57, 103, 50, 122, 55, 111, 51, 68, 106, 54, 117, 79, 108, 54, 109, 122, 79, 57, 79, 118, 115, 67, 70}

func generateKey(ts int) string {
	sum := 0
	for _, v := range strconv.Itoa(ts) {
		d, _ := strconv.Atoi(string(v))
		sum += d
	}
	index := ts % sum
	return string(KEY_MAP[index])
}

func SHA1(s string) string {
	o := sha1.New()
	o.Write([]byte(s))
	return hex.EncodeToString(o.Sum(nil))
}

func GenerateSVER(ts int) string {
	x := generateKey(ts)
	return SHA1("yBb6fQbbiHx3g6Me" + "3ej8aNhjndsfj5Kwe/+" + x + "M3tGfMA0GiSqGlI" + strconv.Itoa(ts))
}
