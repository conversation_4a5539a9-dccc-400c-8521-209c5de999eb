package teld

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/riete/errors"
	"github.com/riete/gpool"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/source/teld/api"
	"tianyan-crawler/internal/source/teld/api/util"
)

const (
	CrawlTarget     = "teld"
	CrawlTargetDesc = "特来电"
	DelayTime       = 3 * time.Second
)

var pool = gpool.NewLongTermPool(config.Config.TELDMaxConcurrence)

type ApiCommonResp struct {
	param api.Param
	data  []map[string]any
	err   errors.Error
}

type StationListApiResp struct {
	ApiCommonResp
}

type StationDetailApiResp struct {
	ApiCommonResp
	StationId string
}

type StationPriceApiResp struct {
	ApiCommonResp
	StationId string
}

type ChargingGunApiResp struct {
	ApiCommonResp
	StationId string
}

type Task struct {
	originMessage task.StartTaskMessage
	message       api.TaskMessage

	api    api.TELDApi
	ch     chan<- *task.TaskResponseMessage
	ctx    context.Context
	cancel context.CancelFunc

	chStationList   chan StationListApiResp
	chStationDetail chan StationDetailApiResp
	chStationPrice  chan StationPriceApiResp
	chChargingGun   chan ChargingGunApiResp
	existStations   map[string]bool
	requestOrder    int64

	mutex              sync.Mutex
	detailSuccess      int
	detailFailed       int
	priceSuccess       int
	priceFailed        int
	chargingGunSuccess int
	chargingGunFailed  int
}

func (t *Task) fetchStationList(p api.Param) {
	pageNum := 1
	total := 0
	for {
		select {
		case <-t.ctx.Done():
			return
		default:
			t.mutex.Lock()
			t.requestOrder += 1
			acr := ApiCommonResp{param: p}
			stations, err := t.api.GetStationList(p, pageNum, t.requestOrder)

			// 处理 Token 失效的情况
			if err != nil &&
				(strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.TOKEN_INVALID_CODE)) ||
					strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.INVALID_CONTEXT_CODE))) {
				logger.Info(fmt.Sprintf("任务ID: %s, Token 失效，尝试刷新", t.message.TaskInstanceId))
				times := 0
				var refreshErr error

				// 最多尝试3次
				for times < api.MAX_REFRESH_TOKEN_TIMES {
					times++

					// 获取新的 Token
					newAccRes, err := task.RefreshAccountInfo(CrawlTarget, CrawlTargetDesc)
					if err != nil {
						logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
						refreshErr = err
						// 失败后等待短暂时间再重试
						time.Sleep(1 * time.Second)
						continue
					}

					newAccountInfo := api.AccountInfo{}
					if jsonErr := json.Unmarshal(newAccRes, &newAccountInfo); jsonErr != nil {
						logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", jsonErr.Error())))
						refreshErr = jsonErr
						continue
					}

					// 更新 api 对象中的 accountInfo
					t.api.(*api.Api).UpdateAccountInfo(&newAccountInfo)

					logger.PostErrorLog(logger.ErrorLogMessage{
						RequestNo:   t.message.TaskInstanceId,
						RequestData: p,
						BizType:     t.message.GlobalParam.BizType,
						Channel:     t.message.Target,
						ErrorMsg:    fmt.Sprintf("用户凭证更新为: [SSDI: %s], [Token: %s]", newAccountInfo.SSDI, newAccountInfo.Token),
						Level:       logger.ERROR_LOG_MESSAGE_MAP.INFO.Type,
					})

					// 等待短暂时间让 Token 生效
					time.Sleep(500 * time.Millisecond)

					// 重新尝试获取站点列表
					stations, err = t.api.GetStationList(p, pageNum, t.requestOrder)

					// 即使有新 Token 依然失败，记录信息并继续尝试
					if err != nil &&
						(strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.TOKEN_INVALID_CODE)) ||
							strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.INVALID_CONTEXT_CODE))) {
						logger.Error(errors.New(fmt.Sprintf("即使获取了新 Token 依然失效: %s", err.Error())))
						refreshErr = err
						continue
					}

					// 成功获取数据
					refreshErr = nil
					break
				}

				if refreshErr != nil {
					logger.Error(errors.New(fmt.Sprintf("Token 刷新重试 %d 次后仍然失败: %s", times, refreshErr.Error())))
				}
			}

			t.mutex.Unlock()
			if err != nil {
				logger.Error(err.Trace(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下站点信息爬取失败", t.message.TaskInstanceId, p.Lat, p.Lgn)))
				logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下, 站点信息爬取失败", t.message.TaskInstanceId, p.Lat, p.Lgn))
				acr.err = err
				t.chStationList <- StationListApiResp{ApiCommonResp: acr}
				return
			}

			acr.data = stations
			t.chStationList <- StationListApiResp{ApiCommonResp: acr}
			if len(stations) > 0 {
				logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下, 第%d页爬取完成, 返回%d条数据", t.message.TaskInstanceId, p.Lat, p.Lgn, pageNum, len(stations)))
				total += len(stations)
				pageNum += 1
				continue
			}
			logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下总计%d个站点", t.message.TaskInstanceId, p.Lat, p.Lgn, total))
			return
		}
	}
}

func (t *Task) CrawlingStationList() {
	select {
	case <-t.ctx.Done():
		return
	default:
		totalTask := len(t.message.Params)
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(totalTask)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.fetchStationList(p)
				}(curParam)
				time.Sleep(DelayTime)
			}
			wg.Wait()
			close(t.chStationList)
		}()

		for i := range t.chStationList {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    fmt.Sprintf("(%s, %s) 经纬度下站点列表爬取失败", i.param.Lat, i.param.Lgn),
				})
				continue
			}

			if len(i.data) == 0 {
				logger.Info(fmt.Sprintf("任务ID: %s, 站点列表爬取结束, 站点列表为空", t.message.TaskInstanceId))
				continue
			}

			for _, siteInfo := range i.data {
				stationId := siteInfo["stationId"].(string)
				if stationId == "" {
					logger.PostErrorLog(logger.ErrorLogMessage{
						RequestNo:   t.message.TaskInstanceId,
						RequestData: i.param,
						BizType:     t.message.GlobalParam.BizType,
						Channel:     t.message.Target,
						StackTrace:  errors.New(fmt.Sprintf("任务ID: %s, 站点ID解析失败, [%v]", t.message.TaskInstanceId, siteInfo)).Stack(),
						ErrorMsg:    fmt.Sprintf("任务ID: %s, 站点ID解析失败, [%v]", t.message.TaskInstanceId, siteInfo),
					})
					continue
				}

				if t.existStations[stationId] {
					continue
				}

				t.existStations[stationId] = true
				resp := task.NewTaskResponseMessage(t.originMessage)
				resp.PrepareToSend(task.NewCrawlingResult(
					api.Param{}.ToAnyMap(),
					siteInfo,
				))
				t.ch <- resp
			}
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 站点列表爬取结束, 总计: %d 个", t.message.TaskInstanceId, len(t.existStations)))
	}
}

func (t *Task) fetchStationDetail(p api.Param) ([]map[string]any, errors.Error) {
	t.mutex.Lock()
	t.requestOrder += 1
	detail, err := t.api.GetStationDetail(p, t.requestOrder)

	// 处理 Token 失效的情况
	if err != nil &&
		(strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.TOKEN_INVALID_CODE)) ||
			strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.INVALID_CONTEXT_CODE))) {
		logger.Info(fmt.Sprintf("任务ID: %s, Token 失效，尝试刷新", t.message.TaskInstanceId))
		times := 0
		var refreshErr error

		// 最多尝试3次
		for times < api.MAX_REFRESH_TOKEN_TIMES {
			times++

			// 获取新的 Token
			newAccRes, err := task.RefreshAccountInfo(CrawlTarget, CrawlTargetDesc)
			if err != nil {
				logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
				refreshErr = err
				// 失败后等待短暂时间再重试
				time.Sleep(1 * time.Second)
				continue
			}

			newAccountInfo := api.AccountInfo{}
			if jsonErr := json.Unmarshal(newAccRes, &newAccountInfo); jsonErr != nil {
				logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", jsonErr.Error())))
				refreshErr = jsonErr
				continue
			}

			// 更新 api 对象中的 accountInfo
			t.api.(*api.Api).UpdateAccountInfo(&newAccountInfo)

			logger.PostErrorLog(logger.ErrorLogMessage{
				RequestNo:   t.message.TaskInstanceId,
				RequestData: p,
				BizType:     t.message.GlobalParam.BizType,
				Channel:     t.message.Target,
				ErrorMsg:    fmt.Sprintf("用户凭证更新为: [SSDI: %s], [Token: %s]", newAccountInfo.SSDI, newAccountInfo.Token),
				Level:       logger.ERROR_LOG_MESSAGE_MAP.INFO.Type,
			})

			// 等待短暂时间让 Token 生效
			time.Sleep(500 * time.Millisecond)

			// 重新尝试获取站点详情
			detail, err = t.api.GetStationDetail(p, t.requestOrder)

			// 即使有新 Token 依然失败，记录信息并继续尝试
			if err != nil &&
				(strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.TOKEN_INVALID_CODE)) ||
					strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.INVALID_CONTEXT_CODE))) {
				refreshErr = err
				continue
			}

			// 成功获取数据
			refreshErr = nil
			break
		}

		if refreshErr != nil {
			logger.Error(errors.New(fmt.Sprintf("Token 刷新重试 %d 次后仍然失败: %s", times, refreshErr.Error())))
		}
	}

	// 详情中还要多调用5次周边信息的接口
	t.requestOrder += 5
	if err != nil {
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点详情失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		t.detailFailed += 1
	} else {
		t.detailSuccess += 1
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点详情成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		json.Marshal(detail)
	}
	t.mutex.Unlock()
	return detail, err
}

func (t *Task) executeStationDetailCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		return
	default:
		errMsg := ""
		detail, err := t.fetchStationDetail(p)
		if err != nil {
			errMsg += err.Error()
		}
		t.chStationDetail <- StationDetailApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  detail,
				err:   err,
			},
			StationId: p.StationID,
		}
	}
}

func (t *Task) CrawlingStationDetail() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点详情信息, 去重后总计%d个站点", t.message.TaskInstanceId, total))
			for _, position := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeStationDetailCrawl(p)
				}(position)
				time.Sleep(DelayTime)
			}
			wg.Wait()
			close(t.chStationDetail)
		}()

		for i := range t.chStationDetail {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点详情失败",
				})
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点详情爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.detailSuccess, t.detailFailed, total))
	}
}

func (t *Task) fetchStationPrice(p api.Param) ([]map[string]any, errors.Error) {
	t.mutex.Lock()
	t.requestOrder += 1
	price, err := t.api.GetStationPrice(p, t.requestOrder)

	// 处理 Token 失效的情况
	if err != nil &&
		(strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.TOKEN_INVALID_CODE)) ||
			strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.INVALID_CONTEXT_CODE))) {
		logger.Info(fmt.Sprintf("任务ID: %s, Token 失效，尝试刷新", t.message.TaskInstanceId))
		times := 0
		var refreshErr error

		// 最多尝试3次
		for times < api.MAX_REFRESH_TOKEN_TIMES {
			times++

			// 获取新的 Token
			newAccRes, err := task.RefreshAccountInfo(CrawlTarget, CrawlTargetDesc)
			if err != nil {
				logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
				refreshErr = err
				// 失败后等待短暂时间再重试
				time.Sleep(1 * time.Second)
				continue
			}

			newAccountInfo := api.AccountInfo{}
			if jsonErr := json.Unmarshal(newAccRes, &newAccountInfo); jsonErr != nil {
				logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", jsonErr.Error())))
				refreshErr = jsonErr
				continue
			}

			// 更新 api 对象中的 accountInfo
			t.api.(*api.Api).UpdateAccountInfo(&newAccountInfo)

			logger.PostErrorLog(logger.ErrorLogMessage{
				RequestNo:   t.message.TaskInstanceId,
				RequestData: p,
				BizType:     t.message.GlobalParam.BizType,
				Channel:     t.message.Target,
				ErrorMsg:    fmt.Sprintf("用户凭证更新为: [SSDI: %s], [Token: %s]", newAccountInfo.SSDI, newAccountInfo.Token),
				Level:       logger.ERROR_LOG_MESSAGE_MAP.INFO.Type,
			})

			// 等待短暂时间让 Token 生效
			time.Sleep(500 * time.Millisecond)

			// 重新尝试获取站点价格
			price, err = t.api.GetStationPrice(p, t.requestOrder)

			// 即使有新 Token 依然失败，记录信息并继续尝试
			if err != nil &&
				(strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.TOKEN_INVALID_CODE)) ||
					strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.INVALID_CONTEXT_CODE))) {
				logger.Error(errors.New(fmt.Sprintf("即使获取了新 Token 依然失效: %s", err.Error())))
				refreshErr = err
				continue
			}

			// 成功获取数据
			refreshErr = nil
			break
		}

		if refreshErr != nil {
			logger.Error(errors.New(fmt.Sprintf("Token 刷新重试 %d 次后仍然失败: %s", times, refreshErr.Error())))
		}
	}

	if err != nil {
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点价格失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		t.priceFailed += 1
	} else {
		t.priceSuccess += 1
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点价格成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		json.Marshal(price)
	}
	t.mutex.Unlock()
	return price, err
}

func (t *Task) executeStationPriceCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		return
	default:
		errMsg := ""
		price, err := t.fetchStationPrice(p)
		if err != nil {
			errMsg += err.Error()
		}
		t.chStationPrice <- StationPriceApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  price,
				err:   err,
			},
			StationId: p.StationID,
		}
	}
}

func (t *Task) CrawlingStationPrice() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(len(t.message.Params))
			logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点价格信息, 去重后总计%d个站点", t.message.TaskInstanceId, total))
			for _, position := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeStationPriceCrawl(p)
				}(position)
				time.Sleep(DelayTime)
			}
			wg.Wait()
			close(t.chStationPrice)
		}()

		for i := range t.chStationPrice {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点价格失败",
				})
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点价格爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.priceSuccess, t.priceFailed, total))
	}
}

func (t *Task) fetchStationChargingGun(p api.Param) ([]map[string]any, errors.Error) {
	t.mutex.Lock()
	t.requestOrder += 1
	chargingGuns, err := t.api.GetChargingGunList(p, t.requestOrder)

	// 处理 Token 失效的情况
	if err != nil &&
		(strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.TOKEN_INVALID_CODE)) ||
			strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.INVALID_CONTEXT_CODE))) {
		logger.Info(fmt.Sprintf("任务ID: %s, Token 失效，尝试刷新", t.message.TaskInstanceId))
		times := 0
		var refreshErr error

		// 最多尝试3次
		for times < api.MAX_REFRESH_TOKEN_TIMES {
			times++

			// 获取新的 Token
			newAccRes, err := task.RefreshAccountInfo(CrawlTarget, CrawlTargetDesc)
			if err != nil {
				logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", err.Error())))
				refreshErr = err
				// 失败后等待短暂时间再重试
				time.Sleep(1 * time.Second)
				continue
			}

			newAccountInfo := api.AccountInfo{}
			if jsonErr := json.Unmarshal(newAccRes, &newAccountInfo); jsonErr != nil {
				logger.Error(errors.New(fmt.Sprintf("重新获取 Token 失败: %s", jsonErr.Error())))
				refreshErr = jsonErr
				continue
			}

			// 更新 api 对象中的 accountInfo
			t.api.(*api.Api).UpdateAccountInfo(&newAccountInfo)

			logger.PostErrorLog(logger.ErrorLogMessage{
				RequestNo:   t.message.TaskInstanceId,
				RequestData: p,
				BizType:     t.message.GlobalParam.BizType,
				Channel:     t.message.Target,
				ErrorMsg:    fmt.Sprintf("用户凭证更新为: [SSDI: %s], [Token: %s]", newAccountInfo.SSDI, newAccountInfo.Token),
				Level:       logger.ERROR_LOG_MESSAGE_MAP.INFO.Type,
			})

			// 等待短暂时间让 Token 生效
			time.Sleep(500 * time.Millisecond)

			// 重新尝试获取充电枪列表
			chargingGuns, err = t.api.GetChargingGunList(p, t.requestOrder)

			// 即使有新 Token 依然失败，记录信息并继续尝试
			if err != nil &&
				(strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.TOKEN_INVALID_CODE)) ||
					strings.Contains(err.Error(), fmt.Sprintf("\"errcode\":\"%s\"", api.INVALID_CONTEXT_CODE))) {
				logger.Error(errors.New(fmt.Sprintf("即使获取了新 Token 依然失效: %s", err.Error())))
				refreshErr = err
				continue
			}

			// 成功获取数据
			refreshErr = nil
			break
		}

		if refreshErr != nil {
			logger.Error(errors.New(fmt.Sprintf("Token 刷新重试 %d 次后仍然失败: %s", times, refreshErr.Error())))
		}
	}

	if err != nil {
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取充电枪失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		t.chargingGunFailed += 1
	} else {
		t.chargingGunSuccess += 1
		logger.Info(fmt.Sprintf("任务ID: %s, 获取充电枪成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		json.Marshal(chargingGuns)
	}
	t.mutex.Unlock()
	return chargingGuns, err
}

func (t *Task) executeStationChargingGunCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		return
	default:
		errMsg := ""
		chargingGuns, err := t.fetchStationChargingGun(p)
		if err != nil {
			errMsg += err.Error()
		}
		t.chChargingGun <- ChargingGunApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  chargingGuns,
				err:   err,
			},
			StationId: p.StationID,
		}
	}
}

func (t *Task) CrawlingStationChargingGun() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点充电枪信息, 去重后总计%d个站点", t.message.TaskInstanceId, total))
			for _, position := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeStationChargingGunCrawl(p)
				}(position)
				time.Sleep(DelayTime)
			}
			wg.Wait()
			close(t.chChargingGun)
		}()

		for i := range t.chChargingGun {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点充电枪列表失败",
				})
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点充电枪列表爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.chargingGunSuccess, t.chargingGunFailed, total))
	}
}

func (t *Task) Exec() {
	crawlingType := t.message.GlobalParam.BizType
	if crawlingType == "" {
		logger.Error(errors.New("[Task Error]: BizType 参数缺失"))
		return
	}

	switch crawlingType {
	case "STATION", "STATION_HOUR":
		t.CrawlingStationList()
	case "DETAIL":
		t.CrawlingStationDetail()
	case "PRICE":
		t.CrawlingStationPrice()
	case "CHARGING_GUN":
		t.CrawlingStationChargingGun()
	default:
		logger.Error(errors.New("[Task Error]: 不支持的爬虫类型"))
	}
}

func (t *Task) Cancel() {
	t.cancel()
}

func NewTELDCrawlingTask(stm task.StartTaskMessage, atmsg *api.TaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	ctx, cancel := context.WithCancel(context.Background())
	requestId := util.RandIntStr()
	return &Task{
		originMessage:   stm,
		message:         *atmsg,
		ctx:             ctx,
		cancel:          cancel,
		ch:              ch,
		api:             api.New(CrawlTarget, CrawlTargetDesc, requestId, stm.TaskInstanceId, atmsg),
		chStationList:   make(chan StationListApiResp),
		chStationDetail: make(chan StationDetailApiResp),
		chStationPrice:  make(chan StationPriceApiResp),
		chChargingGun:   make(chan ChargingGunApiResp),
		existStations:   make(map[string]bool),
		requestOrder:    0,
	}
}

func executor(stm task.StartTaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	msg := api.TaskMessage{}
	err := msg.Marshal(stm)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("[Task Error]: 解析任务参数错误 <%s>, %+v", err.Error(), stm)))
	}

	return NewTELDCrawlingTask(stm, &msg, ch)
}

func init() {
	task.SEMapping.Register(CrawlTarget, executor)
}
