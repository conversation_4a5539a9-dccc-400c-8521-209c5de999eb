package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/riete/errors"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/ecz/api/common"
)

const (
	StationDetailURL = "https://wxapp.e-chong.com/map/station/map/detail"
)

type StationDetailData struct {
	StationId    int    `json:"chargingStationId"`
	StationName  string `json:"stationName"`
	AddrCity     string `json:"addrCity"`
	AddrDistrict string `json:"addrDistrict"`
	AddrStreet   string `json:"addrStreet"`
	AddrBuilding string `json:"addrBuilding"`
	FastCharge   int    `json:"fastPileNums"`
	SlowCharge   int    `json:"mediumPileNums"`
	Lon          string `json:"longitude"`
	Lat          string `json:"latitude"`
	BusinessTime string `json:"busHoursDesc"`
	IsGroupOpen  int    `json:"isGroupOpen"`
	// 价格
	ElecPrice          float64 `json:"electFee,string"`
	ServicePrice       float64 `json:"serviceFee,string"`
	MemberElecPrice    float64 `json:"promoElectFee,string"`
	MemberServicePrice float64 `json:"promoServiceFee,string"`
}

type StationDetailResp struct {
	Code int               `json:"code"`
	Data StationDetailData `json:"data"`
}

func (sdr *StationDetailResp) handlePriceData(
	targetDesc,
	rd,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) (string, error) {
	pd := make([]map[string]any, 24)
	totalPrice := sdr.Data.ElecPrice + sdr.Data.ServicePrice
	memberTotalPrice := sdr.Data.MemberElecPrice + sdr.Data.MemberServicePrice
	for i := 0; i < 24; i++ {
		pd[i] = map[string]any{
			"id":                 rd,
			"jobId":              rd,
			"stationId":          stationId,
			"stationName":        stationName,
			"city":               city,
			"province":           province,
			"stationLat":         latitude,
			"stationLng":         longitude,
			"accountType":        "普通用户",
			"crawlDay":           time.Now().Format("********"),
			"crawlTime":          time.Now().Format(time.TimeOnly),
			"channel":            targetDesc,
			"operName":           targetDesc,
			"pileType":           "3",
			"timeInterval":       fmt.Sprintf("%02d", i),
			"elecPrice":          strconv.FormatFloat(sdr.Data.ElecPrice, 'f', 2, 64),
			"servicePrice":       strconv.FormatFloat(sdr.Data.ServicePrice, 'f', 2, 64),
			"totalPrice":         strconv.FormatFloat(totalPrice, 'f', 2, 64),
			"memberElecPrice":    strconv.FormatFloat(sdr.Data.MemberElecPrice, 'f', 2, 64),
			"memberServicePrice": strconv.FormatFloat(sdr.Data.MemberServicePrice, 'f', 2, 64),
			"memberPrice":        strconv.FormatFloat(memberTotalPrice, 'f', 2, 64),
		}
	}

	p, err := json.Marshal(pd)
	if err != nil {
		return "", err
	}

	return string(p), nil
}

func (sdr *StationDetailResp) convert(channel, province, city string) ([]map[string]any, error) {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)

	address := sdr.Data.AddrCity + sdr.Data.AddrDistrict + sdr.Data.AddrStreet + sdr.Data.AddrBuilding

	openRemark := ""
	if sdr.Data.IsGroupOpen == 0 {
		openRemark = "不对外开放"
	} else if sdr.Data.IsGroupOpen == 1 {
		openRemark = "对外开放"
	}

	price, err := sdr.handlePriceData(
		channel,
		rd,
		strconv.Itoa(sdr.Data.StationId),
		sdr.Data.StationName,
		city,
		province,
		sdr.Data.Lat,
		sdr.Data.Lon,
	)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("价格数据转换失败")
	}

	am := []map[string]any{
		{
			"id":           rd,
			"jobId":        rd,
			"channel":      channel,
			"city":         city,
			"province":     province,
			"stationId":    strconv.Itoa(sdr.Data.StationId),
			"stationName":  sdr.Data.StationName,
			"stationAddr":  address,
			"fastCharge":   sdr.Data.FastCharge,
			"slowCharge":   sdr.Data.SlowCharge,
			"lon":          sdr.Data.Lon,
			"lat":          sdr.Data.Lat,
			"businessTime": sdr.Data.BusinessTime,
			"openRemark":   openRemark,
			"price":        price,
			"runTime":      ct,
		},
	}
	return am, nil
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	body := map[string]string{
		"chargingStationId": p.StationID,
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化站点详情 Body 失败")
	}

	deviceId := uuid.New().String()
	headers := map[string]string{
		"PLAT_TYPE":      "wx",
		"PLAT_INFO":      "3.8.7",
		"REQ_ID":         strconv.Itoa(int(time.Now().UnixMilli())),
		"APP_VER":        "2.0.1",
		"USER_DEVICE_ID": deviceId,
		"CURRENT_CITY":   url.QueryEscape(p.City),
		"USER_TOKEN":     "",
	}
	r, resErr := common.DoRequest(StationDetailURL, jsonBody, 6, request.WithHeader(headers))
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	_ = json.Unmarshal(r.Content(), &sdr)
	if sdr.Code != 200 {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := sdr.convert(a.targetDesc, a.gParam.Province, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点详情数据转换失败, 对端返回 %s", r.ContentToString()))
	}

	return result, nil
}
