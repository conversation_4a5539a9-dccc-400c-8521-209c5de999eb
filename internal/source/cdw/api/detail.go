package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/cdw/api/common"
)

const (
	StationDetailURL = "https://miniappapi.cegncn.com/elephant/stations/user/info/info"
)

type StationDetailData struct {
	StationId         int    `json:"id"`
	StationName       string `json:"stationName"`
	StationAddr       string `json:"address"`
	OperatorName      string `json:"operatorName"`
	OperatorType      string `json:"stationTypeName"`
	OperatorTel       string `json:"contactPhone"`
	FastChargeCountVO struct {
		Total int `json:"total"`
	} `json:"fastChargeCountVO"`
	SlowChargeCountVO struct {
		Total int `json:"total"`
	} `json:"slowChargeCountVO"`
	Lon                string `json:"lng"`
	Lat                string `json:"lat"`
	BusinessTime       string `json:"businessTime"`
	ParkFeeDesc        string `json:"parkingFeeDesc"`
	TaxService         string `json:"invoiceOperatorName"`
	CategoryTypeName   string `json:"categoryTypeName"`
	ParkingFeeTypeName string `json:"parkingFeeTypeName"`
	ServiceTypeName    string `json:"serviceTypeName"`
	Features           []struct {
		StationFeatureName string `json:"stationFeatureName"`
	} `json:"features"`
	PriceInfoList []struct {
		PriceId      int `json:"priceId"`
		PriceOrtMode int `json:"priceOrtMode"`
	} `json:"priceInfoList"`
}

type StationDetailResp struct {
	Code int               `json:"code"`
	Data StationDetailData `json:"data"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) []map[string]any {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	facilities := []string{}
	for _, feature := range sdr.Data.Features {
		if feature.StationFeatureName != "" {
			facilities = append(facilities, feature.StationFeatureName)
		}
	}

	tips := []string{
		sdr.Data.CategoryTypeName,
		sdr.Data.ParkingFeeTypeName,
		sdr.Data.ServiceTypeName,
	}
	tips = append(tips, facilities...)

	pureTips := []string{}
	for _, tip := range tips {
		if tip != "" {
			pureTips = append(pureTips, tip)
		}
	}

	fastGunId := ""
	slowGunId := ""
	for _, priceInfo := range sdr.Data.PriceInfoList {
		if priceInfo.PriceOrtMode == 1 {
			slowGunId = strconv.Itoa(priceInfo.PriceId)
		}
		if priceInfo.PriceOrtMode == 2 {
			fastGunId = strconv.Itoa(priceInfo.PriceId)
		}
	}

	am := []map[string]any{
		{
			"id":                   rd,
			"jobId":                rd,
			"channel":              channel,
			"city":                 city,
			"province":             province,
			"stationId":            strconv.Itoa(sdr.Data.StationId),
			"stationName":          sdr.Data.StationName,
			"operatorName":         sdr.Data.OperatorName,
			"operatorType":         sdr.Data.OperatorType,
			"operatorTel":          sdr.Data.OperatorTel,
			"fastCharge":           sdr.Data.FastChargeCountVO.Total,
			"slowCharge":           sdr.Data.SlowChargeCountVO.Total,
			"lon":                  sdr.Data.Lon,
			"lat":                  sdr.Data.Lat,
			"businessTime":         sdr.Data.BusinessTime,
			"parkFeeDesc":          sdr.Data.ParkFeeDesc,
			"stationAddr":          sdr.Data.StationAddr,
			"taxService":           sdr.Data.TaxService,
			"supportingFacilities": strings.Join(facilities, ","),
			"tips":                 strings.Join(pureTips, ","),
			"fastGunId":            fastGunId,
			"slowGunId":            slowGunId,
			"runTime":              ct,
		},
	}
	return am
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	query := map[string]string{
		"id":  p.StationID,
		"lat": p.Latitude,
		"lng": p.Longitude,
	}

	r, resErr := common.GetRequest(StationDetailURL, query, 6)
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	_ = json.Unmarshal(r.Content(), &sdr)
	if sdr.Code != 0 {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sdr.convert(a.targetDesc, a.gParam.Province, p.City), nil
}
