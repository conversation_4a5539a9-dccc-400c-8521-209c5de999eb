package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/cdw/api/common"
)

const (
	PriceListURL = "https://miniappapi.cegncn.com/elephant/stations/user/info/price"
)

type PriceData struct {
	Time         string `json:"stage"`
	StartTime    string
	StopTime     string
	ChargePrice  string `json:"price"`
	ElecPrice    string `json:"electricPrice"`
	ServicePrice string `json:"servicePrice"`
}

func (pd *PriceData) handleTime() error {
	times := strings.Split(pd.Time, " ~ ")
	if len(times) != 2 {
		return errors.New(fmt.Sprintf("price.Time字符串格式不正确: %s", pd.Time))
	}
	pd.StartTime = times[0]
	pd.StopTime = times[1]
	return nil
}

type StationPriceResp struct {
	Code int         `json:"code"`
	Data []PriceData `json:"data"`
}

func (spr StationPriceResp) convert(
	targetDesc,
	stationId,
	stationName,
	latitude,
	longitude,
	province,
	city string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	r := []map[string]any{}
	if len(spr.Data) != 0 {
		dc, err := handlePriceData(
			spr.Data,
			hoursPerDay,
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return []map[string]any{}, err
		}
		r = append(r, dc...)
	}
	return r, nil
}

func handlePriceData(
	data []PriceData,
	hoursPerDay int,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	for _, price := range data {
		err := price.handleTime()
		if err != nil {
			return pd, err
		}

		startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
		if err != nil {
			return pd, err
		}
		stopTime, _ := time.Parse(time.TimeOnly, price.StopTime+":00")
		// 24:00:00 无法处理，所以错误忽略
		// if err != nil {
		// 	return pd, err
		// }

		start := startTime.Add(30 * time.Minute).Hour()
		end := stopTime.Add(30 * time.Minute).Hour()
		if end == 0 {
			end = hoursPerDay
		}

		for i := start; i < end; i++ {
			pd[i] = map[string]any{
				"id":           rd,
				"jobId":        rd,
				"stationId":    stationId,
				"stationName":  stationName,
				"city":         city,
				"province":     province,
				"stationLat":   latitude,
				"stationLng":   longitude,
				"accountType":  "普通用户",
				"crawlDay":     time.Now().Format("********"),
				"crawlTime":    time.Now().Format(time.TimeOnly),
				"channel":      targetDesc,
				"operName":     targetDesc,
				"pileType":     "3",
				"timeInterval": fmt.Sprintf("%02d", i),
				"elecPrice":    price.ElecPrice,
				"servicePrice": price.ServicePrice,
				"totalPrice":   price.ChargePrice,
			}
		}
	}
	return pd, nil
}

func (a Api) GetStationPrice(p Param, gunId, chargeType string) ([]map[string]any, errors.Error) {
	query := map[string]string{
		"stationId":        p.StationID,
		"contractId":       "",
		"pileId":           gunId,
		"priceId":          gunId,
		"priceOrtMode":     chargeType,
		"gunChargeType":    "",
		"dataFrom":         p.BusinessId, // 站点 dataFrom 字段
		"chargeCustomerId": "",
	}

	r, err := common.GetRequest(PriceListURL, query, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	spr := new(StationPriceResp)
	_ = json.Unmarshal(r.Content(), spr)
	if spr.Code != 0 {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := spr.convert(
		a.targetDesc,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
		a.gParam.Province,
		p.City,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
