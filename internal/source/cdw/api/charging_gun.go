package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/cdw/api/common"
)

const (
	ChargingGunURL = "https://miniappapi.cegncn.com/elephant/stations/user/info/piles"
	VoltageRegExp  = `(\d+)`
)

type ChargingGunData struct {
	PowerRange   string `json:"powerRange"`
	VoltageRange string `json:"voltageRange"`
	Guns         []struct {
		GunId     string `json:"connectorId"`
		GunType   string `json:"ortModeName"`
		RatePower string `json:"ratePower"`
	} `json:"guns"`
}

type ChargingGunResp struct {
	Code int `json:"code"`
	Data struct {
		List []ChargingGunData `json:"list"`
	} `json:"data"`
}

func (cgr *ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	r := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, d := range cgr.Data.List {
		for _, gun := range d.Guns {
			originPower := ""
			if gun.RatePower != "" {
				originPower = gun.RatePower[:len(gun.RatePower)-2]
			}

			re := regexp.MustCompile(VoltageRegExp)
			matches := re.FindAllString(d.VoltageRange, -1)

			voltageLowerLimit := ""
			voltageUpperLimit := ""
			if len(matches) >= 2 {
				voltageLowerLimit = matches[0]
				voltageUpperLimit = matches[1]
			} else {
				return r, errors.New(fmt.Sprintf("解析充电枪电压字段失败，未找到匹配项: %s", d.VoltageRange))
			}

			r = append(r, map[string]any{
				"channel":           targetDesc,
				"id":                rd,
				"jobId":             rd,
				"stationId":         stationId,
				"stationName":       stationName,
				"city":              city,
				"gunId":             gun.GunId,
				"gunType":           gun.GunType,
				"originPower":       originPower,
				"power":             originPower,
				"voltageLowerLimit": voltageLowerLimit,
				"voltageUpperLimit": voltageUpperLimit,
				"runTime":           ct,
			})
		}

	}

	return r, nil
}

func (a Api) GetChargingGunList(p Param, pageNum int, chargeType string) ([]map[string]any, errors.Error) {
	query := map[string]string{
		"stationId": p.StationID,
		"pageNum":   strconv.Itoa(pageNum),
		"pageSize":  "10",
		"condition": chargeType,
	}

	r, err := common.GetRequest(ChargingGunURL, query, 6)
	if err != nil {
		return []map[string]any{}, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return []map[string]any{}, errors.New(status).Trace(fmt.Sprintf("获取站点充电枪数据失败, http响应: %s", status))
	}

	cgr := ChargingGunResp{}
	_ = json.Unmarshal(r.Content(), &cgr)
	if cgr.Code != 0 {
		return []map[string]any{}, errors.New(fmt.Sprintf("获取站点充电枪数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}

	return result, nil
}
