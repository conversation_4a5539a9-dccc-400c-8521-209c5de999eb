package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/internal/source/cdw/api/common"
)

const (
	StationListURL = "https://miniappapi.cegncn.com/elephant/stations/user/info"
)

type StationListData struct {
	StationId   int64  `json:"id"`
	StationName string `json:"stationName"`
	Latitude    string `json:"lat"`
	Longitude   string `json:"lng"`
	DataFrom    int    `json:"dataFrom"` // 业务字段, 写入 businessId, 查价格用
	PriceInfo   struct {
		PriceId      int `json:"priceId"`
		PriceOrtMode int `json:"priceOrtMode"`
	} `json:"priceInfo"`
	SlowChargeCountVO struct {
		Total int `json:"total"`
		Idle  int `json:"idle"`
	} `json:"slowChargeCountVO"`
	FastChargeCountVO struct {
		Total int `json:"total"`
		Idle  int `json:"idle"`
	} `json:"fastChargeCountVO"`
}

type StationListResp struct {
	Code int `json:"code"`
	Data struct {
		List     []StationListData `json:"list"`
		Total    int               `json:"total"`
		PageNum  int               `json:"pageNum"`
		PageSize int               `json:"pageSize"`
		Pages    int               `json:"pages"`
	} `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Data.List {
		fastGunId := ""
		slowGunId := ""
		if d.PriceInfo.PriceOrtMode == 1 {
			slowGunId = strconv.Itoa(d.PriceInfo.PriceId)
		}
		if d.PriceInfo.PriceOrtMode == 2 {
			fastGunId = strconv.Itoa(d.PriceInfo.PriceId)
		}
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      strconv.FormatInt(d.StationId, 10),
			"stationName":    d.StationName,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"businessId":     d.DataFrom,
			"fastGunId":      fastGunId,
			"slowGunId":      slowGunId,
			"fastCharge":     d.FastChargeCountVO.Total,
			"freeFastCharge": d.FastChargeCountVO.Idle,
			"slowCharge":     d.SlowChargeCountVO.Total,
			"freeSlowCharge": d.SlowChargeCountVO.Idle,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error) {
	query := map[string]string{
		"lng":         p.Lgn,
		"lat":         p.Lat,
		"locationLng": p.Lgn,
		"locationLat": p.Lat,
		"pageNum":     strconv.Itoa(pageNum),
		"pageSize":    "10",
		"distance":    "50",
		"stationType": "1",
	}

	r, err := common.GetRequest(StationListURL, query, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := StationListResp{}
	_ = json.Unmarshal(r.Content(), &sr)
	if sr.Code != 0 {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.targetDesc, p.CITY), nil
}
