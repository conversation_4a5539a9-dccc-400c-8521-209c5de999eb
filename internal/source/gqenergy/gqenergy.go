package gqenergy

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/riete/errors"
	"github.com/riete/gpool"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/source/gqenergy/api"
)

const (
	CrawlTarget     = "gqenergy"
	CrawlTargetDesc = "广汽能源"
	DelayTime       = 3 * time.Second
)

var pool = gpool.NewLongTermPool(config.Config.GQEnergyMaxConcurrence)

type ApiCommonResp struct {
	param api.Param
	data  []map[string]any
	err   errors.Error
}

type StationListApiResp struct {
	ApiCommonResp
}

type StationDetailApiResp struct {
	ApiCommonResp
	stationId string
}

type StationPriceApiResp struct {
	ApiCommonResp
	stationId string
}

type ChargingGunApiResp struct {
	ApiCommonResp
	stationId string
}

type Task struct {
	originMessage task.StartTaskMessage
	message       api.TaskMessage

	api    api.GQEnergyApi
	ch     chan<- *task.TaskResponseMessage
	ctx    context.Context
	cancel context.CancelFunc

	chStationList   chan StationListApiResp
	chStationDetail chan StationDetailApiResp
	chStationPrice  chan StationPriceApiResp
	chChargingGun   chan ChargingGunApiResp
	existStation    map[string]bool

	mutex              sync.Mutex
	detailSuccess      int
	detailFailed       int
	priceSuccess       int
	priceFailed        int
	chargingGunSuccess int
	chargingGunFailed  int
}

func (t *Task) fetchStationList(p api.Param) {
	pageNum := 1
	total := 0
	for {
		select {
		case <-t.ctx.Done():
			return
		default:
			acr := ApiCommonResp{param: p}
			stations, err := t.api.GetStationList(p, pageNum)
			if err != nil {
				logger.Error(err.Trace(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下站点信息爬取失败", t.message.TaskInstanceId, p.Lat, p.Lgn)))
				logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下, 站点信息爬取失败", t.message.TaskInstanceId, p.Lat, p.Lgn))
				acr.err = err
				t.chStationList <- StationListApiResp{ApiCommonResp: acr}
				return
			}

			acr.data = stations
			t.chStationList <- StationListApiResp{ApiCommonResp: acr}
			if len(stations) > 0 {
				logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下, 第%d页爬取完成, 返回%d条数据", t.message.TaskInstanceId, p.Lat, p.Lgn, pageNum, len(stations)))
				total += len(stations)
				pageNum += 1
				continue
			}
			logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下总计%d个站点", t.message.TaskInstanceId, p.Lat, p.Lgn, total))
			return
		}
	}
}

func (t *Task) CrawlingStationList() {
	select {
	case <-t.ctx.Done():
		return
	default:
		totalTask := len(t.message.Params)
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(totalTask)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.fetchStationList(p)
				}(curParam)
				time.Sleep(DelayTime)
			}
			wg.Wait()
			close(t.chStationList)
		}()

		for i := range t.chStationList {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    fmt.Sprintf("(%s, %s) 经纬度下站点列表爬取失败", i.param.Lat, i.param.Lgn),
				})
				continue
			}

			if len(i.data) == 0 {
				logger.Info(fmt.Sprintf("任务ID: %s, 站点列表爬取结束, 站点列表为空", t.message.TaskInstanceId))
				continue
			}

			for _, siteInfo := range i.data {
				stationId := siteInfo["stationId"].(string)
				if stationId == "" {
					logger.PostErrorLog(logger.ErrorLogMessage{
						RequestNo:   t.message.TaskInstanceId,
						RequestData: i.param,
						BizType:     t.message.GlobalParam.BizType,
						Channel:     t.message.Target,
						StackTrace:  errors.New(fmt.Sprintf("任务ID: %s, 站点ID解析失败, [%v]", t.message.TaskInstanceId, siteInfo)).Stack(),
						ErrorMsg:    fmt.Sprintf("任务ID: %s, 站点ID解析失败, [%v]", t.message.TaskInstanceId, siteInfo),
					})
					continue
				}

				if t.existStation[stationId] {
					continue
				}

				t.existStation[stationId] = true
				resp := task.NewTaskResponseMessage(t.originMessage)
				resp.PrepareToSend(task.NewCrawlingResult(
					api.Param{}.ToAnyMap(),
					siteInfo,
				))
				t.ch <- resp
			}
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 站点列表爬取结束, 总计: %d 个", t.message.TaskInstanceId, len(t.existStation)))
	}
}

func (t *Task) fetchStationDetail(p api.Param) (map[string]any, errors.Error) {
	detail, err := t.api.GetStationDetail(p)
	if err != nil {
		t.mutex.Lock()
		t.detailFailed += 1
		t.mutex.Unlock()
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点详情失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	} else {
		t.mutex.Lock()
		t.detailSuccess += 1
		t.mutex.Unlock()
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点详情成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	}
	return detail, err
}

func (t *Task) executeSiteDetailCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		logger.Info(fmt.Sprintf("任务ID: %s, 取消, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		return
	default:
		detail, err := t.fetchStationDetail(p)
		t.chStationDetail <- StationDetailApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  []map[string]any{detail},
				err:   err,
			},
			stationId: p.StationID,
		}
	}
}

func (t *Task) CrawlingStationDetail() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点详情, 去重后总计%d个站点", t.message.TaskInstanceId, total))
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeSiteDetailCrawl(p)
				}(curParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chStationDetail)
		}()

		for i := range t.chStationDetail {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点详情失败",
				})
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点详情爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.detailSuccess, t.detailFailed, total))
	}
}

var ChargeTypeMap = struct {
	Fast string
	Slow string
}{
	Fast: "1",
	Slow: "2",
}

func (t *Task) fetchStationPrice(p api.Param) ([]map[string]any, errors.Error) {
	prices := []map[string]any{}

	fastPrices, fErr := t.api.GetStationPrice(ChargeTypeMap.Fast, p)
	if fErr != nil {
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点快枪价格失败, [%s - %s]", t.message.TaskInstanceId, p.CzbStationId, p.StationName))
	} else {
		prices = append(prices, fastPrices...)
	}

	slowPrices, sErr := t.api.GetStationPrice(ChargeTypeMap.Slow, p)
	if sErr != nil {
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点慢枪价格失败, [%s - %s]", t.message.TaskInstanceId, p.CzbStationId, p.StationName))
	} else {
		prices = append(prices, slowPrices...)
	}

	if fErr != nil && sErr != nil {
		t.mutex.Lock()
		t.priceFailed += 1
		t.mutex.Unlock()
		combinedErr := fmt.Errorf("%v + %v", fErr, sErr)
		logger.Error(errors.New(combinedErr.Error()))
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点价格失败, [%s - %s]", t.message.TaskInstanceId, p.CzbStationId, p.StationName))
		return prices, errors.New(combinedErr.Error())
	}

	t.mutex.Lock()
	t.priceSuccess += 1
	t.mutex.Unlock()
	logger.Info(fmt.Sprintf("任务ID: %s, 获取站点价格成功, [%s - %s]", t.message.TaskInstanceId, p.CzbStationId, p.StationName))
	return prices, nil
}

func (t *Task) executeStationPriceCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		logger.Info(fmt.Sprintf("任务ID: %s, 取消, [%s - %s]", t.message.TaskInstanceId, p.CzbStationId, p.StationName))
		return
	default:
		price, err := t.fetchStationPrice(p)
		t.chStationPrice <- StationPriceApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  price,
				err:   err,
			},
			stationId: p.CzbStationId,
		}
	}
}

func (t *Task) CrawlingStationPrice() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点价格信息, 去重后总计%d个站点", t.message.TaskInstanceId, total))
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeStationPriceCrawl(p)
				}(curParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chStationPrice)
		}()

		for i := range t.chStationPrice {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点价格失败",
				})
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点价格爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.priceSuccess, t.priceFailed, total))
	}
}

func (t *Task) fetchChargingGun(p api.Param) ([]map[string]any, errors.Error) {
	chargingGun, err := t.api.GetChargingGunList(p)
	if err != nil {
		t.mutex.Lock()
		t.chargingGunFailed += 1
		t.mutex.Unlock()
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点充电枪列表失败, [%s - %s]", t.message.TaskInstanceId, p.CzbStationId, p.StationName))
	} else {
		t.mutex.Lock()
		t.chargingGunSuccess += 1
		t.mutex.Unlock()
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点充电枪列表成功, [%s - %s]", t.message.TaskInstanceId, p.CzbStationId, p.StationName))
	}
	return chargingGun, err
}

func (t *Task) executeChargingGunCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		logger.Info(fmt.Sprintf("任务ID: %s, 取消, [%s - %s]", t.message.TaskInstanceId, p.CzbStationId, p.StationName))
		return
	default:
		chargingGun, err := t.fetchChargingGun(p)
		t.chChargingGun <- ChargingGunApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  chargingGun,
				err:   err,
			},
			stationId: p.CzbStationId,
		}
	}
}

func (t *Task) CrawlingChargingGun() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点充电枪列表, 去重后总计%d个站点", t.message.TaskInstanceId, total))
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeChargingGunCrawl(p)
				}(curParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chChargingGun)
		}()

		for i := range t.chChargingGun {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点充电枪列表失败",
				})
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点充电枪列表爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.chargingGunSuccess, t.chargingGunFailed, total))
	}
}

func (t *Task) Exec() {
	crawlingType := t.message.GlobalParam.BizType
	if crawlingType == "" {
		logger.Error(errors.New("[Task Error]: BizType 参数缺失"))
		return
	}

	switch crawlingType {
	case "STATION":
		t.CrawlingStationList()
	case "DETAIL":
		t.CrawlingStationDetail()
	case "PRICE":
		t.CrawlingStationPrice()
	case "CHARGING_GUN":
		t.CrawlingChargingGun()
	default:
		logger.Error(errors.New("[Task Error]: 不支持的爬虫类型"))
	}
}

func (t *Task) Cancel() {
	t.cancel()
}

func NewCrawlingTask(stm task.StartTaskMessage, atmsg api.TaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	ctx, cancel := context.WithCancel(context.Background())
	return &Task{
		originMessage:   stm,
		message:         atmsg,
		ctx:             ctx,
		cancel:          cancel,
		ch:              ch,
		api:             api.New(CrawlTargetDesc, atmsg),
		chStationList:   make(chan StationListApiResp),
		chStationDetail: make(chan StationDetailApiResp),
		chStationPrice:  make(chan StationPriceApiResp),
		chChargingGun:   make(chan ChargingGunApiResp),
		existStation:    make(map[string]bool),
	}
}

func executor(stm task.StartTaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	msg := api.TaskMessage{}
	err := msg.Marshal(stm)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("[Task Error]: 解析任务参数错误 <%s>, %+v", err.Error(), stm)))
	}

	return NewCrawlingTask(stm, msg, ch)
}

func init() {
	task.SEMapping.Register(CrawlTarget, executor)
}
