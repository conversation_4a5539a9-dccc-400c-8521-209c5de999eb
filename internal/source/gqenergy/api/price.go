package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/dkcloud/api/common"
	"tianyan-crawler/internal/source/dkcloud/api/util"
)

const (
	PriceListURL = "https://mpcs.dazzlesky.com/services/v4/wechat/zhPolicyList"
)

type PriceData struct {
	StartTime    string  `json:"startTime"`
	StopTime     string  `json:"endTime"`
	ElecPrice    float64 `json:"originalElecPrice"`
	ServicePrice float64 `json:"originalServicePrice"`
	TotalPrice   float64 `json:"originalTotalPrice"`
}

type StationPriceResp struct {
	Code    float32     `json:"code"`
	Message string      `json:"message"`
	Result  []PriceData `json:"result"`
}

func (spr StationPriceResp) convert(
	targetDesc,
	chargeType,
	stationId,
	stationName,
	latitude,
	longitude,
	province,
	city string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	r := []map[string]any{}
	if len(spr.Result) != 0 {
		dc, err := handlePriceData(
			spr.Result,
			hoursPerDay,
			chargeType,
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return []map[string]any{}, err
		}
		r = append(r, dc...)
	}

	return r, nil
}

var PileTypeMap = map[string]string{
	"fastGun": "1",
	"slowGun": "2",
}

func handlePriceData(
	data []PriceData,
	hoursPerDay int,
	pileType,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	for _, price := range data {
		startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
		if err != nil {
			return pd, err
		}
		stopTime, _ := time.Parse(time.TimeOnly, price.StopTime+":00")
		// 24:00:00 无法处理，所以错误忽略
		// if err != nil {
		// 	return pd, err
		// }

		start := startTime.Add(30 * time.Minute).Hour()
		end := stopTime.Add(30 * time.Minute).Hour()
		if end == 0 {
			end = hoursPerDay
		}

		for i := start; i < end; i++ {
			pd[i] = map[string]any{
				"id":           rd,
				"jobId":        rd,
				"stationId":    stationId,
				"stationName":  stationName,
				"city":         city,
				"province":     province,
				"stationLat":   latitude,
				"stationLng":   longitude,
				"accountType":  "普通用户",
				"crawlDay":     time.Now().Format("********"),
				"crawlTime":    time.Now().Format(time.TimeOnly),
				"channel":      targetDesc,
				"operName":     targetDesc,
				"pileType":     pileType,
				"timeInterval": fmt.Sprintf("%02d", i),
				"elecPrice":    strconv.FormatFloat(price.ElecPrice, 'f', -1, 64),
				"servicePrice": strconv.FormatFloat(price.ServicePrice, 'f', -1, 64),
				"totalPrice":   strconv.FormatFloat(price.TotalPrice, 'f', -1, 64),
			}
		}
	}
	return pd, nil
}

func (a Api) GetStationPrice(chargeType string, p Param) ([]map[string]any, errors.Error) {
	ts := int(time.Now().UnixMicro() / 1e3)
	body := map[string]string{
		"czbStationId": p.CzbStationId,
		"chargeType":   chargeType,
		"token":        "",
		"platformType": "3",
		"app_key":      AppKey,
		"timestamp":    strconv.Itoa(ts),
	}

	body["sign"] = util.Sign(body, AppSecret)

	r, err := common.DoRequest(PriceListURL, body, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	spr := new(StationPriceResp)
	_ = json.Unmarshal(r.Content(), spr)
	if spr.Code != 200 {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := spr.convert(
		a.targetDesc,
		chargeType,
		p.CzbStationId,
		p.StationName,
		p.Latitude,
		p.Longitude,
		a.gParam.Province,
		p.City,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
