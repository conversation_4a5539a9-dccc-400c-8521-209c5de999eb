package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/dkcloud/api/common"
	"tianyan-crawler/internal/source/dkcloud/api/util"
)

const (
	ChargingGunURL = "https://mpcs.dazzlesky.com/services/v3/chargebase/getCzbConnectorInfoList"
)

type ChargingGunResp struct {
	Code    float32           `json:"code"`
	Message string            `json:"message"`
	Result  []ChargingGunData `json:"result"`
}

func (cgr *ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	r := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgr.Result {
		power := ""
		if gun.OutPower != "" {
			power = gun.OutPower[:len(gun.OutPower)-2]
		}
		r = append(r, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       gun.GunId,
			"gunType":     gun.GunType,
			"power":       power,
			"runTime":     ct,
		})
	}

	return r, nil
}

type ChargingGunData struct {
	GunId    string `json:"czbConnectorId"`
	GunType  string `json:"czbConnectorTypeName"`
	OutPower string `json:"powerDesc"`
}

func (a Api) GetChargingGunList(p Param) ([]map[string]any, errors.Error) {
	ts := int(time.Now().UnixMicro() / 1e3)
	body := map[string]string{
		"czbStationId": p.CzbStationId,
		"userLatStr":   p.Latitude,
		"userLngStr":   p.Longitude,
		"token":        "",
		"platformType": "3",
		"app_key":      AppKey,
		"timestamp":    strconv.Itoa(ts),
	}

	body["sign"] = util.Sign(body, AppSecret)

	r, err := common.DoRequest(ChargingGunURL, body, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点充电枪数据失败, http响应: %s", status))
	}

	cgr := ChargingGunResp{}
	_ = json.Unmarshal(r.Content(), &cgr)
	if cgr.Code != 200 {
		return nil, errors.New(fmt.Sprintf("获取站点充电枪数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.CzbStationId, p.StationName, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}

	return result, nil
}
