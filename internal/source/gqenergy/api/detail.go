package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/gqenergy/api/common"
)

const (
	StationDetailURL = "https://charge-customer.gacne.com.cn/api/station/optimize/detail"
)

type StationDetailData struct {
	StationId            string   `json:"stationId"`
	StationName          string   `json:"stationName"`
	StationAddr          string   `json:"address"`
	OperatorId           string   `json:"shopId"`   // 运营商ID
	OperatorName         string   `json:"shopName"` // 运营商名称
	OperatorTel          string   `json:"serviceTel"`
	OverChargeTotalCount string   `json:"overChargeTotalCount"` // 超快枪
	DirectGunTotalCount  string   `json:"directGunTotalCount"`  // 快枪
	SlowCharge           string   `json:"exchangeGunTotalCount"`
	Lon                  string   `json:"longitude"`
	Lat                  string   `json:"latitude"`
	BusinessTime         string   `json:"openTime"` // 营运时间
	ParkFeeDesc          string   `json:"parkingRate"`
	Tags                 []string `json:"tags"`
}

type StationDetailResp struct {
	Code    string            `json:"code"`
	Message string            `json:"message"`
	Data    StationDetailData `json:"data"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) map[string]any {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	otc, _ := strconv.Atoi(sdr.Data.OverChargeTotalCount)
	dtc, _ := strconv.Atoi(sdr.Data.DirectGunTotalCount)
	fastChargerCount := otc + dtc

	if sdr.Data.SlowCharge == "" {
		sdr.Data.SlowCharge = "0"
	}
	am := map[string]any{
		"id":           rd,
		"jobId":        rd,
		"channel":      channel,
		"city":         city,
		"province":     province,
		"stationId":    sdr.Data.StationId,
		"stationName":  sdr.Data.StationName,
		"stationAddr":  sdr.Data.StationAddr,
		"operatorId":   sdr.Data.OperatorId,
		"operatorName": sdr.Data.OperatorName,
		"operatorTel":  sdr.Data.OperatorTel,
		"fastCharge":   strconv.Itoa(fastChargerCount),
		"slowCharge":   sdr.Data.SlowCharge,
		"lon":          sdr.Data.Lon,
		"lat":          sdr.Data.Lat,
		"businessTime": sdr.Data.BusinessTime,
		"parkFeeDesc":  sdr.Data.ParkFeeDesc,
		"tips":         strings.Join(sdr.Data.Tags, ","),
		"runTime":      ct,
	}
	return am
}

type StationDetailRequestBody struct {
	StationID string `json:"stationId"`
}

func (a Api) GetStationDetail(p Param) (map[string]any, errors.Error) {
	body := StationDetailRequestBody{
		StationID: p.StationID,
	}

	bJSON, err := json.Marshal(&body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化站点详情Body失败")
	}

	headers := map[string]string{
		"Content-type": "application/json;charset=UTF-8",
		"client":       "",
		"x-tag":        "flyio",
		"reqsource":    "Wechat",
		"v_num":        "",
		"phpsessid":    "",
		"origincharge": "MINIAPP",
		"mobile_type":  "",
	}

	r, resErr := common.DoRequest(StationDetailURL, bJSON, 6, request.WithHeader(headers))
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	_ = json.Unmarshal(r.Content(), &sdr)
	if sdr.Code != "0000" {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sdr.convert(a.targetDesc, a.gParam.Province, p.City), nil
}
