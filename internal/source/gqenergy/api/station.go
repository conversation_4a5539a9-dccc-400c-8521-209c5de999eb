package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/internal/common/request"
	"tianyan-crawler/internal/source/gqenergy/api/common"
)

const (
	StationListURL = "https://charge-customer.gacne.com.cn/api/station/optimize/getMultiConditionStations"
)

type StationListData struct {
	StationId   string `json:"stationId"`
	StationName string `json:"stationName"`
	Latitude    string `json:"latitude"`
	Longitude   string `json:"longitude"`
	ShopName    string `json:"shopName"`
}

type StationListResp struct {
	Code    string            `json:"code"`
	Message string            `json:"message"`
	Data    []StationListData `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	for _, d := range slr.Data {
		ac = append(ac, map[string]any{
			"channel":     channel,
			"city":        city,
			"stationName": d.StationName,
			"businessId":  d.<PERSON>,
			"stationId":   d.StationId,
			"lat":         d.Latitude,
			"lon":         d.Longitude,
			"runTime":     ct,
		})
	}
	return ac
}

type StationListRequestBody struct {
	Page                 int      `json:"page"`
	Size                 int      `json:"size"`
	Latitude             float64  `json:"latitude"`
	Longitude            float64  `json:"longitude"`
	ConditionEleType     []string `json:"conditionEleType"`
	ConditionStationType []string `json:"conditionStationType"`
	ConditionLabelType   []string `json:"conditionLabelType"`
	ConditionVoltage     []string `json:"conditionVoltage"`
}

func (a Api) GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)
	body := StationListRequestBody{
		Page:                 pageNum,
		Size:                 10,
		Latitude:             lat,
		Longitude:            lng,
		ConditionEleType:     []string{},
		ConditionStationType: []string{},
		ConditionLabelType:   []string{},
		ConditionVoltage:     []string{},
	}

	bJSON, err := json.Marshal(&body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化站点列表Body失败")
	}

	headers := map[string]string{
		"Content-type": "application/json;charset=UTF-8",
		"client":       "",
		"x-tag":        "flyio",
		"reqsource":    "Wechat",
		"v_num":        "",
		"phpsessid":    "",
		"origincharge": "MINIAPP",
		"mobile_type":  "",
		"referer":      "https://cdn-evone-oss.echargenet.com/",
	}

	r, err := common.DoRequest(StationListURL, bJSON, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := StationListResp{}
	_ = json.Unmarshal(r.Content(), &sr)
	if sr.Code != "0000" {
		return []map[string]any{}, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.targetDesc, p.CITY), nil
}
