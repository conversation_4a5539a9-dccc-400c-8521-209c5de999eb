package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/xiaoju/api/common"
	"tianyan-crawler/internal/source/xiaoju/api/util"

	"github.com/riete/errors"
)

const (
	StationDetailURL = "https://energy.xiaojukeji.com/station-api/station/getoneinfo"
)

// type StationPriceData struct {
// 	UserPayPriceDetailList []struct {
// 		Time         string  `json:"time"`
// 		ElecPrice    float64 `json:"userPayElecPrice"`
// 		ServicePrice float64 `json:"userPayServPrice"`
// 		TotalPrice   float64 `json:"userPayTotalPrice"`
// 	} `json:"userPayPriceDetailList"`
// 	ComparePriceDetailList []struct {
// 		Time         string  `json:"time"`
// 		ElecPrice    float64 `json:"compareElecPrice"`
// 		ServicePrice float64 `json:"compareServPrice"`
// 		TotalPrice   float64 `json:"compareTotalPrice"`
// 	} `json:"comparePriceDetailList"`
// }

// func handleTime(time string) ([]string, error) {
// 	times := strings.Split(time, "-")
// 	if len(times) != 2 {
// 		return nil, errors.New(fmt.Sprintf("price.Time字符串格式不正确: %s", time))
// 	}
// 	return times, nil
// }

// func (spd *StationPriceData) handle(
// 	hoursPerDay int,
// 	targetDesc,
// 	stationId,
// 	stationName,
// 	city,
// 	province,
// 	latitude,
// 	longitude,
// 	rd string,
// ) ([]map[string]any, error) {
// 	pd := make([]map[string]any, hoursPerDay)
// 	for idx, price := range spd.UserPayPriceDetailList {
// 		memberPrice := spd.ComparePriceDetailList[idx]

// 		priceTime, err := handleTime(price.Time)
// 		if err != nil {
// 			return pd, err
// 		}

// 		startTime, err := time.Parse(time.TimeOnly, priceTime[0]+":00")
// 		if err != nil {
// 			return pd, err
// 		}

// 		stopTime, _ := time.Parse(time.TimeOnly, priceTime[1]+":00")
// 		// 24:00:00 无法处理，所以错误忽略
// 		// if err != nil {
// 		// 	return pd, err
// 		// }

// 		start := startTime.Hour()
// 		end := stopTime.Hour()
// 		if end == 0 {
// 			end = hoursPerDay
// 		}

// 		for i := start; i < end; i++ {
// 			pd[i] = map[string]any{
// 				"id":                 rd,
// 				"jobId":              rd,
// 				"stationId":          stationId,
// 				"stationName":        stationName,
// 				"city":               city,
// 				"province":           province,
// 				"stationLat":         latitude,
// 				"stationLng":         longitude,
// 				"accountType":        "普通用户",
// 				"crawlDay":           time.Now().Format("********"),
// 				"crawlTime":          time.Now().Format(time.TimeOnly),
// 				"channel":            targetDesc,
// 				"operName":           targetDesc,
// 				"pileType":           "3",
// 				"timeInterval":       fmt.Sprintf("%02d", i),
// 				"elecPrice":          strconv.FormatFloat(price.ElecPrice, 'f', 2, 64),
// 				"servicePrice":       strconv.FormatFloat(price.ServicePrice, 'f', 2, 64),
// 				"totalPrice":         strconv.FormatFloat(price.TotalPrice, 'f', 2, 64),
// 				"memberElecPrice":    strconv.FormatFloat(memberPrice.ElecPrice, 'f', 2, 64),
// 				"memberServicePrice": strconv.FormatFloat(memberPrice.ServicePrice, 'f', 2, 64),
// 				"memberPrice":        strconv.FormatFloat(memberPrice.TotalPrice, 'f', 2, 64),
// 			}
// 		}
// 	}
// 	return pd, nil
// }

type ChargingGunData struct {
	GunId       string  `json:"connectorId"`
	GunType     string  `json:"chargeType"`
	OriginPower float64 `json:"power"`
}

type StationDetailData struct {
	StationId    string  `json:"fullStationId"`
	StationName  string  `json:"stationName"`
	StationAddr  string  `json:"address"`
	OperatorId   string  `json:"operatorId"`
	OperatorName string  `json:"operatorName"`
	Lon          float64 `json:"lng"`
	Lat          float64 `json:"lat"`
	BusinessTime string  `json:"openTime"`
	ParkFeeDesc  string  `json:"parkDesc"`
	Tags         []struct {
		Name string `json:"name"`
	} `json:"tags"`
	Qualification struct {
		Pic string `json:"pic"`
	} `json:"qualification"`
	BusinessSituation struct {
		FastCharge         int               `json:"fastTotalNum"`
		SuperCharge        int               `json:"superTotalNum"`
		SlowCharge         int               `json:"slowTotalNum"`
		FastConnectorList  []ChargingGunData `json:"fastConnectorList"`
		SuperConnectorList []ChargingGunData `json:"superConnectorList"`
		SlowConnectorList  []ChargingGunData `json:"slowConnectorList"`
	} `json:"businessSituation"`
	Infrastructure struct {
		Bathroom   int `json:"bathroom"`
		Restaurant int `json:"restaurant"`
		Shop       int `json:"shop"`
		Lounge     int `json:"lounge"`
		Rainshed   int `json:"rainshed"`
	} `json:"infrastructure"`
	// 价格
	// FastConnectorPriceDescription StationPriceData `json:"fastConnectorPriceDescription"`
	// SlowConnectorPriceDescription StationPriceData `json:"slowConnectorPriceDescription"`
}

type StationDetailResp struct {
	Code    float32           `json:"code"`
	Title   string            `json:"title"`
	Msg     string            `json:"msg"`
	Data    StationDetailData `json:"data"`
	TraceId string            `json:"traceId"`
}

// func (sdr *StationDetailResp) handlePriceData(
// 	targetDesc,
// 	rd,
// 	stationId,
// 	stationName,
// 	city,
// 	province,
// 	latitude,
// 	longitude string,
// ) (string, error) {
// 	const hoursPerDay = 24
// 	r := []map[string]any{}

// 	if len(sdr.Data.FastConnectorPriceDescription.UserPayPriceDetailList) != 0 {
// 		dc, err := sdr.Data.FastConnectorPriceDescription.handle(
// 			hoursPerDay,
// 			targetDesc,
// 			stationId,
// 			stationName,
// 			city,
// 			province,
// 			latitude,
// 			longitude,
// 			rd,
// 		)
// 		if err != nil {
// 			return "", err
// 		}
// 		r = append(r, dc...)
// 	} else if len(sdr.Data.SlowConnectorPriceDescription.UserPayPriceDetailList) != 0 {
// 		dc, err := sdr.Data.SlowConnectorPriceDescription.handle(
// 			hoursPerDay,
// 			targetDesc,
// 			stationId,
// 			stationName,
// 			city,
// 			province,
// 			latitude,
// 			longitude,
// 			rd,
// 		)
// 		if err != nil {
// 			return "", err
// 		}
// 		r = append(r, dc...)
// 	}

// 	p, err := json.Marshal(r)
// 	if err != nil {
// 		return "", err
// 	}

// 	return string(p), nil
// }

func (sdr *StationDetailResp) handleGunData(
	targetDesc,
	rd,
	stationId,
	stationName,
	city,
	ct string,
) (string, error) {
	guns := []map[string]any{}

	for _, fGun := range sdr.Data.BusinessSituation.FastConnectorList {
		guns = append(guns, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       fGun.GunId,
			"gunType":     fGun.GunType,
			"power":       fGun.OriginPower,
			"originPower": fGun.OriginPower,
			"runTime":     ct,
		})
	}
	for _, sGun := range sdr.Data.BusinessSituation.SuperConnectorList {
		guns = append(guns, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       sGun.GunId,
			"gunType":     sGun.GunType,
			"power":       sGun.OriginPower,
			"originPower": sGun.OriginPower,
			"runTime":     ct,
		})
	}
	for _, sGun := range sdr.Data.BusinessSituation.SlowConnectorList {
		guns = append(guns, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       sGun.GunId,
			"gunType":     sGun.GunType,
			"power":       sGun.OriginPower,
			"originPower": sGun.OriginPower,
			"runTime":     ct,
		})
	}

	g, err := json.Marshal(guns)
	if err != nil {
		return "", err
	}

	return string(g), nil
}

func (sdr *StationDetailResp) convert(channel, province, city string) ([]map[string]any, error) {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)

	stationLocation := ""
	for _, tag := range sdr.Data.Tags {
		if tag.Name == "地上" {
			stationLocation = "地上"
		}
		if tag.Name == "地下" {
			stationLocation = "地下"
		}
	}

	facilities := []string{}
	if sdr.Data.Infrastructure.Bathroom == 1 {
		facilities = append(facilities, "厕所")
	}
	if sdr.Data.Infrastructure.Restaurant == 1 {
		facilities = append(facilities, "餐厅")
	}
	if sdr.Data.Infrastructure.Shop == 1 {
		facilities = append(facilities, "便利店")
	}
	if sdr.Data.Infrastructure.Lounge == 1 {
		facilities = append(facilities, "休息室")
	}
	if sdr.Data.Infrastructure.Rainshed == 1 {
		facilities = append(facilities, "雨棚")
	}

	tips := []string{}
	for _, tag := range sdr.Data.Tags {
		tips = append(tips, tag.Name)
	}

	// price, err := sdr.handlePriceData(
	// 	channel,
	// 	rd,
	// 	sdr.Data.StationId,
	// 	sdr.Data.StationName,
	// 	city,
	// 	province,
	// 	strconv.FormatFloat(sdr.Data.Lat, 'f', -1, 64),
	// 	strconv.FormatFloat(sdr.Data.Lon, 'f', -1, 64),
	// )
	// if err != nil {
	// 	return nil, errors.NewFromErr(err).Trace("价格数据转换失败")
	// }

	chargingGun, err := sdr.handleGunData(
		channel,
		rd,
		sdr.Data.StationId,
		sdr.Data.StationName,
		city,
		ct,
	)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("充电枪数据转换失败")
	}

	am := []map[string]any{
		{
			"id":                   rd,
			"jobId":                rd,
			"channel":              channel,
			"city":                 city,
			"province":             province,
			"stationId":            sdr.Data.StationId,
			"stationName":          sdr.Data.StationName,
			"stationAddr":          sdr.Data.StationAddr,
			"operatorId":           sdr.Data.OperatorId,
			"operatorName":         sdr.Data.OperatorName,
			"fastCharge":           sdr.Data.BusinessSituation.FastCharge + sdr.Data.BusinessSituation.SuperCharge,
			"slowCharge":           sdr.Data.BusinessSituation.SlowCharge,
			"lon":                  sdr.Data.Lon,
			"lat":                  sdr.Data.Lat,
			"stationLocation":      stationLocation,
			"parkFeeDesc":          sdr.Data.ParkFeeDesc,
			"businessTime":         sdr.Data.BusinessTime,
			"supportingFacilities": strings.Join(facilities, ","),
			"serviceProvider":      sdr.Data.Qualification.Pic,
			"stationRemark":        "小桔渠道运营商字段从serviceProvider获取",
			"tips":                 strings.Join(tips, ","),
			"price":                "",
			"chargingGun":          chargingGun,
			"runTime":              ct,
		},
	}
	return am, nil
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	params := map[string]string{
		"source":          "2",
		"lat":             p.Latitude,
		"lng":             p.Longitude,
		"fullstationid":   p.StationID,
		"showSuperCharge": "true",
		"channel":         "wx",
		"openid":          "general_app",
		"mobiletype":      "microsoft_microsoft",
		"nettype":         "wifi",
		"amChannel":       "50051",
		"ttid":            "wx",
		"ticket":          a.accountInfo.Token,
		"token":           a.accountInfo.Token,
		"tokenId":         a.accountInfo.Token,
	}

	query := url.Values{}
	for k, v := range params {
		query.Set(k, v)
	}

	wsgsig, werr := util.GetSiginature[any]("GET", query)
	if werr != nil {
		return nil, errors.New(werr.Error()).Trace(fmt.Sprintf("获取 wsgsig 数据失败, http请求: %s", query))
	}
	query.Set("wsgsig", wsgsig)
	params["wsgsig"] = wsgsig

	siteDetailURL := StationDetailURL + "?" + query.Encode()
	logger.DetailLogger.Info("SiteDetailURL with params: ", siteDetailURL)

	headers := map[string]string{
		"content-type": "application/json",
		"ticket":       "",
	}
	r, err := common.GetRequest(siteDetailURL, params, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	_ = json.Unmarshal(r.Content(), &sdr)
	fmt.Println(r.ContentToString())
	if sdr.Code != 10000 {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := sdr.convert(a.targetDesc, a.gParam.Province, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点详情数据转换失败, 对端返回 %s", r.ContentToString()))
	}

	return result, nil
}
