package util

import (
	"encoding/json"
	"net/url"
	"strings"

	"github.com/riete/errors"
	"github.com/riete/exec"
)

const (
	SignatureJSPath = "scripts/xiaoju/sign.js"
)

func invokeSignatureScript(method, paramsString, bodyJSONStr string) (string, errors.Error) {
	r := exec.NewCmdRunner("node", SignatureJSPath, method, paramsString, bodyJSONStr)
	s, _, err := r.RunWithSeparatedOutput()
	if err != nil {
		return s, errors.NewFromErr(err).Trace("参数加密失败: " + err.Error())
	}
	return s, nil
}

func GetSiginature[T any](method string, param url.Values, body ...T) (string, errors.Error) {
	method = strings.ToUpper(method)
	paramString := param.Encode()

	bodyString := ""
	if method == "POST" {
		b, err := json.Marshal(body[0])
		if err != nil {
			return "", errors.NewFromErr(err)
		}
		bodyString = string(b)
	}

	result, err := invokeSignatureScript(method, paramString, bodyString)
	if err != nil {
		return "", errors.NewFromErr(err)
	}

	return result, nil
}
