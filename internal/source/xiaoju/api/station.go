package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/xiaoju/api/common"
	"tianyan-crawler/internal/source/xiaoju/api/util"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://energy.xiaojukeji.com/station-api/homepage/stationList"
)

type StationListParam struct {
	Source       int64   `json:"source"`
	Ticket       string  `json:"ticket"`
	Token        string  `json:"token"`
	TokenId      string  `json:"tokenId"`
	Bathroom     int64   `json:"bathroom"`
	Canopy       int64   `json:"canopy"`
	Channel      int64   `json:"channel"`
	Distance     int64   `json:"distance"`
	Keeper       int64   `json:"keeper"`
	Lat          float64 `json:"lat"`
	Lng          float64 `json:"lng"`
	Userlng      float64 `json:"userlng"`
	Userlat      float64 `json:"userlat"`
	Lounge       int64   `json:"lounge"`
	Park         int64   `json:"park"`
	Type         int64   `json:"type"`
	GuideTagKeys []int64 `json:"guideTagKeys"`
	AmChannel    string  `json:"amChannel"`
	TTID         string  `json:"ttid,omitempty"`
	PageSize     int64   `json:"pageSize"`
	PageNo       int     `json:"pageNo"`
}

type StationData struct {
	StationId           string  `json:"fullStationId"`
	StationName         string  `json:"stationName"`
	Latitude            float64 `json:"lat"`
	Longitude           float64 `json:"lng"`
	FastCharge          int     `json:"fastChargeNum"`
	FreeFastCharge      int     `json:"fastChargeIdleNum"`
	SuperFastCharge     int     `json:"superChargeNum"`
	FreeSuperFastCharge int     `json:"superChargeIdleNum"`
	SlowCharge          int     `json:"slowChargeNum"`
	FreeSlowCharge      int     `json:"slowChargeIdleNum"`
	// 价格相关
	ElecPrice         float64 `json:"elecSalePrice"`
	ServicePrice      float64 `json:"servSalePrice"`
	TotalPrice        string  `json:"totalSalePrice"`
	MemberAnchorPrice struct {
		MemberElecPrice    float64 `json:"elecSalePrice"`
		MemberServicePrice float64 `json:"servSalePrice"`
		MemberTotalPrice   float64 `json:"totalSalePrice"`
	}
}

func getTimeRange() []int {
	now := time.Now()
	switch {
	case 0 <= now.Hour() && now.Hour() < 8:
		return []int{0, 7}
	case 8 <= now.Hour() && now.Hour() < 16:
		return []int{8, 15}
	case 16 <= now.Hour() && now.Hour() <= 23:
		return []int{16, 23}
	default:
		return []int{0, 23}
	}
}

func (sd *StationData) handlePriceData(
	targetDesc,
	rd,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) (string, error) {
	r := []map[string]any{}

	if sd.TotalPrice != "" {
		pd := make([]map[string]any, 8)

		timeRange := getTimeRange()
		// servicePrice := strconv.FormatFloat(sd.MemberAnchorPrice.ServMarketPrice-sd.MemberAnchorPrice.TotalMarketDiff, 'f', 2, 64)
		for i := timeRange[0]; i <= timeRange[1]; i++ {
			pd[i%8] = map[string]any{
				"id":                 rd,
				"jobId":              rd,
				"stationId":          stationId,
				"stationName":        stationName,
				"city":               city,
				"province":           province,
				"stationLat":         latitude,
				"stationLng":         longitude,
				"accountType":        "普通用户",
				"crawlDay":           time.Now().Format("********"),
				"crawlTime":          time.Now().Format(time.TimeOnly),
				"channel":            targetDesc,
				"operName":           targetDesc,
				"pileType":           "3",
				"timeInterval":       fmt.Sprintf("%02d", i),
				"elecPrice":          strconv.FormatFloat(sd.ElecPrice, 'f', 2, 64),
				"servicePrice":       strconv.FormatFloat(sd.ServicePrice, 'f', 2, 64),
				"totalPrice":         sd.TotalPrice,
				"memberElecPrice":    strconv.FormatFloat(sd.MemberAnchorPrice.MemberElecPrice, 'f', 2, 64),
				"memberServicePrice": strconv.FormatFloat(sd.MemberAnchorPrice.MemberServicePrice, 'f', 2, 64),
				"memberPrice":        strconv.FormatFloat(sd.MemberAnchorPrice.MemberTotalPrice, 'f', 2, 64),
			}
		}
		r = append(r, pd...)
	}

	p, err := json.Marshal(r)
	if err != nil {
		return "", err
	}

	return string(p), nil
}

type StationListData struct {
	Components []struct {
		ComponentId string        `json:"components"`
		Data        []StationData `json:"data"`
	} `json:"components"`
	Total int `json:"total"`
	// StationSummary    any   `json:"stationSummary"`
	// ChargeGuide       bool  `json:"chargeGuide"`
	// OldUser           bool  `json:"oldUser"`
	// TopRecommendTitle any   `json:"topRecommendTitle"`
	// Route             any   `json:"route"`
}

type StationListResp struct {
	Code    float32         `json:"code"`
	Title   string          `json:"title"`
	Msg     string          `json:"msg"`
	Data    StationListData `json:"data"`
	TraceId string          `json:"traceId"`
}

func (slr *StationListResp) convert(channel, province, city string) ([]map[string]any, error) {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	timeInterval := time.Now().Format("15")

	if len(slr.Data.Components) > 0 {
		for _, d := range slr.Data.Components[0].Data {
			price, err := d.handlePriceData(
				channel,
				rd,
				d.StationId,
				d.StationName,
				city,
				province,
				strconv.FormatFloat(d.Latitude, 'f', -1, 64),
				strconv.FormatFloat(d.Longitude, 'f', -1, 64),
			)
			if err != nil {
				return nil, errors.NewFromErr(err).Trace("价格数据转换失败")
			}
			ac = append(ac, map[string]any{
				"channel":        channel,
				"city":           city,
				"stationId":      d.StationId,
				"stationName":    d.StationName,
				"lon":            d.Longitude,
				"lat":            d.Latitude,
				"fastCharge":     d.FastCharge + d.SuperFastCharge,
				"freeFastCharge": d.FreeFastCharge + d.FreeSuperFastCharge,
				"slowCharge":     d.SlowCharge,
				"freeSlowCharge": d.FreeSlowCharge,
				"date":           date,
				"timeInterval":   timeInterval,
				"priceJson":      price,
				"runTime":        ct,
			})
		}
	}
	return ac, nil
}

const (
	TTID_WX     = "wx"
	TTID_ALIPAY = "alipay"
)

var TTID_TYPE_MAP = map[string]struct {
	source string
	ttid   string
}{
	TTID_WX:     {source: "2", ttid: "wx"},
	TTID_ALIPAY: {source: "9", ttid: "ali"},
}

func (a Api) GetStationList(p Param, pageNum int) ([]map[string]any, int, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)

	formData := StationListParam{
		Source:       2,
		Ticket:       a.accountInfo.Token,
		Token:        a.accountInfo.Token,
		TokenId:      a.accountInfo.Token,
		Bathroom:     0,
		Canopy:       0,
		Channel:      0,
		Distance:     30,
		Keeper:       0,
		Lat:          lat,
		Lng:          lng,
		Userlng:      lng,
		Userlat:      lat,
		Lounge:       0,
		Park:         0,
		Type:         1,
		PageSize:     10,
		PageNo:       pageNum,
		GuideTagKeys: []int64{},
		AmChannel:    "50051",
	}

	source := TTID_TYPE_MAP[a.accountInfo.Type].source
	ttid := TTID_TYPE_MAP[a.accountInfo.Type].ttid
	query := url.Values{}
	params := map[string]string{
		"source": source,
		"ttid":   ttid,
	}
	for k, v := range params {
		query.Set(k, v)
	}

	wsgsig, werr := util.GetSiginature("POST", query, formData)
	if werr != nil {
		formDataJSON, _ := json.Marshal(formData)
		return nil, 0, errors.New(werr.Error()).Trace(fmt.Sprintf("获取 wsgsig 数据失败, http请求: %s, %s", query.Encode(), string(formDataJSON)))
	}
	query.Set("wsgsig", wsgsig)

	siteListUrl := StationListURL + "?" + query.Encode()
	logger.DetailLogger.Info("SiteListURL with params: ", siteListUrl)

	b, err := json.Marshal(formData)
	if err != nil {
		return nil, 0, errors.NewFromErr(err)
	}

	headers := map[string]string{
		"content-type": "application/json",
		"ticket":       a.accountInfo.Token,
		"referer":      "https://servicewechat.com/wx06cb940499986937/403/page-frame.html",
	}

	r, err := common.DoRequest(siteListUrl, b, 6, request.WithHeader(headers))
	if err != nil {
		return nil, 0, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, 0, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	slr := StationListResp{}
	fmt.Println(r.ContentToString())
	_ = json.Unmarshal(r.Content(), &slr)
	if slr.Code != 10000 {
		if slr.Code == 20009 {
			return []map[string]any{}, 0, nil
		} else {
			return nil, 0, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
		}
	}

	result, err := slr.convert(a.targetDesc, a.gParam.Province, p.CITY)
	if err != nil {
		return nil, 0, errors.New(fmt.Sprintf("站点列表数据转换失败, 对端返回 %s", r.ContentToString()))
	}
	return result, slr.Data.Total, nil
}
