package dynamic

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/riete/errors"
	"github.com/riete/gpool"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/source/dynamic/api"
)

const (
	CrawlTarget     = task.Dynamic
	CrawlTargetDesc = "动态执行脚本"
	DelayTime       = 3 * time.Second
	ResultChunkSize = 100
)

var pool = gpool.NewLongTermPool(config.Config.DynamicScriptMaxConcurrence)

type DynamicApiResp struct {
	param api.Param
	data  []any
	err   errors.Error
}

type Task struct {
	originMessage task.StartTaskMessage
	message       api.TaskMessage

	script []byte
	api    api.DynamicApi
	ch     chan<- *task.TaskResponseMessage
	ctx    context.Context
	cancel context.CancelFunc

	chDynamicResult chan DynamicApiResp
}

func (t *Task) fetchScript() error {
	script, err := t.api.GetDynamicScript(t.message.GlobalParam.ScriptUrl)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("[Task Error]: %s 获取脚本失败 <%s>", t.message.TaskInstanceId, err.Error())))
		return err
	}
	t.script = script
	if err := t.api.CheckScript(t.script); err != nil {
		logger.Error(errors.New(fmt.Sprintf("[Task Error]: %s 脚本校验失败 <%s>", t.message.TaskInstanceId, err.Error())))
		return err
	}
	return nil
}

func (t *Task) execScript(p api.Param, gParam api.GlobalParam) {
	chResult := make(chan []any)
	go func(ctx context.Context) {
		err := t.api.ExecScript(t.script, p, gParam.ToAnyMap(), t.message.TaskInstanceId, chResult, ctx)
		if err != nil {
			t.chDynamicResult <- DynamicApiResp{
				param: p,
				data:  nil,
				err:   err,
			}
			close(chResult)
			return
		}
		close(chResult)
	}(t.ctx)
	for r := range chResult {
		// 计算需要拆分的块数
		numChunks := (len(r) + ResultChunkSize - 1) / ResultChunkSize
		for i := 0; i < numChunks; i++ {
			// 计算当前块的起始和结束索引
			start := i * ResultChunkSize
			end := start + ResultChunkSize
			if end > len(r) {
				end = len(r)
			}
			// 发送当前块到 channel
			t.chDynamicResult <- DynamicApiResp{
				param: p,
				data:  r[start:end],
				err:   nil,
			}
		}
	}

}

func (t *Task) ExecDynamicScript() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 动态脚本开始执行, 总计%d条参数", t.message.TaskInstanceId, total))
		if total == 0 {
			total += 1
			t.message.Params = append(t.message.Params, api.Param{})
		}
		if err := t.fetchScript(); err != nil {
			logger.PostErrorLog(logger.ErrorLogMessage{
				RequestNo:  t.message.TaskInstanceId,
				Channel:    t.message.Target,
				StackTrace: err.Error(),
				ErrorMsg:   fmt.Sprintf("[Task Error]: %s 获取脚本失败 <%s>", t.message.TaskInstanceId, err.Error()),
			})
			logger.Error(errors.New(fmt.Sprintf("[Task Error]: %s 获取脚本失败 <%s>", t.message.TaskInstanceId, err.Error())))
			task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
			return
		}
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, p := range t.message.Params {
				pool.Get()
				go func(p api.Param, gParam api.GlobalParam) {
					defer wg.Done()
					defer pool.Put()
					t.execScript(p, gParam)
				}(p, t.message.GlobalParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chDynamicResult)
		}()

		errCnt := 0
		for i := range t.chDynamicResult {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    fmt.Sprintf("[Task Error]: %s 执行脚本失败 <%s>", t.message.TaskInstanceId, i.err.Error()),
				})
				logger.Error(errors.New(fmt.Sprintf("[Task Error]: %s 执行脚本失败 <%s>", t.message.TaskInstanceId, i.err.Error())))
				errCnt++
				if errCnt == 10 {
					t.cancel()
					break
				}
				continue
			}
			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param,
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 动态脚本执行结束", t.message.TaskInstanceId))
	}
}

func (t *Task) Exec() {
	if t.message.GlobalParam.ScriptUrl == "" {
		logger.Error(errors.New(fmt.Sprintf("[Task Error]: %s 脚本地址为空", t.message.TaskInstanceId)))
		return
	}
	t.ExecDynamicScript()
}

func (t *Task) Cancel() {
	t.cancel()
}

func NewDynamicTask(tsm task.StartTaskMessage, atmsg api.TaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	ctx, cancel := context.WithCancel(context.Background())
	return &Task{
		originMessage:   tsm,
		message:         atmsg,
		ch:              ch,
		ctx:             ctx,
		cancel:          cancel,
		api:             api.New(CrawlTargetDesc),
		chDynamicResult: make(chan DynamicApiResp),
	}
}

func executor(stm task.StartTaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	msg := api.TaskMessage{}
	err := msg.Marshal(stm)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("[Task Error]: %s 解析任务参数错误 <%s>, %+v", stm.TaskInstanceId, err.Error(), stm)))
	}

	return NewDynamicTask(stm, msg, ch)
}

func init() {
	task.SEMapping.Register(string(CrawlTarget), executor)
}
