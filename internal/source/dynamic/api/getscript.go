package api

import (
	"fmt"
	"net/http"
	"tianyan-crawler/internal/source/dynamic/api/common"

	"github.com/riete/errors"
)

func (a Api) GetDynamicScript(script string) ([]byte, errors.Error) {
	r, resErr := common.GetRequest(script, map[string]string{}, 6)
	if resErr != nil {
		return []byte{}, errors.NewFromErr(resErr)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return []byte{}, errors.New(status).Trace(fmt.Sprintf("获取脚本失败, http响应: %s", status))
	}

	return r.Content(), nil
}
