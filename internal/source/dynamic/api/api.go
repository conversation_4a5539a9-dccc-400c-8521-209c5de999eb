package api

import (
	"context"
	"encoding/json"

	"github.com/riete/errors"

	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/util"
)

type AccountInfo struct {
	Token string `json:"token"`
}

type GlobalParam struct {
	TaskId     string `json:"taskId"`
	TemplateId string `json:"templateId"`
	Channel    string `json:"channel"`
	ScriptUrl  string `json:"scriptUrl"`
	BizType    string `json:"bizType"`
	Province   string `json:"province"`
	City       string `json:"city"`
	CityCode   string `json:"cityCode"`
}

func (gp GlobalParam) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(gp)
}

type Param map[string]any

type TaskMessage struct {
	TaskInstanceId string      `json:"taskInstanceId"`
	Target         string      `json:"target"`
	AccountInfo    AccountInfo `json:"accountInfo"`
	GlobalParam    GlobalParam `json:"globalParam"`
	Params         []Param     `json:"params"`
}

func (msg *TaskMessage) Marshal(stmsg task.StartTaskMessage) error {
	s, err := json.Marshal(stmsg)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(s, msg); err != nil {
		return err
	}
	return nil
}

type DynamicApi interface {
	GetDynamicScript(url string) ([]byte, errors.Error)
	ExecScript(script []byte, param, gParam map[string]any, taskInstanceId string, chResult chan<- []any, ctx context.Context) errors.Error
	CheckScript(script []byte) errors.Error
}

type Api struct {
	targetDesc string
}

func New(targetDesc string) Api {
	return Api{targetDesc}
}
