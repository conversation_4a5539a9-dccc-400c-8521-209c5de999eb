package api

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os/exec"
	"reflect"
	"tianyan-crawler/internal/common/logger"

	"github.com/riete/errors"
)

const (
	ExecScript = "scripts/python3/exec.py"
)

func handleResult(res any) ([]any, error) {
	t := reflect.TypeOf(res)
	// 判断类型并输出结果
	switch t.Kind() {
	case reflect.Array:
		return res.([]any), nil
	case reflect.Slice:
		return res.([]any), nil
	case reflect.Map:
		return []any{res}, nil
	default:
		return nil, errors.New("结果不是数组、切片或映射")
	}
}

func handleOutputByLine(stdio io.ReadCloser, ch chan<- cmdOutput) {
	stdioReader := bufio.NewReader(stdio)
	cmdOutput := cmdOutput{}
	for {
		line, err := stdioReader.ReadBytes('\n')
		if err != nil {
			cmdOutput.err = err
			ch <- cmdOutput
			break
		}
		if err == io.EOF {
			break
		}
		cmdOutput.content = line[:len(line)-1]
		ch <- cmdOutput
	}
}

type cmdOutput struct {
	content []byte
	err     error
}

func (a Api) ExecScript(script []byte, param, gParam map[string]any, taskInstanceId string, chResult chan<- []any, ctx context.Context) errors.Error {
	// combingParam := map[string]any{}
	// maps.Copy(combingParam, param)
	// maps.Copy(combingParam, gParam)
	gParam["taskInstanceId"] = taskInstanceId
	gpJSON, err := json.Marshal(gParam)
	if err != nil {
		return errors.New("gParam 数据解析失败").Trace(fmt.Sprintf("gParam 数据解析失败: %s", err.Error()))
	}

	pJSON, err := json.Marshal(param)
	if err != nil {
		return errors.New("param 数据解析失败").Trace(fmt.Sprintf("param 数据解析失败: %s", err.Error()))
	}

	cmd := exec.CommandContext(ctx, "python3", "-u", ExecScript, string(script), string(gpJSON), string(pJSON))
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return errors.New("获取标准输出失败").Trace(fmt.Sprintf("get stdout err: %s", err.Error()))
	}
	cmd.Stderr = cmd.Stdout

	if err = cmd.Start(); err != nil {
		return errors.New("启动脚本失败").Trace(fmt.Sprintf("start err: %s", err.Error()))
	}

	ch := make(chan cmdOutput)
	go func() {
		handleOutputByLine(stdout, ch)
	}()

	for output := range ch {
		if output.err != nil {
			cmd.Process.Kill()
			break
		}

		var res any
		err := json.Unmarshal(output.content, &res)
		if err != nil {
			logger.Info(fmt.Sprintf("任务ID: %s [非结果输出]>> %s", taskInstanceId, string(output.content)))
			continue
		}

		result, err := handleResult(res)
		if err != nil {
			logger.Info(fmt.Sprintf("任务ID: %s [非结果输出]>> %s", taskInstanceId, string(output.content)))
			continue
		}
		chResult <- result
	}

	if err = cmd.Wait(); err != nil {
		return errors.New("脚本执行失败").Trace(fmt.Sprintf("wait err: %s", err.Error()))
	}
	close(ch)
	return nil
}
