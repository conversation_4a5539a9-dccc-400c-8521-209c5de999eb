package api

import (
	"fmt"

	"github.com/riete/errors"
	"github.com/riete/exec"
)

const (
	CheckScript = "scripts/python3/check.py"
)

func (a Api) CheckScript(script []byte) errors.Error {
	r := exec.NewCmdRunner("python3", CheckScript, string(script))
	stdO, stdE, err := r.RunWithSeparatedOutput()
	if err != nil {
		return errors.New(err.Error()).Trace(fmt.Sprintf("脚本检查失败: %s, %s", stdO, stdE))
	}
	return nil
}
