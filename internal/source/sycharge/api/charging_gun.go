package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/sycharge/api/common"
)

const (
	ChargingGunURL = "https://app.wodeev.com/equipment/v2.0/stations-gunlist"
)

type ChargingGunData struct {
	GunId   string `json:"gunNo"`
	GunType string `json:"gunSubtype"`
	Power   string `json:"power"`
	Voltage string `json:"voltage"`
}

type ChargingGunResp struct {
	Ret     int               `json:"ret"`
	GunList []ChargingGunData `json:"gunList"`
}

func (cgr *ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	r := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgr.GunList {
		power := ""
		if gun.Power != "" {
			power = gun.Power[:len(gun.Power)-2]
		}
		gunType := ""
		if gun.GunType == "01" {
			gunType = "交流"
		} else {
			gunType = "直流"
		}

		r = append(r, map[string]any{
			"channel":           targetDesc,
			"id":                rd,
			"jobId":             rd,
			"stationId":         stationId,
			"stationName":       stationName,
			"city":              city,
			"gunId":             gun.GunId,
			"gunType":           gunType,
			"power":             power,
			"voltageUpperLimit": gun.Voltage,
			"voltageLowerLimit": gun.Voltage,
			"runTime":           ct,
		})
	}

	return r, nil
}

func (a Api) GetChargingGunList(p Param, pageNum int, chargeType string) ([]map[string]any, errors.Error) {
	query := map[string]string{
		"stationId":  p.StationID,
		"pageNum":    strconv.Itoa(pageNum),
		"totalNum":   "20",
		"gunSubtype": chargeType,
	}

	r, err := common.GetRequest(ChargingGunURL, query, 6)
	if err != nil {
		return []map[string]any{}, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return []map[string]any{}, errors.New(status).Trace(fmt.Sprintf("获取站点充电枪数据失败, http响应: %s", status))
	}

	cgr := ChargingGunResp{}
	_ = json.Unmarshal(r.Content(), &cgr)
	if cgr.Ret != 200 {
		return []map[string]any{}, errors.New(fmt.Sprintf("获取站点充电枪数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}

	return result, nil
}
