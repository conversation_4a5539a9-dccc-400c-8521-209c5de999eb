package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/sycharge/api/common"
)

const (
	PriceListURL = "https://app.wodeev.com/equipment/v2.0/stationPrice"
	PriceRegExp  = `电费\s*[:：]?\s*(\d+\.\d+)\s*元\/度\s*[;；]?服务费\s*[:：]?\s*(\d+\.\d+)\s*元\/度`
)

type PriceData struct {
	// "chargePrice": "1.2",
	// "time": "00:00~24:00",
	// "otherItem": "电费：0.75元/度；服务费：0.45元/度"
	Time         string `json:"time"`
	ChargePrice  string `json:"chargePrice"`
	OtherItem    string `json:"otherItem"`
	StartTime    string
	StopTime     string
	ElecPrice    float64
	ServicePrice float64
}

func (pd *PriceData) handleTime() error {
	times := strings.Split(pd.Time, "~")
	if len(times) != 2 {
		return errors.New(fmt.Sprintf("price.Time字符串格式不正确: %s", pd.Time))
	}
	pd.StartTime = times[0]
	pd.StopTime = times[1]
	return nil
}

func (pd *PriceData) handlePrice() error {
	// 正则表达式匹配电费和服务费
	re := regexp.MustCompile(PriceRegExp)
	matches := re.FindStringSubmatch(pd.OtherItem)
	if matches == nil || len(matches) < 3 {
		return errors.New(fmt.Sprintf("解析站点价格字段失败，未找到匹配项: %s", pd.OtherItem))
	}

	electricityFee, err := strconv.ParseFloat(matches[1], 64)
	if err != nil {
		return errors.New(fmt.Sprintf("转换站点电费字段失败: %s", matches[1]))
	}
	serviceFee, err := strconv.ParseFloat(matches[2], 64)
	if err != nil {
		return errors.New(fmt.Sprintf("转换站点服务费字段失败: %s", matches[2]))
	}

	pd.ElecPrice = electricityFee
	pd.ServicePrice = serviceFee
	return nil
}

type StationPriceResp struct {
	Ret          int `json:"ret"`
	StationPrice struct {
		PriceList struct {
			DirectPrice   []PriceData `json:"directPrice"`
			ExchangePrice []PriceData `json:"exchangePrice"`
		} `json:"priceList"`
	} `json:"stationPrice"`
}

var PileTypeMap = map[string]string{
	"fastGun": "1",
	"slowGun": "2",
}

func (spr StationPriceResp) convert(
	targetDesc,
	stationId,
	stationName,
	latitude,
	longitude,
	province,
	city string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	r := []map[string]any{}
	if len(spr.StationPrice.PriceList.DirectPrice) != 0 {
		dc, err := handlePriceData(
			spr.StationPrice.PriceList.DirectPrice,
			hoursPerDay,
			PileTypeMap["fastGun"],
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return []map[string]any{}, err
		}
		r = append(r, dc...)
	}

	if len(spr.StationPrice.PriceList.ExchangePrice) != 0 {
		dc, err := handlePriceData(
			spr.StationPrice.PriceList.ExchangePrice,
			hoursPerDay,
			PileTypeMap["slowGun"],
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return []map[string]any{}, err
		}
		r = append(r, dc...)
	}

	return r, nil
}

func handlePriceData(
	data []PriceData,
	hoursPerDay int,
	chargeType,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	for _, price := range data {
		err := price.handleTime()
		if err != nil {
			return pd, err
		}

		err = price.handlePrice()
		if err != nil {
			return pd, err
		}

		startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
		if err != nil {
			return pd, err
		}
		stopTime, _ := time.Parse(time.TimeOnly, price.StopTime+":00")
		// 24:00:00 无法处理，所以错误忽略
		// if err != nil {
		// 	return pd, err
		// }

		start := startTime.Add(30 * time.Minute).Hour()
		end := stopTime.Add(30 * time.Minute).Hour()
		if end == 0 {
			end = hoursPerDay
		}

		for i := start; i < end; i++ {
			pd[i] = map[string]any{
				"id":           rd,
				"jobId":        rd,
				"stationId":    stationId,
				"stationName":  stationName,
				"city":         city,
				"province":     province,
				"stationLat":   latitude,
				"stationLng":   longitude,
				"accountType":  "普通用户",
				"crawlDay":     time.Now().Format("********"),
				"crawlTime":    time.Now().Format(time.TimeOnly),
				"channel":      targetDesc,
				"operName":     targetDesc,
				"pileType":     chargeType,
				"timeInterval": fmt.Sprintf("%02d", i),
				"elecPrice":    strconv.FormatFloat(price.ElecPrice, 'f', -1, 64),
				"servicePrice": strconv.FormatFloat(price.ServicePrice, 'f', -1, 64),
				"totalPrice":   price.ChargePrice,
			}
		}
	}
	return pd, nil
}

func (a Api) GetStationPrice(p Param) ([]map[string]any, errors.Error) {
	query := map[string]string{
		"stationId": p.StationID,
	}

	r, err := common.GetRequest(PriceListURL, query, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	spr := new(StationPriceResp)
	_ = json.Unmarshal(r.Content(), spr)
	if spr.Ret != 200 {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := spr.convert(
		a.targetDesc,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
		a.gParam.Province,
		p.City,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
