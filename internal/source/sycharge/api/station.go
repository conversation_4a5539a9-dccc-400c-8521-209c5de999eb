package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/internal/source/sycharge/api/common"
)

const (
	StationListURL = "https://app.wodeev.com/equipment/es_stationNearBy"
)

type StationListData struct {
	StationId      string `json:"stationId"`
	StationName    string `json:"stationName"`
	Latitude       string `json:"stationLat"`
	Longitude      string `json:"stationLon"`
	FastCharge     int    `json:"dcNums"`
	FreeFastCharge int    `json:"dcFreeNums"`
	SlowCharge     int    `json:"acNums"`
	FreeSlowCharge int    `json:"acFreeNums"`
}

type StationListResp struct {
	Ret     int               `json:"ret"`
	ChcList []StationListData `json:"chcList"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.ChcList {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error) {
	query := map[string]string{
		"positionLon": p.Lgn,
		"positionLat": p.Lat,
		"sort":        "01",
		"pageNum":     strconv.Itoa(pageNum),
		"totalNum":    "20",
		"elecMode":    "",
		"operType":    "",
		"distance":    "",
		"freeFlag":    "",
		"parkingFee":  "",
		"chargeMode":  "",
		"cityCode":    p.CityCode,
	}

	r, err := common.GetRequest(StationListURL, query, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := StationListResp{}
	_ = json.Unmarshal(r.Content(), &sr)
	if sr.Ret != 200 {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.targetDesc, p.CITY), nil
}
