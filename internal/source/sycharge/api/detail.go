package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/sycharge/api/common"
)

const (
	StationDetailURL = "https://app.wodeev.com/equipment/v2.0/charging-station/details"
)

type StationDetailData struct {
	StationId    string `json:"stationId"`
	StationName  string `json:"stationName"`
	StationAddr  string `json:"stationAddr"`
	OperatorId   string `json:"operId"`
	OperatorName string `json:"operAlias"`
	OperatorType string `json:"operTypeName"`
	OperatorTel  string `json:"serviceTel"`
	FastCharge   int    `json:"dcNums"`
	SlowCharge   int    `json:"acNums"`
	Lon          string `json:"stationLon"`
	Lat          string `json:"stationLat"`
	BusinessTime string `json:"busiTime"` // 营运时间
	ParkFeeDesc  string `json:"parkingFee"`
}

type StationDetailResp struct {
	Ret         int               `json:"ret"`
	StationInfo StationDetailData `json:"stationInfo"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) map[string]any {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	am := map[string]any{
		"id":           rd,
		"jobId":        rd,
		"channel":      channel,
		"city":         city,
		"province":     province,
		"stationId":    sdr.StationInfo.StationId,
		"stationName":  sdr.StationInfo.StationName,
		"operatorId":   sdr.StationInfo.OperatorId,
		"operatorName": sdr.StationInfo.OperatorName,
		"operatorType": sdr.StationInfo.OperatorType,
		"operatorTel":  sdr.StationInfo.OperatorTel,
		"fastCharge":   sdr.StationInfo.FastCharge,
		"slowCharge":   sdr.StationInfo.SlowCharge,
		"lon":          sdr.StationInfo.Lon,
		"lat":          sdr.StationInfo.Lat,
		"businessTime": sdr.StationInfo.BusinessTime[:len(sdr.StationInfo.BusinessTime)-1],
		"parkFeeDesc":  sdr.StationInfo.ParkFeeDesc,
		"stationAddr":  sdr.StationInfo.StationAddr,
		"runTime":      ct,
	}
	return am
}

func (a Api) GetStationDetail(p Param) (map[string]any, errors.Error) {
	query := map[string]string{
		"stationId": p.StationID,
		"dayNum":    "7",
	}

	r, resErr := common.GetRequest(StationDetailURL, query, 6)
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	_ = json.Unmarshal(r.Content(), &sdr)
	if sdr.Ret != 200 {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sdr.convert(a.targetDesc, a.gParam.Province, p.City), nil
}
