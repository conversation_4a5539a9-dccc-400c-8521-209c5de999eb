package sycharge

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/riete/errors"
	"github.com/riete/gpool"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/source/sycharge/api"
)

const (
	CrawlTarget     = "sycharge"
	CrawlTargetDesc = "顺易充"
	DelayTime       = 3 * time.Second
)

var pool = gpool.NewLongTermPool(config.Config.SYChargeMaxConcurrence)

type ApiCommonResp struct {
	param api.Param
	data  []map[string]any
	err   errors.Error
}

type StationListApiResp struct {
	ApiCommonResp
}

type StationDetailApiResp struct {
	ApiCommonResp
}

type StationPriceApiResp struct {
	ApiCommonResp
}

type ChargingGunApiResp struct {
	ApiCommonResp
}

type Task struct {
	originMessage task.StartTaskMessage
	message       api.TaskMessage

	api    api.SYChargeApi
	ch     chan<- *task.TaskResponseMessage
	ctx    context.Context
	cancel context.CancelFunc

	chStationList   chan StationListApiResp
	chStationDetail chan StationDetailApiResp
	chStationPrice  chan StationPriceApiResp
	chChargingGun   chan ChargingGunApiResp
	existStations   map[string]bool

	mutex              sync.Mutex
	detailSuccess      int
	detailFailed       int
	priceSuccess       int
	priceFailed        int
	chargingGunSuccess int
	chargingGunFailed  int
}

func (t *Task) fetchSiteList(p api.Param) {
	pageNum := 1
	total := 0
	for {
		select {
		case <-t.ctx.Done():
			return
		default:
			acr := ApiCommonResp{param: p}
			stations, err := t.api.GetStationList(p, pageNum)
			if err != nil {
				logger.Error(err.Trace(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下站点信息爬取失败", t.message.TaskInstanceId, p.Lat, p.Lgn)))
				logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下, 站点信息爬取失败", t.message.TaskInstanceId, p.Lat, p.Lgn))
				acr.err = err
				t.chStationList <- StationListApiResp{ApiCommonResp: acr}
				return
			}

			acr.data = stations
			t.chStationList <- StationListApiResp{ApiCommonResp: acr}
			if len(stations) > 0 {
				logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下, 第%d页爬取完成, 返回%d条数据", t.message.TaskInstanceId, p.Lat, p.Lgn, pageNum, len(stations)))
				total += len(stations)
				pageNum += 1
				continue
			}
			logger.Info(fmt.Sprintf("任务ID: %s, (%s, %s)经纬度下总计%d个站点", t.message.TaskInstanceId, p.Lat, p.Lgn, total))
			return
		}
	}
}

func (t *Task) CrawlingStationList() {
	select {
	case <-t.ctx.Done():
		return
	default:
		totalTask := len(t.message.Params)
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(totalTask)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.fetchSiteList(p)
				}(curParam)
				time.Sleep(DelayTime)
			}
			wg.Wait()
			close(t.chStationList)
		}()

		for i := range t.chStationList {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    fmt.Sprintf("(%s, %s) 经纬度下站点列表爬取失败", i.param.Lat, i.param.Lgn),
				})
				continue
			}

			if len(i.data) == 0 {
				logger.Info(fmt.Sprintf("任务ID: %s, 站点列表爬取结束, 站点列表为空", t.message.TaskInstanceId))
				continue
			}

			for _, siteInfo := range i.data {
				stationId := siteInfo["stationId"].(string)
				if stationId == "" {
					logger.PostErrorLog(logger.ErrorLogMessage{
						RequestNo:   t.message.TaskInstanceId,
						RequestData: i.param,
						BizType:     t.message.GlobalParam.BizType,
						Channel:     t.message.Target,
						StackTrace:  errors.New(fmt.Sprintf("任务ID: %s, 站点ID解析失败, [%v]", t.message.TaskInstanceId, siteInfo)).Stack(),
						ErrorMsg:    fmt.Sprintf("任务ID: %s, 站点ID解析失败, [%v]", t.message.TaskInstanceId, siteInfo),
					})
					continue
				}

				if t.existStations[stationId] {
					continue
				}

				t.existStations[stationId] = true
				resp := task.NewTaskResponseMessage(t.originMessage)
				resp.PrepareToSend(task.NewCrawlingResult(
					api.Param{}.ToAnyMap(),
					siteInfo,
				))
				t.ch <- resp
			}
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 站点列表爬取结束, 总计: %d 个", t.message.TaskInstanceId, len(t.existStations)))
	}
}

func (t *Task) fetchStationDetail(p api.Param) (map[string]any, errors.Error) {
	detail, err := t.api.GetStationDetail(p)
	if err != nil {
		t.mutex.Lock()
		t.detailFailed += 1
		t.mutex.Unlock()
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点详情失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	} else {
		t.mutex.Lock()
		t.detailSuccess += 1
		t.mutex.Unlock()
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点详情成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	}
	return detail, err
}

func (t *Task) executeStationDetailCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		logger.Info(fmt.Sprintf("任务ID: %s, 取消, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		return
	default:
		detail, err := t.fetchStationDetail(p)
		t.chStationDetail <- StationDetailApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  []map[string]any{detail},
				err:   err,
			},
		}
	}
}

func (t *Task) CrawlingStationDetail() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点详情, 去重后总计%d个站点", t.message.TaskInstanceId, total))
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeStationDetailCrawl(p)
				}(curParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chStationDetail)
		}()

		for i := range t.chStationDetail {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点详情失败",
				})
				continue
			}

			if len(i.data) > 0 {
				stationId := i.data[0]["stationId"].(string)
				if stationId == "" {
					logger.PostErrorLog(logger.ErrorLogMessage{
						Level:       logger.ERROR_LOG_MESSAGE_MAP.WARN.Type,
						RequestNo:   t.message.TaskInstanceId,
						RequestData: i.param,
						BizType:     t.message.GlobalParam.BizType,
						Channel:     t.message.Target,
						StackTrace:  "",
						ErrorMsg:    "当前站点详情数据为空",
					})
					continue
				}
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点详情爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.detailSuccess, t.detailFailed, total))
	}
}

func (t *Task) fetchSitePrice(p api.Param) ([]map[string]any, errors.Error) {
	prices, err := t.api.GetStationPrice(p)
	if err != nil {
		t.mutex.Lock()
		t.priceFailed += 1
		t.mutex.Unlock()
		logger.Error(errors.New(err.Error()))
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点价格失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		return prices, err
	}

	t.mutex.Lock()
	t.priceSuccess += 1
	t.mutex.Unlock()
	logger.Info(fmt.Sprintf("任务ID: %s, 获取站点价格成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	return prices, err
}

func (t *Task) executeSitePriceCrawl(p api.Param) {
	select {
	case <-t.ctx.Done():
		logger.Info(fmt.Sprintf("任务ID: %s, 取消, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
		return
	default:
		price, err := t.fetchSitePrice(p)
		t.chStationPrice <- StationPriceApiResp{
			ApiCommonResp: ApiCommonResp{
				param: p,
				data:  price,
				err:   err,
			},
		}
	}
}

func (t *Task) CrawlingStationPrice() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点价格信息, 去重后总计%d个站点", t.message.TaskInstanceId, total))
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					t.executeSitePriceCrawl(p)
				}(curParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chStationPrice)
		}()

		for i := range t.chStationPrice {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点价格失败",
				})
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点价格爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.priceSuccess, t.priceFailed, total))
	}
}

func (t *Task) fetchSiteChargingGun(p api.Param, pageNum int, chargeType string) ([]map[string]any, errors.Error) {
	chargingGun, err := t.api.GetChargingGunList(p, pageNum, chargeType)
	if err != nil {
		t.mutex.Lock()
		t.chargingGunFailed += 1
		t.mutex.Unlock()
		logger.Error(err)
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点充电枪列表失败, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	} else {
		t.mutex.Lock()
		t.chargingGunSuccess += 1
		t.mutex.Unlock()
		logger.Info(fmt.Sprintf("任务ID: %s, 获取站点充电枪列表成功, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
	}
	return chargingGun, err
}

func (t *Task) executeSiteChargingGunCrawl(p api.Param, chargeType string) int {
	pageNum := 1
	total := 0
	for {
		select {
		case <-t.ctx.Done():
			logger.Info(fmt.Sprintf("任务ID: %s, 取消, [%s - %s]", t.message.TaskInstanceId, p.StationID, p.StationName))
			return total
		default:
			acr := ApiCommonResp{param: p}
			chargingGun, err := t.fetchSiteChargingGun(p, pageNum, chargeType)
			if err != nil {
				logger.Info(fmt.Sprintf("任务ID: %s, 站点ID: %s, 获取充电枪列表失败", t.message.TaskInstanceId, p.StationID))
				logger.Error(err.Trace(fmt.Sprintf("任务ID: %s, 站点ID: %s, 获取充电枪列表失败", t.message.TaskInstanceId, p.StationID)))
				acr.err = err
				t.chChargingGun <- ChargingGunApiResp{ApiCommonResp: acr}
				return total
			}

			acr.data = chargingGun
			t.chChargingGun <- ChargingGunApiResp{ApiCommonResp: acr}
			total += len(chargingGun)
			if len(chargingGun) >= 20 {
				logger.Info(fmt.Sprintf("任务ID: %s, 站点ID: %s, 第%d页爬取完成, 返回%d条数据", t.message.TaskInstanceId, p.StationID, pageNum, len(chargingGun)))
				pageNum += 1
				continue
			}
			logger.Info(fmt.Sprintf("任务ID: %s, 站点ID: %s, 共%d页数据, 总计%d个充电枪", t.message.TaskInstanceId, p.StationID, pageNum, total))
			return total
		}
	}
}

var ChargeTypeMap = struct {
	Fast string
	Slow string
}{
	Fast: "02",
	Slow: "01",
}

func (t *Task) CrawlingChargingGun() {
	select {
	case <-t.ctx.Done():
		return
	default:
		total := len(t.message.Params)
		logger.Info(fmt.Sprintf("任务ID: %s, 开始爬取站点充电枪列表, 去重后总计%d个充电枪", t.message.TaskInstanceId, total))
		go func() {
			wg := &sync.WaitGroup{}
			wg.Add(total)
			for _, curParam := range t.message.Params {
				pool.Get()
				go func(p api.Param) {
					defer pool.Put()
					defer wg.Done()
					sTotal := t.executeSiteChargingGunCrawl(p, ChargeTypeMap.Slow)
					fTotal := t.executeSiteChargingGunCrawl(p, ChargeTypeMap.Fast)
					if sTotal == 0 && fTotal == 0 {
						logger.PostErrorLog(logger.ErrorLogMessage{
							RequestNo:   t.message.TaskInstanceId,
							RequestData: p,
							BizType:     t.message.GlobalParam.BizType,
							Channel:     t.message.Target,
							StackTrace:  "",
							ErrorMsg:    "当前站点充电枪列表为空",
						})
					}
				}(curParam)
			}
			time.Sleep(DelayTime)
			wg.Wait()
			close(t.chChargingGun)
		}()

		for i := range t.chChargingGun {
			if i.err != nil {
				logger.PostErrorLog(logger.ErrorLogMessage{
					RequestNo:   t.message.TaskInstanceId,
					RequestData: i.param,
					BizType:     t.message.GlobalParam.BizType,
					Channel:     t.message.Target,
					StackTrace:  i.err.Stack(),
					ErrorMsg:    "获取站点充电枪列表失败",
				})
				continue
			}
			if len(i.data) == 0 {
				continue
			}

			resp := task.NewTaskResponseMessage(t.originMessage)
			resp.PrepareToSend(task.NewCrawlingResult(
				i.param.ToAnyMap(),
				i.data,
			))
			t.ch <- resp
		}
		task.PostFinishMessage(t.message.TaskInstanceId, t.message.GlobalParam.TemplateId)
		logger.Info(fmt.Sprintf("任务ID: %s, 所有站点充电枪列表爬取结束, 成功: %d, 失败: %d, 总计: %d", t.message.TaskInstanceId, t.chargingGunSuccess, t.chargingGunFailed, total))
	}
}

func (t *Task) Exec() {
	crawlingType := t.message.GlobalParam.BizType
	if crawlingType == "" {
		logger.Error(errors.New("[Task Error]: BizType 参数缺失"))
		return
	}

	switch crawlingType {
	case "STATION", "STATION_HOUR":
		t.CrawlingStationList()
	case "DETAIL":
		t.CrawlingStationDetail()
	case "PRICE":
		t.CrawlingStationPrice()
	case "CHARGING_GUN":
		t.CrawlingChargingGun()
	default:
		logger.Error(errors.New("[Task Error]: 不支持的爬虫类型"))
	}
}

func (t *Task) Cancel() {
	t.cancel()
}

func NewEChargeCrawlingTask(stm task.StartTaskMessage, atmsg api.TaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	ctx, cancel := context.WithCancel(context.Background())
	return &Task{
		originMessage:   stm,
		message:         atmsg,
		ctx:             ctx,
		cancel:          cancel,
		ch:              ch,
		api:             api.New(CrawlTargetDesc, atmsg),
		chStationList:   make(chan StationListApiResp),
		chStationDetail: make(chan StationDetailApiResp),
		chStationPrice:  make(chan StationPriceApiResp),
		chChargingGun:   make(chan ChargingGunApiResp),
		existStations:   make(map[string]bool),
	}
}

func executor(stm task.StartTaskMessage, ch chan<- *task.TaskResponseMessage) task.TaskRunner {
	msg := api.TaskMessage{}
	err := msg.Marshal(stm)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("[Task Error]: 解析任务参数错误 <%s>, %+v", err.Error(), stm)))
	}

	return NewEChargeCrawlingTask(stm, msg, ch)
}

func init() {
	task.SEMapping.Register(CrawlTarget, executor)
}
