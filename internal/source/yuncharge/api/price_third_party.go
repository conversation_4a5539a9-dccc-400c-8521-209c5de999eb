package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/yuncharge/api/common"

	"github.com/riete/errors"
)

type ThirdPartyStationPriceData struct {
	CurrentPrice struct {
		ServiceFee     string `json:"serviceFee"`
		TotalFee       string `json:"totalFee"`
		StartTime      string `json:"startTime"`
		EndTime        string `json:"endTime"`
		ElectricityFee string `json:"electricityFee"`
	} `json:"currentPrice"`
}

func (spd *ThirdPartyStationPriceData) handle(
	hoursPerDay int,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)

	for i := 0; i < hoursPerDay; i++ {
		pd[i] = map[string]any{
			"id":                 rd,
			"jobId":              rd,
			"stationId":          stationId,
			"stationName":        stationName,
			"city":               city,
			"province":           province,
			"stationLat":         latitude,
			"stationLng":         longitude,
			"accountType":        "普通用户",
			"crawlDay":           time.Now().Format("********"),
			"crawlTime":          time.Now().Format(time.TimeOnly),
			"channel":            targetDesc,
			"operName":           targetDesc,
			"pileType":           "3",
			"timeInterval":       fmt.Sprintf("%02d", i),
			"elecPrice":          spd.CurrentPrice.ElectricityFee,
			"servicePrice":       spd.CurrentPrice.ServiceFee,
			"totalPrice":         spd.CurrentPrice.TotalFee,
			"memberElecPrice":    0.00,
			"memberServicePrice": 0.00,
			"memberPrice":        0.00,
		}
	}
	return pd, nil
}

type ThirdPartyStationPriceResp struct {
	PageIndex  int64                      `json:"pageIndex"`
	TotalPage  int64                      `json:"totalPage"`
	Header     RespHeader                 `json:"header"`
	PageSize   int64                      `json:"pageSize"`
	Body       ThirdPartyStationPriceData `json:"body"`
	TotalCount int64                      `json:"totalCount"`
}

func (spr *ThirdPartyStationPriceResp) convert(
	targetDesc,
	province,
	city,
	stationId,
	stationName,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	result := []map[string]any{}
	prices, err := spr.Body.handle(
		hoursPerDay,
		targetDesc,
		stationId,
		stationName,
		city,
		province,
		latitude,
		longitude,
	)
	if err != nil {
		return result, err
	}
	result = append(result, prices...)

	return result, nil
}

func (a Api) GetThirdPartyStationPrice(p Param) ([]map[string]any, errors.Error) {
	// 通过非自营站点详情接口获取价格数据
	priceListParam := ThirdPartyStationDetailParams{
		StationId:    p.StationID,
		Latitude:     p.Latitude,
		Longitude:    p.Longitude,
		CityId:       p.BusinessID,
		UserId:       a.accountInfo.UserID,
		TerminalType: "2",
	}

	plp, err := json.Marshal(priceListParam)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("ThirdPartyStationDetailParams 序列化失败 %s", err)))
	}

	signature := ParamsSign(plp, HmacMd5Key)
	req := YunChargeReq{
		Header: YunChargeReqHeader{
			Version:       "0",
			IsSync:        "0",
			IsChannelUser: "1",
			Token:         a.accountInfo.Token,
			AccessKey:     AccessKey,
			Timestamp:     signature.ts,
			Nonce:         signature.nonce,
			Sign:          signature.sign,
		},
		Body: string(plp),
	}

	encryptParams, err := req.Encrypt(AESKey, AESIV)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("encryptParams 加密失败 %s", err)))
		return nil, errors.NewFromErr(err)
	}
	r, err := common.DoRequest(ThirdPartyStationDetailURL, encryptParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	spr := ThirdPartyStationPriceResp{}
	_ = json.Unmarshal(r.Content(), &spr)
	if spr.Header.ResultCode != "0" {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := spr.convert(
		a.targetDesc,
		a.gParam.Province,
		p.City,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
