package api

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"

	"tianyan-crawler/internal/source/yuncharge/api/util"
)

type AccountInfo struct {
	Token  string `json:"token"`
	PriKey string `json:"priKey"`
	UserID string `json:"userId"`
}

type GlobalParam struct {
	TemplateId string `json:"templateId"`
	Channel    string `json:"channel"`
	ScriptUrl  string `json:"scriptUrl"`
	BizType    string `json:"bizType"`
	Province   string `json:"province"`
	City       string `json:"city"`
}

func (gp GlobalParam) ToAnyMap() map[string]any {
	return commonUtil.ToAnyMapWithJSONTag(gp)
}

type Param struct {
	StationID   string `json:"station_id,omitempty"`
	StationName string `json:"station_name,omitempty"`
	CITY        string `json:"CITY,omitempty"`
	City        string `json:"city,omitempty"`
	CityCode    string `json:"CITY_CODE,omitempty"`
	CityId      string `json:"YKC_CITY_ID,omitempty"`
	BusinessID  string `json:"business_id,omitempty"`
	Lat         string `json:"LAT,omitempty"`
	Lgn         string `json:"LGN,omitempty"`
	Latitude    string `json:"lat,omitempty"`
	Longitude   string `json:"lon,omitempty"`
}

func (p Param) ToAnyMap() map[string]any {
	return commonUtil.ToAnyMapWithJSONTag(p)
}

type TaskMessage struct {
	TaskInstanceId string      `json:"taskInstanceId"`
	Target         string      `json:"target"`
	AccountInfo    AccountInfo `json:"accountInfo"`
	GlobalParam    GlobalParam `json:"globalParam"`
	Params         []Param     `json:"params"`
}

func (msg *TaskMessage) Marshal(stmsg task.StartTaskMessage) error {
	s, err := json.Marshal(stmsg)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(s, msg); err != nil {
		return err
	}
	return nil
}

const (
	AccessKey  = "app6000000576936459"
	HmacMd5Key = "75d46199475ce40b"
	AESIV      = "whnbqwdswf2bv587"
	AESKey     = "v587wf2bqwdswhnb"
)

type YunChargeReq struct {
	Header    YunChargeReqHeader `json:"header"`
	Body      string             `json:"body"`
	PageIndex int                `json:"pageIndex,omitempty"`
	PageSize  int                `json:"pageSize,omitempty"`
}

func (req YunChargeReq) Encrypt(key string, iv string) ([]byte, error) {
	bizKey, _ := util.UTF8Parse(key)
	bizIv, _ := util.UTF8Parse(iv)

	paramAes := util.AesBase64{Key: bizKey, IV: bizIv}
	jsonStr, err := json.Marshal(req)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("YunChargeReq 序列化失败 %s", err)))
		return []byte{}, err
	}

	encryptParams, err := paramAes.Encrypt(jsonStr)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("YunChargeReq 加密失败 %s", err)))
		return []byte{}, err
	}

	return []byte(encryptParams), err
}

type ParamsSignature struct {
	ts    int64
	nonce string
	sign  string
}

func ParamsSign(params []byte, key string) ParamsSignature {
	ts := time.Now().UnixMilli()
	nonce := util.RandStr(4)
	signStr := string(params) + strconv.FormatInt(ts, 10) + nonce
	sign := strings.ToUpper(util.HmacMd5(key, signStr))

	return ParamsSignature{
		ts:    ts,
		nonce: nonce,
		sign:  sign,
	}
}

type RespHeader struct {
	IsSecurity string `json:"isSecurity"`
	BusinessID string `json:"businessID"`
	ResultCode string `json:"resultCode"`
	Sign       string `json:"sign"`
	ResultDesc string `json:"resultDesc"`
	Version    string `json:"version"`
	IsSync     string `json:"isSync"`
	Nonce      string `json:"nonce"`
	Token      string `json:"token"`
	ExpendTime string `json:"expendTime"`
	Security   string `json:"security"`
	AccessKey  string `json:"accessKey"`
	SyncSignal string `json:"syncSignal"`
	Timestamp  string `json:"timestamp"`
}

type YunChargeReqHeader struct {
	Version       string `json:"version"`
	Token         string `json:"token"`
	IsSecurity    string `json:"isSecurity"`
	Security      string `json:"security"`
	BusinessID    string `json:"businessID"`
	IsSync        string `json:"isSync"`
	SyncSignal    string `json:"syncSignal"`
	ExpendTime    string `json:"expendTime"`
	ResultCode    string `json:"resultCode"`
	ResultDesc    string `json:"resultDesc"`
	AccessKey     string `json:"accessKey"`
	Timestamp     int64  `json:"timestamp"`
	Nonce         string `json:"nonce"`
	Sign          string `json:"sign"`
	IsChannelUser string `json:"isChannelUser"`
}

type YunChargeApi interface {
	GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error)
	GetStationDetail(p Param) ([]map[string]any, errors.Error)
	GetThirdPartyStationDetail(p Param) ([]map[string]any, errors.Error)
	GetStationPrice(p Param) ([]map[string]any, errors.Error)
	GetThirdPartyStationPrice(p Param) ([]map[string]any, errors.Error)
	GetChargingGunList(p Param, pileType string) ([]map[string]any, errors.Error)
	GetThirdPartyChargingGunList(p Param, pileType string) ([]map[string]any, errors.Error)
}

type Api struct {
	targetDesc  string
	accountInfo AccountInfo
	gParam      GlobalParam
}

func New(targetDesc string, tm TaskMessage) Api {
	return Api{
		targetDesc:  targetDesc,
		accountInfo: tm.AccountInfo,
		gParam:      tm.GlobalParam,
	}
}
