package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/yuncharge/api/common"

	"github.com/riete/errors"
)

const (
	ThirdPartyStationDetailURL = "https://gw3.ykccn.com/api/omp/mt/fcs/app/chargingstation/detail"
)

type ThirdPartyStationDetailParams struct {
	UserId       string `json:"userId"`
	CityId       string `json:"cityId"`
	StationId    string `json:"id"`
	Longitude    string `json:"longitude"`
	Latitude     string `json:"latitude"`
	TerminalType string `json:"terminalType"`
}

type ThirdPartyStationDetailData struct {
	StationId          int    `json:"id"`
	StationName        string `json:"stationName"`
	StationAddr        string `json:"stationAddress"`
	OperatorTel        string `json:"stationTel"`
	BusinessTime       string `json:"stationOpenTime"`
	ParkFeeDesc        string `json:"stationParkingFee"`
	StationRemark      string `json:"stationRemark"`
	StatisticGunStatus struct {
		FastCharge int `json:"dcCount"`
		SlowCharge int `json:"acCount"`
	} `json:"statisticGunStatus"`
	CurrentPrice struct {
		ServiceFee     string `json:"serviceFee"`
		TotalFee       string `json:"totalFee"`
		StartTime      string `json:"startTime"`
		EndTime        string `json:"endTime"`
		ElectricityFee string `json:"electricityFee"`
	} `json:"currentPrice"`
}

type ThirdPartyStationDetailResp struct {
	PageIndex  int64                       `json:"pageIndex"`
	TotalPage  int64                       `json:"totalPage"`
	Header     RespHeader                  `json:"header"`
	PageSize   int64                       `json:"pageSize"`
	Body       ThirdPartyStationDetailData `json:"body"`
	TotalCount int64                       `json:"totalCount"`
}

func (sdr *ThirdPartyStationDetailResp) convert(channel, province string, p Param) ([]map[string]any, error) {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("**************")
	rd := ts + commonUtil.RandIntStr(18)

	am := []map[string]any{
		{
			"id":            rd,
			"jobId":         rd,
			"channel":       channel,
			"city":          p.City,
			"province":      province,
			"stationId":     strconv.Itoa(sdr.Body.StationId),
			"stationName":   sdr.Body.StationName,
			"stationAddr":   sdr.Body.StationAddr,
			"operatorTel":   sdr.Body.OperatorTel,
			"fastCharge":    strconv.Itoa(sdr.Body.StatisticGunStatus.FastCharge),
			"slowCharge":    strconv.Itoa(sdr.Body.StatisticGunStatus.SlowCharge),
			"lon":           p.Longitude,
			"lat":           p.Latitude,
			"businessTime":  sdr.Body.BusinessTime,
			"parkFeeDesc":   sdr.Body.ParkFeeDesc,
			"openRemark":    sdr.Body.StationRemark,
			"stationRemark": sdr.Body.StationRemark,
			"runTime":       ct,
		},
	}
	return am, nil
}

func (a Api) GetThirdPartyStationDetail(p Param) ([]map[string]any, errors.Error) {
	stationDetailParam := ThirdPartyStationDetailParams{
		StationId:    p.StationID,
		Latitude:     p.Latitude,
		Longitude:    p.Longitude,
		CityId:       p.BusinessID,
		UserId:       a.accountInfo.UserID,
		TerminalType: "2",
	}

	sdp, err := json.Marshal(stationDetailParam)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("stationDetailParam 序列化失败 %s", err)))
	}

	signature := ParamsSign(sdp, HmacMd5Key)
	req := YunChargeReq{
		Header: YunChargeReqHeader{
			Version:       "0",
			IsSync:        "0",
			IsChannelUser: "1",
			Token:         a.accountInfo.Token,
			AccessKey:     AccessKey,
			Timestamp:     signature.ts,
			Nonce:         signature.nonce,
			Sign:          signature.sign,
		},
		Body: string(sdp),
	}

	encryptParams, err := req.Encrypt(AESKey, AESIV)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("encryptParams 加密失败 %s", err)))
		return nil, errors.NewFromErr(err)
	}

	r, err := common.DoRequest(ThirdPartyStationDetailURL, encryptParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sdr := ThirdPartyStationDetailResp{}
	_ = json.Unmarshal(r.Content(), &sdr)
	if sdr.Header.ResultCode != "0" {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := sdr.convert(a.targetDesc, a.gParam.Province, p)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点详情数据转换失败, 对端返回 %s", r.ContentToString()))
	}

	return result, nil
}
