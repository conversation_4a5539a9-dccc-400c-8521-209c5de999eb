package util

import (
	"fmt"

	"tianyan-crawler/internal/common/logger"

	"github.com/riete/errors"
	"golang.org/x/text/encoding/charmap"
	"golang.org/x/text/encoding/simplifiedchinese"
)

func UTF8Parse(str string) ([]byte, error) {
	strGBK, err := simplifiedchinese.GBK.NewEncoder().Bytes([]byte(str))
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("strGBK 编码转化失败 %s", err)))
		return nil, err
	}
	r, err := charmap.ISO8859_1.NewDecoder().Bytes(strGBK)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("编码转化失败 %s", err)))
		return nil, err
	}
	return r, nil
}
