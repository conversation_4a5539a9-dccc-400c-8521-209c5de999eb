package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/yuncharge/api/common"

	"github.com/riete/errors"
)

const (
	ThirdPartyChargingGunURL = "https://gw3.ykccn.com/api/omp/mt/fcs/app/chargingstation/gunListByType"
)

type ThirdPartyChargingGunParams struct {
	// "id": "2853",
	// "userId": "11309554",
	// "pileType": "300102"
	StationId string `json:"id"`
	UserID    string `json:"userId"`
	PileType  string `json:"pileType"`
}

type ThirdPartyChargingGunData struct {
	GunId   string  `json:"gunCode"`
	GunType int     `json:"pileType"`
	Power   float64 `json:"power"`
}

type ThirdPartyChargingGunResp struct {
	PageIndex  int64                       `json:"pageIndex"`
	TotalPage  int64                       `json:"totalPage"`
	Header     RespHeader                  `json:"header"`
	PageSize   int64                       `json:"pageSize"`
	Body       []ThirdPartyChargingGunData `json:"body"`
	TotalCount int64                       `json:"totalCount"`
}

var ThirdPartyPileTypeChineseMap = map[string]string{
	"300102": "直流",
	"300101": "交流",
}

func (cgr ThirdPartyChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	guns := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgr.Body {
		guns = append(guns, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       gun.GunId,
			"gunType":     ThirdPartyPileTypeChineseMap[strconv.Itoa(gun.GunType)],
			"power":       strconv.FormatFloat(gun.Power, 'f', -1, 64),
			"runTime":     ct,
		})
	}

	return guns, nil
}

var ThirdPartyPileTypeMap = map[string]string{
	"fast": "300102",
	"slow": "300101",
}

func (a Api) GetThirdPartyChargingGunList(p Param, pileType string) ([]map[string]any, errors.Error) {
	chargingGunParam := ThirdPartyChargingGunParams{
		StationId: p.StationID,
		UserID:    a.accountInfo.UserID,
		PileType:  ThirdPartyPileTypeMap[pileType],
	}

	cgp, err := json.Marshal(chargingGunParam)

	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("chargingGunParam 序列化失败 %s", err)))
	}

	signature := ParamsSign(cgp, HmacMd5Key)

	req := YunChargeReq{
		Header: YunChargeReqHeader{
			Version:       "0",
			IsSync:        "0",
			IsChannelUser: "1",
			Token:         a.accountInfo.Token,
			AccessKey:     AccessKey,
			Timestamp:     signature.ts,
			Nonce:         signature.nonce,
			Sign:          signature.sign,
		},
		Body:      string(cgp),
		PageIndex: 1,
		PageSize:  999,
	}

	encryptParams, err := req.Encrypt(AESKey, AESIV)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("encryptParams 加密失败 %s", err)))
		return nil, errors.NewFromErr(err)
	}

	r, err := common.DoRequest(ThirdPartyChargingGunURL, encryptParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("充电枪列表数据失败, http响应: %s", status))
	}

	cgr := ThirdPartyChargingGunResp{}
	_ = json.Unmarshal(r.Content(), &cgr)

	if cgr.Header.ResultCode != "0" {
		return nil, errors.New(fmt.Sprintf("充电枪列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}
	return result, nil
}
