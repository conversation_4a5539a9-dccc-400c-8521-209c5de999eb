package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/yuncharge/api/common"

	"github.com/riete/errors"
)

const (
	StationPriceURL = "https://gw3.ykccn.com/api/omp/mt/station/getNewStationPeriodPriceList"
)

type StationPriceParams struct {
	UserId       string `json:"userId"`
	StationId    string `json:"stationId"`
	TerminalType string `json:"terminalType"`
}

type PriceData struct {
	TimeRange    string `json:"periodTime"`
	StartTime    string
	StopTime     string
	ElecPrice    float64 `json:"chargePrice,string"`
	ServicePrice float64 `json:"servicePrice,string"`
	TotalPrice   float64 `json:"totalPeriodPrice,string"`
}

func (pd *PriceData) handleTime() error {
	times := strings.Split(pd.TimeRange, "~")
	if len(times) != 2 {
		return errors.New(fmt.Sprintf("price.TimeRange字符串格式不正确: %s", pd.TimeRange))
	}
	// 处理 "periodTime": "00:00:00~07:00:00"
	pd.StartTime = times[0][0:5]
	pd.StopTime = times[1][0:5]
	return nil
}

type StationPriceData struct {
	StationPriceList []PriceData `json:"stationPriceList"`
	YKCPriceList     []PriceData `json:"ykcPriceList"`
	MemberPriceList  []PriceData `json:"memberPriceList"`
}

func (spd *StationPriceData) handle(
	hoursPerDay int,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	if len(spd.StationPriceList) == 0 {
		// 处理站点只有 ykcPriceList 的情况
		spd.StationPriceList = spd.YKCPriceList
	}
	for _, price := range spd.StationPriceList {
		// 处理 "periodTime": "其他时段"
		if price.TimeRange == "其他时段" {
			for i, p := range pd {
				if p == nil {
					pd[i] = map[string]any{
						"id":                 rd,
						"jobId":              rd,
						"stationId":          stationId,
						"stationName":        stationName,
						"city":               city,
						"province":           province,
						"stationLat":         latitude,
						"stationLng":         longitude,
						"accountType":        "普通用户",
						"crawlDay":           time.Now().Format("********"),
						"crawlTime":          time.Now().Format(time.TimeOnly),
						"channel":            targetDesc,
						"operName":           targetDesc,
						"pileType":           "3",
						"timeInterval":       fmt.Sprintf("%02d", i),
						"elecPrice":          price.ElecPrice,
						"servicePrice":       price.ServicePrice,
						"totalPrice":         price.TotalPrice,
						"memberElecPrice":    0.00,
						"memberServicePrice": 0.00,
						"memberPrice":        0.00,
					}
				}
			}
		} else {
			err := price.handleTime()
			if err != nil {
				return pd, err
			}

			startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
			if err != nil {
				return pd, err
			}
			stopTime, _ := time.Parse(time.TimeOnly, price.StopTime+":00")
			// 24:00:00 无法处理，所以错误忽略
			// if err != nil {
			// 	return pd, err
			// }

			start := startTime.Add(30 * time.Minute).Hour()
			end := stopTime.Add(30 * time.Minute).Hour()
			if end == 0 {
				end = hoursPerDay
			}

			for i := start; i < end; i++ {
				pd[i] = map[string]any{
					"id":                 rd,
					"jobId":              rd,
					"stationId":          stationId,
					"stationName":        stationName,
					"city":               city,
					"province":           province,
					"stationLat":         latitude,
					"stationLng":         longitude,
					"accountType":        "普通用户",
					"crawlDay":           time.Now().Format("********"),
					"crawlTime":          time.Now().Format(time.TimeOnly),
					"channel":            targetDesc,
					"operName":           targetDesc,
					"pileType":           "3",
					"timeInterval":       fmt.Sprintf("%02d", i),
					"elecPrice":          price.ElecPrice,
					"servicePrice":       price.ServicePrice,
					"totalPrice":         price.TotalPrice,
					"memberElecPrice":    0.00,
					"memberServicePrice": 0.00,
					"memberPrice":        0.00,
				}
			}
		}
	}
	for _, price := range spd.MemberPriceList {
		// 处理 "periodTime": "其他时段"
		if price.TimeRange == "其他时段" {
			for i := range pd {
				if pd[i]["memberPrice"] == 0.00 {
					pd[i]["memberElecPrice"] = price.ElecPrice
					pd[i]["memberServicePrice"] = price.ServicePrice
					pd[i]["memberPrice"] = price.TotalPrice
				}
			}
		} else {
			err := price.handleTime()
			if err != nil {
				return pd, err
			}

			startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
			if err != nil {
				return pd, err
			}
			stopTime, _ := time.Parse(time.TimeOnly, price.StopTime+":00")
			// 24:00:00 无法处理，所以错误忽略
			// if err != nil {
			// 	return pd, err
			// }

			start := startTime.Add(30 * time.Minute).Hour()
			end := stopTime.Add(30 * time.Minute).Hour()
			if end == 0 {
				end = hoursPerDay
			}

			for i := start; i < end; i++ {
				pd[i]["memberElecPrice"] = price.ElecPrice
				pd[i]["memberServicePrice"] = price.ServicePrice
				pd[i]["memberPrice"] = price.TotalPrice
			}
		}
	}
	return pd, nil
}

type StationPriceResp struct {
	PageIndex  int64            `json:"pageIndex"`
	TotalPage  int64            `json:"totalPage"`
	Header     RespHeader       `json:"header"`
	PageSize   int64            `json:"pageSize"`
	Body       StationPriceData `json:"body"`
	TotalCount int64            `json:"totalCount"`
}

func (spr *StationPriceResp) convert(
	targetDesc,
	province,
	city,
	stationId,
	stationName,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	result := []map[string]any{}
	if len(spr.Body.StationPriceList) != 0 || len(spr.Body.YKCPriceList) != 0 {
		prices, err := spr.Body.handle(
			hoursPerDay,
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return result, err
		}
		result = append(result, prices...)
	}

	return result, nil
}

func (a Api) GetStationPrice(p Param) ([]map[string]any, errors.Error) {
	priceListParam := StationPriceParams{
		UserId:       a.accountInfo.UserID,
		StationId:    p.StationID,
		TerminalType: "2",
	}

	plp, err := json.Marshal(priceListParam)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("stationListRawParam 序列化失败 %s", err)))
	}

	signature := ParamsSign(plp, HmacMd5Key)

	req := YunChargeReq{
		Header: YunChargeReqHeader{
			Version:       "0",
			IsSync:        "0",
			IsChannelUser: "1",
			Token:         a.accountInfo.Token,
			AccessKey:     AccessKey,
			Timestamp:     signature.ts,
			Nonce:         signature.nonce,
			Sign:          signature.sign,
		},
		Body: string(plp),
	}

	encryptParams, err := req.Encrypt(a.accountInfo.PriKey, AESIV)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("encryptParams 加密失败, priKey 为空 %s", err)))
		return nil, errors.New(fmt.Sprintf("encryptParams 加密失败, priKey 为空 %s", err))
	}

	var headers = map[string]string{
		"head-token":    a.accountInfo.UserID + "," + a.accountInfo.Token,
		"os-type":       "2",
		"cipher-type":   "2",
		"authorization": "MT-AUTHORIZATION",
	}

	r, err := common.DoRequest(StationPriceURL, encryptParams, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	spr := StationPriceResp{}
	_ = json.Unmarshal(r.Content(), &spr)
	if spr.Header.ResultCode != "0" {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回: %s", r.ContentToString()))
	}

	result, err := spr.convert(
		a.targetDesc,
		a.gParam.Province,
		p.City,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}
	// 过滤掉 result 中的 nil 值
	filteredResult := make([]map[string]any, 0)
	for _, item := range result {
		if item != nil {
			filteredResult = append(filteredResult, item)
		}
	}
	result = filteredResult

	return result, nil
}
