package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/yuncharge/api/common"

	"github.com/riete/errors"
)

const (
	ChargingGunURL = "https://gw3.ykccn.com/api/omp/mt/os/powerStation/queryPileListByPileType"
)

type ChargingGunParams struct {
	// stationId: "70212",
	// userId: "11309554",
	// pileType: "交流"
	StationId string `json:"stationId"`
	UserID    string `json:"userId"`
	PileType  string `json:"pileType"`
}

type ChargingGunData struct {
	GunId   string `json:"gunNo"`
	GunType string `json:"pileType"`
	Power   string `json:"power"`
}

type ChargingGunResp struct {
	PageIndex int64      `json:"pageIndex"`
	TotalPage int64      `json:"totalPage"`
	Header    RespHeader `json:"header"`
	PageSize  int64      `json:"pageSize"`
	Body      struct {
		DataList []ChargingGunData `json:"dataList"`
	} `json:"body"`
	TotalCount int64 `json:"totalCount"`
}

func (cgr ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	guns := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgr.Body.DataList {
		guns = append(guns, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       gun.GunId,
			"gunType":     gun.GunType,
			"power":       gun.Power,
			"runTime":     ct,
		})
	}

	return guns, nil
}

var PileTypeMap = map[string]string{
	"fast": "直流",
	"slow": "交流",
}

func (a Api) GetChargingGunList(p Param, pileType string) ([]map[string]any, errors.Error) {
	chargingGunParam := ChargingGunParams{
		StationId: p.StationID,
		UserID:    a.accountInfo.UserID,
		PileType:  PileTypeMap[pileType],
	}

	cgp, err := json.Marshal(chargingGunParam)

	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("chargingGunParam 序列化失败 %s", err)))
	}

	signature := ParamsSign(cgp, HmacMd5Key)

	req := YunChargeReq{
		Header: YunChargeReqHeader{
			Version:       "0",
			IsSync:        "0",
			IsChannelUser: "1",
			Token:         a.accountInfo.Token,
			AccessKey:     AccessKey,
			Timestamp:     signature.ts,
			Nonce:         signature.nonce,
			Sign:          signature.sign,
		},
		Body:      string(cgp),
		PageIndex: 1,
		PageSize:  999,
	}

	encryptParams, err := req.Encrypt(AESKey, AESIV)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("encryptParams 加密失败 %s", err)))
		return nil, errors.NewFromErr(err)
	}

	r, err := common.DoRequest(ChargingGunURL, encryptParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("充电枪列表数据失败, http响应: %s", status))
	}

	cgr := ChargingGunResp{}
	_ = json.Unmarshal(r.Content(), &cgr)

	if cgr.Header.ResultCode != "0" {
		return nil, errors.New(fmt.Sprintf("充电枪列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}
	return result, nil
}
