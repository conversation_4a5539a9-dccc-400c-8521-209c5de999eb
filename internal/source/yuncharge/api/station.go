package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/source/yuncharge/api/common"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://gw3.ykccn.com/api/omp/mt/os/powerStation/queryNewPowerStationList"
)

type StationListParams struct {
	TerminalType    string   `json:"terminalType"`
	Latitude        float64  `json:"latitude"`
	Longitude       float64  `json:"longitude"`
	PreferCondition string   `json:"preferCondition"`
	PileType        []string `json:"pileType"`
	Label           []string `json:"label"`
	ParkType        []string `json:"parkType"`
	ParkFee         []string `json:"parkFee"`
	PilePower       []string `json:"pilePower"`
	PileVoltage     []string `json:"pileVoltage"`
	BillAttr        string   `json:"billAttr"`
	CityId          string   `json:"cityId"`
	UserId          string   `json:"userId"`
	UserLat         float64  `json:"userLat"`
	UserLng         float64  `json:"userLng"`
	WhichFirst      string   `json:"whichFirst"`
	StationName     string   `json:"stationName"`
}

type StationData struct {
	StationId      string `json:"stationId"`
	StationName    string `json:"stationName"`
	Latitude       string `json:"latitude"`
	Longitude      string `json:"longitude"`
	FastCharge     int    `json:"directSum"`
	FreeFastCharge int    `json:"directIdleNum"`
	SlowCharge     int    `json:"interSum"`
	FreeSlowCharge int    `json:"interIdleNum"`
}

type StationListResp struct {
	PageIndex  int64         `json:"pageIndex"`
	TotalPage  int64         `json:"totalPage"`
	Header     RespHeader    `json:"header"`
	PageSize   int64         `json:"pageSize"`
	Body       []StationData `json:"body"`
	TotalCount int64         `json:"totalCount"`
}

func (slr *StationListResp) convert(channel, city, cityId string) ([]map[string]any, error) {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Body {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"businessId":     cityId, // ykc_city_id
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac, nil
}

func (a Api) GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)

	stationListRawParam := StationListParams{
		TerminalType:    "2",
		Latitude:        lat,
		Longitude:       lng,
		PreferCondition: "2",
		CityId:          p.CityId,
		UserLat:         lat,
		UserLng:         lng,
		WhichFirst:      "0",
		UserId:          a.accountInfo.UserID,
	}

	slp, err := json.Marshal(stationListRawParam)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("stationListRawParam 序列化失败 %s", err)))
	}

	signature := ParamsSign(slp, HmacMd5Key)

	req := YunChargeReq{
		Header: YunChargeReqHeader{
			Version:       "0",
			IsSync:        "0",
			IsChannelUser: "1",
			Token:         a.accountInfo.Token,
			AccessKey:     AccessKey,
			Timestamp:     signature.ts,
			Nonce:         signature.nonce,
			Sign:          signature.sign,
		},
		Body:      string(slp),
		PageIndex: pageNum,
		PageSize:  9999,
	}

	encryptParams, err := req.Encrypt(AESKey, AESIV)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("encryptParams 加密失败 %s", err)))
		return nil, errors.NewFromErr(err)
	}

	r, err := common.DoRequest(StationListURL, encryptParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	slr := StationListResp{}
	_ = json.Unmarshal(r.Content(), &slr)
	if slr.Header.ResultCode != "0" {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := slr.convert(a.targetDesc, p.CITY, p.CityId)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点列表数据转换失败, 对端返回 %s", r.ContentToString()))
	}
	return result, nil
}
