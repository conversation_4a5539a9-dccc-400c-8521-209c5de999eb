package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"tianyan-crawler/internal/common/logger"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/yuncharge/api/common"

	"github.com/riete/errors"
)

const (
	StationDetailURL = "https://gw3.ykccn.com/api/omp/mt/os/powerStation/queryStationDetail"
)

type StationDetailParams struct {
	UserId       string `json:"userId"`
	CityId       string `json:"cityId"`
	StationId    string `json:"stationId"`
	Longitude    string `json:"longitude"`
	Latitude     string `json:"latitude"`
	TerminalType string `json:"terminalType"`
	AppTag       string `json:"appTag"`
}

type StationDetailData struct {
	StationId                string `json:"stationId"`
	StationName              string `json:"stationName"`
	StationAddr              string `json:"stationPosition"`
	OperatorName             string `json:"siteOperator"`
	OperatorTel              string `json:"operatorTel"`
	FastCharge               string `json:"directNum"`
	SlowCharge               string `json:"inteNum"`
	Lon                      string `json:"longitude"`
	Lat                      string `json:"latitude"`
	BusinessTime             string `json:"businessTime"`
	StationLocationSettingId string `json:"stationLocationSettingId"`
	ParkFeeDesc              string `json:"parkingFee"`
	StationRemark            string `json:"stationRemark"`
	ServiceProvider          string `json:"subjectBusinessLicence"`
	TaxService               string `json:"subjectName"`
	LabelList                []struct {
		LabelName string `json:"labelName"`
	} `json:"labelList"`
}

type StationDetailResp struct {
	PageIndex  int64             `json:"pageIndex"`
	TotalPage  int64             `json:"totalPage"`
	Header     RespHeader        `json:"header"`
	PageSize   int64             `json:"pageSize"`
	Body       StationDetailData `json:"body"`
	TotalCount int64             `json:"totalCount"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) ([]map[string]any, error) {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	stationLocation := ""
	if sdr.Body.StationLocationSettingId == "1000125" {
		stationLocation = "地上"
	} else if sdr.Body.StationLocationSettingId == "1000126" {
		stationLocation = "地下"
	}

	tips := []string{}
	for _, tag := range sdr.Body.LabelList {
		if tag.LabelName != "" {
			tips = append(tips, tag.LabelName)
		}
	}

	am := []map[string]any{
		{
			"id":                   rd,
			"jobId":                rd,
			"channel":              channel,
			"city":                 city,
			"province":             province,
			"stationId":            sdr.Body.StationId,
			"stationName":          sdr.Body.StationName,
			"stationAddr":          sdr.Body.StationAddr,
			"operatorName":         sdr.Body.OperatorName,
			"siteOperator":         sdr.Body.OperatorName,
			"operatorTel":          sdr.Body.OperatorTel,
			"fastCharge":           sdr.Body.FastCharge,
			"slowCharge":           sdr.Body.SlowCharge,
			"lon":                  sdr.Body.Lon,
			"lat":                  sdr.Body.Lat,
			"stationLocation":      stationLocation, // 标识地上地下
			"businessTime":         sdr.Body.BusinessTime,
			"parkFeeDesc":          sdr.Body.ParkFeeDesc,
			"supportingFacilities": strings.Join(tips, ","),
			"openRemark":           sdr.Body.StationRemark,
			"stationRemark":        sdr.Body.StationRemark,
			"serviceProvider":      sdr.Body.ServiceProvider,
			"taxService":           sdr.Body.TaxService,
			"runTime":              ct,
		},
	}
	return am, nil
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	stationDetailParam := StationDetailParams{
		TerminalType: "2",
		StationId:    p.StationID,
		Latitude:     p.Latitude,
		Longitude:    p.Longitude,
		CityId:       p.BusinessID,
		UserId:       a.accountInfo.UserID,
		AppTag:       "V2.0",
	}

	sdp, err := json.Marshal(stationDetailParam)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("stationDetailParam 序列化失败 %s", err)))
	}

	signature := ParamsSign(sdp, HmacMd5Key)
	req := YunChargeReq{
		Header: YunChargeReqHeader{
			Version:       "0",
			IsSync:        "0",
			IsChannelUser: "1",
			Token:         a.accountInfo.Token,
			AccessKey:     AccessKey,
			Timestamp:     signature.ts,
			Nonce:         signature.nonce,
			Sign:          signature.sign,
		},
		Body: string(sdp),
	}

	encryptParams, err := req.Encrypt(AESKey, AESIV)
	if err != nil {
		logger.Error(errors.New(fmt.Sprintf("encryptParams 加密失败 %s", err)))
		return nil, errors.NewFromErr(err)
	}

	r, err := common.DoRequest(StationDetailURL, encryptParams, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	_ = json.Unmarshal(r.Content(), &sdr)
	if sdr.Header.ResultCode != "0" {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := sdr.convert(a.targetDesc, a.gParam.Province, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点详情数据转换失败, 对端返回 %s", r.ContentToString()))
	}

	return result, nil
}
