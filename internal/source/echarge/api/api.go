package api

import (
	"encoding/json"
	"sort"

	"github.com/riete/errors"
	"github.com/riete/exec"

	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/util"
)

const (
	SignatureJSPath     = "scripts/echarge/signature.js"
	SignatureListJSPath = "scripts/echarge_station_list/signature.js"
)

type EChargeApi interface {
	GetStationList(param Param) ([]map[string]any, errors.Error)
	GetStationDetail(param Param) ([]map[string]any, errors.Error)
	GetStationPrice(param Param) ([]map[string]any, errors.Error)
	GetChargingGunList(param Param) ([]map[string]any, errors.Error)
}

type AccountInfo struct {
	Token string `json:"token"`
}

type GlobalParam struct {
	TaskId     string `json:"taskId"`
	TemplateId string `json:"templateId"`
	Channel    string `json:"channel"`
	ScriptUrl  string `json:"scriptUrl"`
	BizType    string `json:"bizType"`
	Province   string `json:"province"`
	City       string `json:"city"`
	CityCode   string `json:"cityCode"`
}

func (gp GlobalParam) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(gp)
}

type Param struct {
	StationID   string `json:"station_id"`
	StationName string `json:"station_name"`
	CITY        string `json:"CITY,omitempty"`
	City        string `json:"city,omitempty"`
	CityCode    string `json:"CITY_CODE,omitempty"`
	Lat         string `json:"LAT"`
	Lgn         string `json:"LGN"`
	Latitude    string `json:"lat"`
	Longitude   string `json:"lon"`
}

func (p Param) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(p)
}

type TaskMessage struct {
	TaskInstanceId string      `json:"taskInstanceId"`
	Target         string      `json:"target"`
	AccountInfo    AccountInfo `json:"accountInfo"`
	GlobalParam    GlobalParam `json:"globalParam"`
	Params         []Param     `json:"Params"`
}

func (msg *TaskMessage) Marshal(stmsg task.StartTaskMessage) error {
	s, err := json.Marshal(stmsg)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(s, msg); err != nil {
		return err
	}
	return nil
}

type Api struct {
	targetDesc  string
	accountInfo AccountInfo
	gParam      GlobalParam
}

func (a Api) invokeSignatureScript(bodyJSONStr, headerStr string) (string, errors.Error) {
	r := exec.NewCmdRunner("node", SignatureJSPath, bodyJSONStr, headerStr)
	s, e, err := r.RunWithSeparatedOutput()
	if err != nil {
		return s, errors.NewFromErr(err).Trace("参数加密失败: " + e)
	}
	return s, nil
}

func (a Api) generateSiginature(body []byte, headers map[string]string) (map[string]string, errors.Error) {
	kArr := []string{}
	for k := range headers {
		kArr = append(kArr, k)
	}
	sort.Strings(kArr)

	hs := ""
	for i := range kArr {
		hs += kArr[i] + headers[kArr[i]]
	}

	r, err := a.invokeSignatureScript(string(body), hs)

	signature := make(map[string]string)
	json.Unmarshal([]byte(r), &signature)

	return signature, err
}

func (a Api) invokeSignatureListScript(bodyJSONStr, headerStr string) (string, errors.Error) {
	r := exec.NewCmdRunner("node", SignatureListJSPath, bodyJSONStr, headerStr)
	s, e, err := r.RunWithSeparatedOutput()
	if err != nil {
		return s, errors.NewFromErr(err).Trace("参数加密失败: " + e)
	}
	return s, nil
}

func (a Api) generateListSiginature(body []byte, headers map[string]string) (map[string]string, errors.Error) {
	kArr := []string{}
	for k := range headers {
		kArr = append(kArr, k)
	}
	sort.Strings(kArr)

	hs := ""
	for i := range kArr {
		hs += kArr[i] + headers[kArr[i]]
	}

	r, err := a.invokeSignatureListScript(string(body), hs)

	signature := make(map[string]string)
	json.Unmarshal([]byte(r), &signature)

	return signature, err
}

func New(targetDesc string, tm TaskMessage) Api {
	return Api{
		targetDesc:  targetDesc,
		accountInfo: tm.AccountInfo,
		gParam:      tm.GlobalParam,
	}
}
