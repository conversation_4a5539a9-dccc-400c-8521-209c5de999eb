package util

import (
	"encoding/hex"
	"math/big"

	"github.com/tjfoc/gmsm/sm2"
)

func SM2Decode(data string) ([]byte, error) {
	// js 加密的密文，需要添加 04 标识
	cipherBytes, err := hex.DecodeString(data)
	if err != nil {
		return nil, err
	}

	// 还原私钥结构体
	// 公钥 移除 04 以64位拆分两段
	X, _ := hex.DecodeString("60e035a7682d5320e9954defc759cfa4041f14cd34825db72e47c3096ac222a1")
	Y, _ := hex.DecodeString("f18c4384c7e602ca3586c1db7bb048cce092e18b618ed2fb841cea91c58d73cb")
	// 私钥
	D, _ := hex.DecodeString("04a9122a9bf5bbe30b0b29bdd5c9e35a1fb8f4f30a58fbe5daa0c92bc3930503")

	pubX := new(big.Int).SetBytes(X)
	pubY := new(big.Int).SetBytes(Y)
	priD := new(big.Int).SetBytes(D)

	sm2PriKey := &sm2.PrivateKey{
		PublicKey: sm2.PublicKey{
			Curve: sm2.P256Sm2(),
			X:     pubX,
			Y:     pubY,
		},
		D: priD,
	}

	// 私钥解密
	decrypt, err := sm2.Decrypt(sm2PriKey, cipherBytes, sm2.C1C3C2)
	if err != nil {
		return nil, err
	}

	return decrypt, nil
}
