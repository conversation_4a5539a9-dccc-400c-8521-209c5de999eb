package util

import (
	"math/rand"
	"strings"
)

func GenerateRequestID() string {
	const hexChars = "0123456789abcdef"

	// 创建一个36位的数组，与JS代码匹配
	e := make([]byte, 36)

	// 用随机的十六进制字符填充所有位置
	for t := 0; t < 36; t++ {
		e[t] = hexChars[rand.Intn(16)]
	}

	// 设置特定位置的值
	e[14] = '4' // 第14位设为"4"

	// 第19位设置为特定值，使用与JS代码相同的位运算
	val := e[19] - '0'
	if val > 9 {
		val = val - ('a' - '0') + 10 // 将十六进制字符转换为数值
	}
	e[19] = hexChars[((3 & int(val)) | 8)]

	// 设置连字符位置
	e[8] = '-'
	e[13] = '-'
	e[18] = '-'
	e[23] = '-'

	// 将字节切片转换为字符串并移除连字符
	return strings.ReplaceAll(string(e), "-", "")
}
