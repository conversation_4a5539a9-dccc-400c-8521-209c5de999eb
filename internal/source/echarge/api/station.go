package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"tianyan-crawler/internal/common/request"
	"tianyan-crawler/internal/source/echarge/api/common"
	"tianyan-crawler/internal/source/echarge/api/util"
	"time"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://applite-evone-wx.echargenet.com/echargeassets/open/es/findStation"
)

type StationData struct {
	StationId   string `json:"id"`
	StationName string `json:"name"`
	Location    struct {
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
	} `json:"location"`
	FastCharge     int `json:"dcTotal"`
	FreeFastCharge int `json:"dcAvailable"`
	SlowCharge     int `json:"acTotal"`
	FreeSlowCharge int `json:"acAvailable"`
}

type Pager struct {
	Total       int64 `json:"total"`
	Offset      int64 `json:"offset"`
	Limit       int64 `json:"limit"`
	TotalPage   int64 `json:"totalPage"`
	CurrentPage int64 `json:"currentPage"`
}

type StationListResp struct {
	Code      float32       `json:"code"`
	Timestamp string        `json:"timestamp"`
	Message   string        `json:"message"`
	Data      []StationData `json:"data"`
	Page      Pager         `json:"page"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Data {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"lat":            strconv.FormatFloat(d.Location.Latitude, 'f', -1, 64),
			"lon":            strconv.FormatFloat(d.Location.Longitude, 'f', -1, 64),
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac
}

type Point struct {
	Direction int16   `json:"direction"`
	Lat       float64 `json:"lat"`
	Lng       float64 `json:"lng"`
}

type ParamStation struct {
	IsHaveUnionTravel bool `json:"isHaveUnionTravel"`
	IsHaveStopStation bool `json:"isHaveStopStation"`
}

type TopLabelItem struct {
	Name       string  `json:"name"`
	Code       string  `json:"code"`
	Alert      *string `json:"alert"`
	Checked    bool    `json:"checked"`
	AssetsType int16   `json:"assetsType"`
	ImgUrl     *string `json:"imgUrl"`
}

type LabelItemSonItem struct {
	Code    string `json:"code"`
	Checked bool   `json:"checked"`
	Name    string `json:"name"`
	Alert   string `json:"alert,omitempty"`
}

type LabelItemSon struct {
	Code      string             `json:"code"`
	GroupName string             `json:"groupName"`
	Relation  string             `json:"relation"`
	Labels    []LabelItemSonItem `json:"labels"`
}

type LabelItem struct {
	Code       string         `json:"code"`
	Checked    bool           `json:"checked"`
	Son        []LabelItemSon `json:"son"`
	Name       string         `json:"name"`
	AssetsType string         `json:"assetsType"`
}

type StationListBody struct {
	CityCode      string         `json:"cityCode"`
	Point         Point          `json:"point"`
	Radius        int64          `json:"radius"`
	StakePowerMin int16          `json:"stakePowerMin"`
	StakePowerMax int16          `json:"stakePowerMax"`
	StationType   int16          `json:"stationType"`
	SearchType    int16          `json:"searchType"`
	SortBy        int16          `json:"sortBy"`
	ParamStation  ParamStation   `json:"paramStation"`
	TopLabels     []TopLabelItem `json:"topLabels"`
	Labels        struct {
		Code      string      `json:"code"`
		GroupName string      `json:"groupName"`
		Labels    []LabelItem `json:"labels"`
	} `json:"labels"`
}

func (a Api) GetStationList(p Param) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)

	body := StationListBody{
		CityCode: "110100", // 读 header 里的 city_code
		Point: struct {
			Direction int16   `json:"direction"`
			Lat       float64 `json:"lat"`
			Lng       float64 `json:"lng"`
		}{
			Direction: 0,
			Lat:       lat,
			Lng:       lng,
		},
		Radius:        10000,
		StakePowerMin: 0,
		StakePowerMax: 1000,
		StationType:   55,
		SearchType:    3,
		SortBy:        0,
		ParamStation: struct {
			IsHaveUnionTravel bool `json:"isHaveUnionTravel"`
			IsHaveStopStation bool `json:"isHaveStopStation"`
		}{
			IsHaveUnionTravel: true,
			IsHaveStopStation: true,
		},
		TopLabels: []TopLabelItem{
			{Name: "免费停车", Code: "10010108001", Alert: nil, Checked: false, AssetsType: 1, ImgUrl: nil},
			{Name: "高速路站", Code: "10010107004", Alert: nil, Checked: false, AssetsType: 1, ImgUrl: nil},
			{Name: "优惠站", Code: "10010107007", Alert: nil, Checked: false, AssetsType: 1, ImgUrl: nil},
			{Name: "优质站", Code: "10010107008", Alert: nil, Checked: false, AssetsType: 1, ImgUrl: nil},
			{Name: "空闲", Code: "10010107001", Alert: nil, Checked: false, AssetsType: 1, ImgUrl: nil},
		},
		Labels: struct {
			Code      string      "json:\"code\""
			GroupName string      "json:\"groupName\""
			Labels    []LabelItem "json:\"labels\""
		}{
			Code:      "1001",
			GroupName: "网点类型",
			Labels: []LabelItem{
				{
					Code:    "100101",
					Checked: true,
					Son: []LabelItemSon{
						{
							Code:      "10010101",
							GroupName: "运营状态",
							Relation:  "OR",
							Labels: []LabelItemSonItem{
								{Code: "10010101001", Checked: true, Name: "营业中"},
								{Code: "10010101002", Checked: false, Name: "暂停运营"},
							},
						},
						{
							Code:      "10010102",
							GroupName: "运营商",
							Relation:  "OR",
							Labels: []LabelItemSonItem{
								{Code: "10010102001", Checked: true, Name: "自营"},
								{Code: "10010102002", Checked: true, Name: "合作站"},
								{Code: "10010102003", Checked: true, Name: "互联互通"},
								{Code: "10010102004", Checked: false, Name: "个人桩"},
							},
						},
						{
							Code:      "10010103",
							GroupName: "电站类型",
							Relation:  "OR",
							Labels: []LabelItemSonItem{
								{Code: "10010103001", Checked: true, Name: "对外开放"},
								{Code: "10010103002", Checked: false, Name: "不对外开放"},
							},
						},
						{
							Code:      "10010106",
							GroupName: "特色功能",
							Relation:  "AND",
							Labels: []LabelItemSonItem{
								{Code: "10010107002", Checked: false, Name: "即插即充"},
								{Code: "10010107005", Checked: false, Name: "CPU即插即充"},
								{Code: "10010107003", Checked: false, Name: "车电包", Alert: "以下电站支持车电包付款，使用车电包支付与其他优惠互斥"},
							},
						},
						{
							Code:      "10010104",
							GroupName: "充电桩类型",
							Relation:  "OR",
							Labels: []LabelItemSonItem{
								{Code: "10010104001", Checked: true, Name: "直流快充"},
								{Code: "10010104002", Checked: true, Name: "交流慢充"},
								{Code: "10010104004", Checked: false, Name: "有序充电"},
								{Code: "10010104005", Checked: false, Name: "V2G"},
							},
						},
						{
							Code:      "10010106",
							GroupName: "充电桩电压",
							Relation:  "OR",
							Labels: []LabelItemSonItem{
								{Code: "10010106001", Checked: true, Name: "低于700V"},
								{Code: "10010106002", Checked: true, Name: "700~1000v"},
								{Code: "10010106003", Checked: true, Name: "高于1000v"},
							},
						},
					},
					Name:       "充电站",
					AssetsType: "1",
				},
				// {
				// 	Code:       "100102",
				// 	Checked:    true,
				// 	Son:        []LabelItemSon{},
				// 	Name:       "营业厅",
				// 	AssetsType: "2",
				// },
			},
		},
	}

	bJSON, err := json.Marshal(&body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化站点列表Body失败")
	}

	ts := strconv.Itoa(int(time.Now().UnixMilli()))
	echargeHeaders := map[string]string{
		"x-evone-meta-appid":           "ecdwx",
		"x-evone-auth-ticket":          a.accountInfo.Token,
		"x-evone-device":               "",
		"x-evone-version":              "3.4.2",
		"x-evone-api":                  "1.0.0",
		"x-evone-request-id":           util.GenerateRequestID(),
		"x-evone-profile":              "1",
		"x-evone-area":                 p.CityCode,
		"x-evone-request-timeInterval": ts,
	}

	signHeader, err := a.generateListSiginature(bJSON, echargeHeaders)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("站点列表生成签名失败")
	}

	headers := map[string]string{
		"x-evone-signature": signHeader["signature"],
		"Content-Type":      "application/json;charset=UTF-8",
		"userToken":         a.accountInfo.Token,
		"channel":           "ecdwx",
		"timestamp":         ts,
		"Connection":        "close",
	}

	r, resErr := common.DoRequest(StationListURL, bJSON, 6, request.WithHeader(echargeHeaders), request.WithHeader(headers))
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := new(StationListResp)
	_ = json.Unmarshal(r.Content(), sr)
	if sr.Code != 1 {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}
	return sr.convert(a.targetDesc, p.CITY), nil
}
