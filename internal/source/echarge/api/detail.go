package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/echarge/api/common"
	"tianyan-crawler/internal/source/echarge/api/util"
)

const (
	StationDetailURL = "https://applite-evone-wx.echargenet.com/echargeassets/open/es/queryStationInfo"
)

type StationDetailBody struct {
	StationID string `json:"id"`
	Token     string `json:"token,omitempty"`
}

type StationDetailData struct {
	StationId    string  `json:"id"`
	StationName  string  `json:"stationName"` // 站点名称
	StationAddr  string  `json:"address"`
	Lon          float32 `json:"lng"`
	Lat          float32 `json:"lat"`
	BusinessTime string  `json:"openTimeInfo"` // 营运时间
	FastCharge   int     `json:"dcTotal"`
	SlowCharge   int     `json:"acTotal"`
	ParkFeeDesc  string  `json:"priceParking"`
	OperatorName string  `json:"operatorName"` // 运营商名称
	OperatorId   string  `json:"operatorId"`   // 运营商ID
	OperatorTel  string  `json:"phone"`        // 运营商手机号码
	SiteOperator string  `json:"siteOperator"` // 联系人名称
	Open         int     `json:"open"`
	LabelsAll    []struct {
		Name string `json:"name"`
		Type string `json:"type"`
	} `json:"labelsAll"`
	Status int `json:"status"`
}

type StationDetailResp struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
	Data      string `json:"data"`
	Page      string `json:"page"`
}

var OperatorTypeEnum = []string{"自营", "合作站", "互联互通", "个人桩"}

func (sdr *StationDetailResp) convert(channel, province, city string) ([]map[string]any, error) {
	if sdr.Data == "" {
		return nil, errors.New(sdr.Data).Trace("站点详情返回值 Data 为空")
	}

	r, err := util.SM2Decode(sdr.Data)
	if err != nil {
		return nil, errors.New(sdr.Data).Trace("http响应解密失败")
	}

	sdd := StationDetailData{}
	err = json.Unmarshal(r, &sdd)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("响应 Data 反序列化失败")
	}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	openMark := ""
	if sdd.Open == 1 {
		openMark = "对外开放"
	} else if sdd.Open == 0 {
		openMark = "不对外开放"
	}

	operatorType := ""
	tips := []string{}
	for _, label := range sdd.LabelsAll {
		tips = append(tips, label.Name)
		if util.ContainsStr(OperatorTypeEnum, label.Name) {
			operatorType = label.Name
		}
	}

	stationStatus := "已下线"
	if sdd.Status == 3 {
		stationStatus = "营业中"
	}

	am := []map[string]any{
		{
			"id":            rd,
			"jobId":         rd,
			"channel":       channel,
			"city":          city,
			"province":      province,
			"stationId":     sdd.StationId,
			"stationName":   sdd.StationName,
			"stationAddr":   sdd.StationAddr,
			"operatorId":    sdd.OperatorId,
			"operatorName":  sdd.OperatorName,
			"operatorType":  operatorType,
			"operatorTel":   sdd.OperatorTel,
			"fastCharge":    sdd.FastCharge,
			"slowCharge":    sdd.SlowCharge,
			"lon":           sdd.Lon,
			"lat":           sdd.Lat,
			"businessTime":  sdd.BusinessTime,
			"parkFeeDesc":   sdd.ParkFeeDesc,
			"openRemark":    openMark,
			"tips":          strings.Join(tips, ","),
			"stationStatus": stationStatus,
			"runTime":       ct,
		},
	}
	return am, nil
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	body := StationDetailBody{
		StationID: p.StationID,
		Token:     a.accountInfo.Token,
	}

	bJSON, err := json.Marshal(&body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化站点详情 Body 失败")
	}

	echargeHeaders := map[string]string{
		"x-evone-auth-ticket": a.accountInfo.Token,
		"x-evone-device":      "ecdH5",
		"x-evone-version":     "3070000",
		"x-evone-api":         "1.0.0",
		"x-evone-meta-appid":  "1233123",
		"x-evone-request-id":  "",
		"x-evone-profile":     "1",
		"x-evone-area":        "12312",
	}

	signHeader, err := a.generateSiginature(bJSON, echargeHeaders)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("站点详情生成签名失败")
	}

	headers := map[string]string{
		"x-evone-signature": signHeader["signature"],
		"Content-type":      "application/json;charset=UTF-8",
		"channel":           "ecdH5",
	}

	r, resErr := common.DoRequest(StationDetailURL, bJSON, 6, request.WithHeader(echargeHeaders), request.WithHeader(headers))
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := new(StationDetailResp)
	_ = json.Unmarshal(r.Content(), sdr)

	if sdr.Code != 1 {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := sdr.convert(a.targetDesc, a.gParam.Province, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点详情数据转换失败, %s", err.Error()))
	}

	return result, nil
}
