package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/echarge/api/common"

	"github.com/riete/errors"
)

const (
	ChargingGunURL = "https://applite-evone-wx.echargenet.com/echargeapi/open/station/getChargerList"
)

type ChargingGunBody struct {
	StationID string `json:"id"`
	Token     string `json:"token,omitempty"`
}

type ChargingGunResp struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
	Data      struct {
		Index      int               `json:"index"`
		Quantity   int               `json:"quantity"`
		Name       string            `json:"name"`
		Equipments []ChargingGunData `json:"equipments"`
	} `json:"data"`
	Page string `json:"page"`
}

func (cgr ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	r := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgr.Data.Equipments {
		gunType := "直流"
		if gun.ChargerModel.PlugType == "1" {
			gunType = "交流"
		}

		r = append(r, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       gun.GunId,
			"gunType":     gunType,
			"power":       strconv.FormatFloat(gun.OutPower, 'f', 1, 64),
			"runTime":     ct,
		})
	}

	return r, nil
}

type ChargingGunData struct {
	GunId        string  `json:"chargerCode"`
	OutPower     float64 `json:"outPower"`
	ChargerModel struct {
		PlugType string `json:"PlugType"`
	} `json:"chargerModel"`
}

func (a Api) GetChargingGunList(p Param) ([]map[string]any, errors.Error) {
	body := ChargingGunBody{
		StationID: p.StationID,
		Token:     a.accountInfo.Token,
	}

	bJSON, err := json.Marshal(&body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化充电枪列表 Body 失败")
	}

	echargeHeaders := map[string]string{
		"x-evone-auth-ticket": a.accountInfo.Token,
		"x-evone-device":      "ecdH5",
		"x-evone-version":     "3070000",
		"x-evone-api":         "1.0.0",
		"x-evone-meta-appid":  "1233123",
		"x-evone-request-id":  "",
		"x-evone-profile":     "1",
		"x-evone-area":        "12312",
	}

	signHeader, err := a.generateSiginature(bJSON, echargeHeaders)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("站点价格生成签名失败")
	}
	time.Sleep(time.Millisecond * 100)
	headers := map[string]string{
		"x-evone-signature": signHeader["signature"],
		"Content-type":      "application/json;charset=UTF-8",
		"channel":           "ecdH5",
	}
	r, resErr := common.DoRequest(ChargingGunURL, bJSON, 6, request.WithHeader(echargeHeaders), request.WithHeader(headers))
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	sr := new(ChargingGunResp)
	_ = json.Unmarshal(r.Content(), sr)

	if sr.Code != 1 {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := sr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
