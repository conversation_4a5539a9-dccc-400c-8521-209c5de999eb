package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/echarge/api/common"
)

const (
	PriceListURL = "https://applite-evone-wx.echargenet.com/echargeassets/open/es/station/rateModel"
)

type StationPriceBody struct {
	StationID string `json:"stationId"`
	Token     string `json:"token,omitempty"`
}

type PriceData struct {
	StartTime    string `json:"stTime"`
	StopTime     string `json:"spTime"`
	ElecPrice    string `json:"powerRate"`
	ServicePrice string `json:"serviceFree"`
	TotalPrice   string `json:"totalFree"`
}

type PriceList []PriceData

func (pl PriceList) handle(
	hoursPerDay int,
	pileType,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	for _, price := range pl {
		startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
		if err != nil {
			// 返回值兼容处理，不通用
			startTime, err = time.Parse(time.TimeOnly, price.StartTime+":00:00")
			if err != nil {
				return pd, err
			}
		}
		stopTime, err := time.Parse(time.TimeOnly, price.StopTime+":00")
		if err != nil {
			// 返回值兼容处理，不通用
			if price.StopTime == "24" {
				price.StopTime = "23"
			}
			stopTime, err = time.Parse(time.TimeOnly, price.StopTime+":00:00")
			if err != nil {
				return pd, err
			}
		}

		start := startTime.Hour()
		end := stopTime.Hour()
		if startTime.Add(time.Minute).Day() != stopTime.Add(time.Minute).Day() {
			// StopTime: 23:59
			end = hoursPerDay
		}

		for i := start; i < end; i++ {
			pd[i] = map[string]any{
				"id":           rd,
				"jobId":        rd,
				"stationId":    stationId,
				"stationName":  stationName,
				"city":         city,
				"province":     province,
				"stationLat":   latitude,
				"stationLng":   longitude,
				"accountType":  "普通用户",
				"crawlDay":     time.Now().Format("********"),
				"crawlTime":    time.Now().Format(time.TimeOnly),
				"channel":      targetDesc,
				"operName":     targetDesc,
				"pileType":     pileType,
				"timeInterval": fmt.Sprintf("%02d", i),
				"elecPrice":    price.ElecPrice,
				"servicePrice": price.ServicePrice,
				"totalPrice":   price.TotalPrice,
			}
		}
	}
	return pd, nil
}

type StationPriceData struct {
	RateModelAc PriceList `json:"rateModelAc"`
	RateModelDc PriceList `json:"rateModelDc"`
}

type StationPriceResp struct {
	Code      int              `json:"code"`
	Message   string           `json:"message"`
	Timestamp string           `json:"timestamp"`
	Data      StationPriceData `json:"data"`
	Page      string           `json:"page"`
}

var PileTypeMap = map[string]string{
	"fastGun": "1",
	"slowGun": "2",
}

func (spr StationPriceResp) convert(
	targetDesc,
	province,
	city,
	stationId,
	stationName,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	r := []map[string]any{}
	// fastGun
	if len(spr.Data.RateModelDc) != 0 {
		dc, err := spr.Data.RateModelDc.handle(
			hoursPerDay,
			PileTypeMap["fastGun"],
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return r, err
		}
		r = append(r, dc...)
	}

	// slowGun
	if len(spr.Data.RateModelAc) != 0 {
		ac, err := spr.Data.RateModelAc.handle(
			hoursPerDay,
			PileTypeMap["slowGun"],
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return r, err
		}
		r = append(r, ac...)
	}

	return r, nil
}

func (a Api) GetStationPrice(p Param) ([]map[string]any, errors.Error) {
	body := StationPriceBody{
		StationID: p.StationID,
		Token:     a.accountInfo.Token,
	}

	bJSON, err := json.Marshal(&body)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("序列化站点价格 Body 失败")
	}

	echargeHeaders := map[string]string{
		"x-evone-auth-ticket": a.accountInfo.Token,
		"x-evone-device":      "ecdH5",
		"x-evone-version":     "3070000",
		"x-evone-api":         "1.0.0",
		"x-evone-meta-appid":  "1233123",
		"x-evone-request-id":  "",
		"x-evone-profile":     "1",
		"x-evone-area":        "12312",
	}

	signHeader, err := a.generateSiginature(bJSON, echargeHeaders)
	if err != nil {
		return nil, errors.New(err.Error()).Trace("站点价格生成签名失败")
	}

	headers := map[string]string{
		"x-evone-signature": signHeader["signature"],
		"Content-type":      "application/json;charset=UTF-8",
		"channel":           "ecdH5",
	}

	r, resErr := common.DoRequest(PriceListURL, bJSON, 6, request.WithHeader(echargeHeaders), request.WithHeader(headers))
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	sr := new(StationPriceResp)
	_ = json.Unmarshal(r.Content(), sr)
	if sr.Code != 1 {
		return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := sr.convert(
		a.targetDesc,
		a.gParam.Province,
		p.City,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
