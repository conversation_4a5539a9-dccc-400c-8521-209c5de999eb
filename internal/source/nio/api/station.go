package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
	"tianyan-crawler/internal/source/nio/api/common"
	"tianyan-crawler/internal/source/nio/api/util"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://app.niopower.com/pe/app/map/v2/mini/power/around"
)

type StationData struct {
	StationId      string `json:"id"`
	StationName    string `json:"name"`
	Location       string `json:"location"`
	FastCharge     int    `json:"charger_total_number_dc"`
	FreeFastCharge int    `json:"charger_free_number_dc"`
	SlowCharge     int    `json:"charger_total_number_ac"`
	FreeSlowCharge int    `json:"charger_free_number_ac"`
	// 站点详情
	DetailJSONData
	// StationAddr string `json:"address"`
	// OperatorId  string `json:"operator_id"`
	// Tags        []any  `json:"tags"`
	// 站点价格
	PriceJSONData
	// CurrentFee        float64 `json:"current_fee"`
	// CurrentElecFee    float64 `json:"current_elec_fee"`
	// CurrentServiceFee float64 `json:"current_service_fee"`
}

type DetailJSONData struct {
	StationAddr string `json:"address"`
	OperatorId  string `json:"operator_id"`
	FastCharge  int    `json:"fast_charge"`
	SlowCharge  int    `json:"slow_charge"`
	Tags        []any  `json:"tags"`
}

type PriceJSONData struct {
	CurrentFee        float64 `json:"current_fee"`
	CurrentElecFee    float64 `json:"current_elec_fee"`
	CurrentServiceFee float64 `json:"current_service_fee"`
}

type StationListResp struct {
	RequestID   string `json:"request_id"`
	ServerTime  int64  `json:"server_time"`
	ResultCode  string `json:"result_code"`
	EncryptType int    `json:"encrypt_type"`
	Data        struct {
		RecommendationResources   []StationData `json:"recommendation_resources"`
		UnRecommendationResources []StationData `json:"un_recommendation_resources"`
	} `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) ([]map[string]any, error) {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Data.RecommendationResources {
		location := strings.Split(d.Location, ",")
		d.DetailJSONData.FastCharge = d.FastCharge
		d.DetailJSONData.SlowCharge = d.SlowCharge
		detailJSON, err := json.Marshal(d.DetailJSONData)
		if err != nil {
			detailJSON = []byte{}
		}
		priceJSON, err := json.Marshal(d.PriceJSONData)
		if err != nil {
			priceJSON = []byte{}
		}
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"lon":            location[0],
			"lat":            location[1],
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"detailJson":     string(detailJSON),
			"priceJson":      string(priceJSON),
			"runTime":        ct,
		})
	}
	for _, d := range slr.Data.UnRecommendationResources {
		location := strings.Split(d.Location, ",")
		d.DetailJSONData.FastCharge = d.FastCharge
		d.DetailJSONData.SlowCharge = d.SlowCharge
		detailJSON, err := json.Marshal(d.DetailJSONData)
		if err != nil {
			detailJSON = []byte{}
		}
		priceJSON, err := json.Marshal(d.PriceJSONData)
		if err != nil {
			priceJSON = []byte{}
		}
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"lon":            location[0],
			"lat":            location[1],
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"detailJson":     string(detailJSON),
			"priceJson":      string(priceJSON),
			"runTime":        ct,
		})
	}
	return ac, nil
}

func (a Api) GetStationList(p Param) ([]map[string]any, errors.Error) {
	ts := time.Now().Unix()
	params := map[string]string{
		"app_id":        "100443",
		"app_ver":       "4.5.0",
		"lang":          "zh-cn",
		"region":        "cn",
		"latitude":      p.Lat,
		"longitude":     p.Lgn,
		"distance":      "30000",
		"device_id":     a.accountInfo.DeviceID,
		"terminal":      `{"name":"apple","model":"apple"}`,
		"with_distance": "true",
		"with_fee":      "true",
		"with_merge":    "false",
		"timestamp":     strconv.FormatInt(ts, 10),
	}

	kArr := []string{}
	for k := range params {
		kArr = append(kArr, k)
	}
	sort.Strings(kArr)

	parsedURL, _ := url.Parse(StationListURL)
	s := ""
	query := parsedURL.Query()
	for i := range kArr {
		query.Add(kArr[i], params[kArr[i]])
		if i != 0 {
			s += "&"
		}
		s += kArr[i] + "=" + params[kArr[i]]
	}

	parsedURL.RawQuery = query.Encode()

	signature := util.ParamsSign([]byte(s), "GET", parsedURL.Path, a.accountInfo.Token)
	query.Add("sign", signature)

	siteListURL := StationListURL + "?" + s + "&sign=" + signature
	logger.DetailLogger.Info("SiteDetailURL with params: ", siteListURL)

	headers := map[string]string{
		"content-type":  "application/x-www-form-urlencoded",
		"authorization": a.accountInfo.Token,
		"referer":       "https://servicewechat.com/wx35849c7f0cf7f7a9/132/page-frame.html",
	}

	r, err := common.GetRequest(siteListURL, map[string]string{}, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	slr := StationListResp{}
	_ = json.Unmarshal(r.Content(), &slr)
	if slr.ResultCode != "success" {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := slr.convert(a.targetDesc, p.CITY)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点列表数据转换失败, 对端返回 %s", r.ContentToString()))
	}
	return result, nil
}
