package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/nio/api/common"
	"tianyan-crawler/internal/source/nio/api/util"

	"github.com/riete/errors"
)

const (
	ChargingGunURL = "https://app.niopower.com/pe/app/charge-station/v1/resource/connectors"
)

type ChargingGunData struct {
	GunId   string `json:"connector_id"`
	GunType string `json:"type"`
	Power   string `json:"power"`
}

type ChargingGunResp struct {
	RequestID   string `json:"request_id"`
	ServerTime  int64  `json:"server_time"`
	ResultCode  string `json:"result_code"` // success || sign_failed
	EncryptType int    `json:"encrypt_type"`
	Data        struct {
		List []ChargingGunData `json:"connector_detail_list"`
	} `json:"data"`
}

var PileTypeMap = map[string]string{
	"DC": "直流",
	"AC": "交流",
}

func (cgr ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	guns := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgr.Data.List {
		guns = append(guns, map[string]any{
			"channel":     targetDesc,
			"id":          rd,
			"jobId":       rd,
			"stationId":   stationId,
			"stationName": stationName,
			"city":        city,
			"gunId":       gun.GunId,
			"gunType":     PileTypeMap[gun.GunType],
			"power":       gun.Power,
			"runTime":     ct,
		})
	}

	return guns, nil
}

func (a Api) GetChargingGunList(p Param) ([]map[string]any, errors.Error) {
	ts := time.Now().Unix()
	params := map[string]string{
		"app_id":    "100443",
		"app_ver":   "4.5.0",
		"lang":      "zh-cn",
		"region":    "cn",
		"device_id": a.accountInfo.DeviceID,
		"terminal":  `{"name":"apple","model":"apple"}`,
		"group_id":  p.StationID,
		"timestamp": strconv.FormatInt(ts, 10),
	}

	kArr := []string{}
	for k := range params {
		kArr = append(kArr, k)
	}
	sort.Strings(kArr)

	parsedURL, _ := url.Parse(ChargingGunURL)
	s := ""
	query := parsedURL.Query()
	for i := range kArr {
		query.Add(kArr[i], params[kArr[i]])
		if i != 0 {
			s += "&"
		}
		s += kArr[i] + "=" + params[kArr[i]]
	}

	parsedURL.RawQuery = query.Encode()

	signature := util.ParamsSign([]byte(s), "GET", parsedURL.Path, a.accountInfo.Token)
	query.Add("sign", signature)

	chargingGunURL := ChargingGunURL + "?" + s + "&sign=" + signature
	logger.DetailLogger.Info("ChargingGunURL with params: ", chargingGunURL)
	// fmt.Println("URL with params:", siteDetailURL)

	headers := map[string]string{
		"content-type":  "application/x-www-form-urlencoded",
		"authorization": a.accountInfo.Token,
		"referer":       "https://servicewechat.com/wx35849c7f0cf7f7a9/132/page-frame.html",
	}

	r, err := common.GetRequest(chargingGunURL, map[string]string{}, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("充电枪列表数据失败, http响应: %s", status))
	}

	cgr := ChargingGunResp{}
	_ = json.Unmarshal(r.Content(), &cgr)
	if cgr.ResultCode != "success" {
		return nil, errors.New(fmt.Sprintf("充电枪列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}
	return result, nil
}
