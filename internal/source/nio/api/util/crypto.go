package util

import (
	"encoding/base64"
	"fmt"

	"github.com/riete/errors"
)

const (
	AESKey = "nvukPrdsceR2iOP2"
)

func DecryptData(d string) (string, error) {
	key := []byte(AESKey)
	// 解码base64密文
	ciphertext, err := base64.StdEncoding.DecodeString(d)
	if err != nil {
		return "", errors.New(fmt.Sprintf("ciphertext error %s", err))
	}

	block, err := AesNewCipher(key)
	if err != nil {
		return "", errors.New(fmt.Sprintf("block error %s", err))
	}

	result, err := ECBDecrypt(block, ciphertext)
	if err != nil {
		return "", errors.New(fmt.Sprintf("SiteDetailResp 解密失败 %s", err))
	}

	return string(result), nil
}
