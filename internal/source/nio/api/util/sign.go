package util

import "tianyan-crawler/internal/common/util"

const (
	Secret = "F1D4FBbF32B593e8DF0F2f58Aff5F2C3"
)

func ParamsSign(params []byte, method, path, token string) string {
	signStr := method + path + "?" + string(params) + Secret + token
	// GET/pe/app/map/v2/mini/power/around?app_id=100443&app_ver=4.5.0&device_id=b46a7f4c652347a79514c0b60fb9b303&distance=30000&lang=zh-cn&latitude=31.490549087524414&longitude=120.36434173583984&region=cn&terminal={"name":"apple","model":"apple"}&timestamp=1711708021&with_distance=true&with_fee=true&with_merge=falseF1D4FBbF32B593e8DF0F2f58Aff5F2C3Bearer cc4Lg7PI6Nogr3Z3L-ZpGKsp-Mj-JBXq6wLko9NUVOzJWNEVWte9ZkIW_00aYouGc7tN0BKmhxL60csTmAUlNCuoMaaIvTdIxFmL5TinN5g=
	return util.MD5(signStr)
}
