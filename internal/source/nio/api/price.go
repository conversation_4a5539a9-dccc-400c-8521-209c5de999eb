package api

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	commonUtil "tianyan-crawler/internal/common/util"

	"github.com/riete/errors"
)

const (
	StationPriceURL = "https://app.niopower.com/pe/app/charge-station/v1/"
)

type StationPriceData struct {
	CurrentFee        float64 `json:"current_fee"`
	CurrentElecFee    float64 `json:"current_elec_fee"`
	CurrentServiceFee float64 `json:"current_service_fee"`
}

func (spd StationPriceData) convert(targetDesc, province string, p Param) ([]map[string]any, error) {
	const hoursPerDay = 24

	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	result := make([]map[string]any, hoursPerDay)

	for i := 0; i < hoursPerDay; i++ {
		elecPrice := strconv.FormatFloat(spd.CurrentElecFee, 'f', -1, 64)
		servicePrice := strconv.FormatFloat(spd.CurrentServiceFee, 'f', -1, 64)
		totalPrice := strconv.FormatFloat(spd.CurrentFee, 'f', -1, 64)
		if p.PriceJSON == "" {
			elecPrice = ""
			servicePrice = ""
			totalPrice = ""
		}
		result[i] = map[string]any{
			"id":          rd,
			"jobId":       rd,
			"stationId":   p.StationID,
			"stationName": p.StationName,
			"city":        p.City,
			"province":    province,
			"stationLat":  p.Latitude,
			"stationLng":  p.Longitude,
			// 上面的与站点详情相同
			"accountType":  "普通用户",                           // 写死
			"crawlDay":     time.Now().Format("********"),    // 时间戳 YYYYMMDD
			"crawlTime":    time.Now().Format(time.TimeOnly), // 时间戳 HH:mm:ss
			"channel":      targetDesc,                       // 中文渠道名
			"operName":     targetDesc,                       // 中文渠道名
			"pileType":     "3",                              // 写死
			"timeInterval": fmt.Sprintf("%02d", i),           // 价格时段，00 ~ 23 共24条
			"elecPrice":    elecPrice,                        // 当前电费 current_elec_fee
			"servicePrice": servicePrice,                     // 当前服务费 current_service_fee
			"totalPrice":   totalPrice,                       // 当前总价 current_fee
		}
	}

	return result, nil
}

func (a Api) GetStationPrice(p Param) ([]map[string]any, errors.Error) {
	// ts := time.Now().Unix()
	// params := map[string]string{
	// 	"app_id":    "100443",
	// 	"app_ver":   "4.5.0",
	// 	"lang":      "zh-cn",
	// 	"region":    "cn",
	// 	"device_id": a.accountInfo.DeviceID,
	// 	"terminal":  `{"name":"apple","model":"apple"}`,
	// 	"timestamp": strconv.FormatInt(ts, 10),
	// }

	// kArr := []string{}
	// for k := range params {
	// 	kArr = append(kArr, k)
	// }
	// sort.Strings(kArr)

	// parsedURL, _ := url.Parse(StationDetailURL + p.StationID)
	// s := ""
	// query := parsedURL.Query()
	// for i := range kArr {
	// 	query.Add(kArr[i], params[kArr[i]])
	// 	if i != 0 {
	// 		s += "&"
	// 	}
	// 	s += kArr[i] + "=" + params[kArr[i]]
	// }

	// parsedURL.RawQuery = query.Encode()

	// signature := util.ParamsSign([]byte(s), "GET", parsedURL.Path, a.accountInfo.Token)
	// query.Add("sign", signature)

	// siteDetailURL := StationPriceURL + p.StationID + "?" + s + "&sign=" + signature
	// logger.DetailLogger.Info("SiteDetailURL with params: ", siteDetailURL)

	// headers := map[string]string{
	// 	"content-type":  "application/x-www-form-urlencoded",
	// 	"authorization": a.accountInfo.Token,
	// 	"referer":       "https://servicewechat.com/wx35849c7f0cf7f7a9/132/page-frame.html",
	// }

	// r, err := common.GetRequest(siteDetailURL, map[string]string{}, 6, request.WithHeader(headers))
	// if err != nil {
	// 	return nil, errors.NewFromErr(err)
	// }

	// code, status := r.Status()
	// if code != http.StatusOK {
	// 	return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	// }

	spd := StationPriceData{}
	_ = json.Unmarshal([]byte(p.PriceJSON), &spd)
	// if spr.ResultCode != "success" {
	// 	return nil, errors.New(fmt.Sprintf("获取站点价格数据失败, 对端返回 %s", r.ContentToString()))
	// }

	result, err := spd.convert(a.targetDesc, a.gParam.Province, p)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点站点价格转换失败, 对端返回 %s", err.Error()))
	}
	return result, nil
}
