package common

import (
	"net/http"
	"time"

	"tianyan-crawler/internal/common/proxy"
	"tianyan-crawler/internal/common/request"
)

var commonHeader = map[string]string{
	"Accept":          "*/*",
	"xweb_xhr":        "1",
	"User-Agent":      "Mozilla/5.0 (Linux; U; Android 11; zh-C<PERSON>; M2007J22C Build/RP1A.200720.011) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/69.0.3497.100 UWS/********** Mobile Safari/537.36 UCBS/**********_200831203602 ChannelId(1) NebulaSDK/1.8.100112 Nebula AlipayDefined(nt:WIFI,ws:393|0|2.75) AliApp(AP/10.2.3.8917) AlipayClient/10.2.3.8917 Language/zh-Hans useStatusBar/true isConcaveScreen/true Region/CN NebulaX/1.0.0 Ariver/1.0.0",
	"sec-fetch-site":  "cross-site",
	"sec-fetch-mode":  "cors",
	"sec-fetch-dest":  "empty",
	"accept-language": "zh-C<PERSON>,zh;q=0.9",
}

func NewRequest(proxyIp proxy.ProxyIp, options ...request.Option) *request.Request {
	options = append(
		[]request.Option{
			request.WithDefaultClient(),
			request.WithTimeout(5 * time.Second),
			request.WithProxyFunc(http.ProxyURL(proxyIp.ProxyUrl())),
			request.WithHeader(commonHeader),
		},
		options...,
	)
	return request.NewRequest(options...)
}

func DoRequest(url string, data []byte, retry int, options ...request.Option) (*request.Request, error) {
	var r *request.Request
	var err error
	for i := 0; i < retry; i++ {
		r = NewRequest(proxy.GetIp(), options...)
		err = r.Post(url, data)
		if err != nil {
			time.Sleep(3 * time.Second)
		} else {
			break
		}
	}
	return r, err
}

func GetRequest(url string, query map[string]string, retry int, options ...request.Option) (*request.Request, error) {
	var r *request.Request
	var err error
	for i := 0; i < retry; i++ {
		r = NewRequest(proxy.GetIp(), options...)
		err = r.Get(url, query)
		if err != nil {
			time.Sleep(3 * time.Second)
		} else {
			break
		}
	}
	return r, err
}
