package api

import (
	"encoding/json"

	"github.com/riete/errors"

	"tianyan-crawler/internal/app/task"
	commonUtil "tianyan-crawler/internal/common/util"
)

type AccountInfo struct {
	Token    string `json:"token"`
	DeviceID string `json:"ssdi"`
}

type GlobalParam struct {
	TemplateId string `json:"templateId"`
	Channel    string `json:"channel"`
	ScriptUrl  string `json:"scriptUrl"`
	BizType    string `json:"bizType"`
	Province   string `json:"province"`
	City       string `json:"city"`
}

func (gp GlobalParam) ToAnyMap() map[string]any {
	return commonUtil.ToAnyMapWithJSONTag(gp)
}

type Param struct {
	StationID   string `json:"station_id,omitempty"`
	StationName string `json:"station_name,omitempty"`
	CITY        string `json:"CITY,omitempty"`
	City        string `json:"city,omitempty"`
	CityCode    string `json:"CITY_CODE,omitempty"`
	Lat         string `json:"LAT,omitempty"`
	Lgn         string `json:"LGN,omitempty"`
	Latitude    string `json:"lat,omitempty"`
	Longitude   string `json:"lon,omitempty"`
	DetailJSON  string `json:"detail_json,omitempty"`
	PriceJSON   string `json:"price_json,omitempty"`
}

func (p Param) ToAnyMap() map[string]any {
	return commonUtil.ToAnyMapWithJSONTag(p)
}

type TaskMessage struct {
	TaskInstanceId string      `json:"taskInstanceId"`
	Target         string      `json:"target"`
	AccountInfo    AccountInfo `json:"accountInfo"`
	GlobalParam    GlobalParam `json:"globalParam"`
	Params         []Param     `json:"params"`
}

func (msg *TaskMessage) Marshal(stmsg task.StartTaskMessage) error {
	s, err := json.Marshal(stmsg)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(s, msg); err != nil {
		return err
	}
	return nil
}

type NIOApi interface {
	GetStationList(p Param) ([]map[string]any, errors.Error)
	GetStationDetail(p Param) ([]map[string]any, errors.Error)
	GetStationPrice(p Param) ([]map[string]any, errors.Error)
	GetChargingGunList(p Param) ([]map[string]any, errors.Error)
}

type Api struct {
	targetDesc  string
	accountInfo AccountInfo
	gParam      GlobalParam
}

func New(targetDesc string, tm TaskMessage) Api {
	return Api{
		targetDesc:  targetDesc,
		accountInfo: tm.AccountInfo,
		gParam:      tm.GlobalParam,
	}
}
