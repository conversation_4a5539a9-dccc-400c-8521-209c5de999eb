package api

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	commonUtil "tianyan-crawler/internal/common/util"

	"github.com/riete/errors"
)

const (
	StationDetailURL = "https://app.niopower.com/pe/app/charge-station/v1/"
)

type StationDetailData struct {
	StationAddr string `json:"address"`
	OperatorId  string `json:"operator_id"`
	FastCharge  int    `json:"fast_charge"`
	SlowCharge  int    `json:"slow_charge"`
	Tags        []struct {
		TagName string `json:"tag_name"`
		TagCode string `json:"tag_code"`
	} `json:"tags"`
	// GroupDetail struct {
	// 	StationId    string `json:"id"`
	// 	StationName  string `json:"name"`
	// 	StationAddr  string `json:"address"`
	// 	OperatorId   string `json:"operator_id"`
	// 	OperatorName string `json:"operator_name"`
	// 	Location     string `json:"location"`
	// 	Tips         []struct {
	// 		Icon string `json:"icon"`
	// 		Text string `json:"text"`
	// 	} `json:"tips"`
	// Tags []struct {
	// 	TagName string `json:"tag_name"`
	// } `json:"tags"`
	// } `json:"group_detail"`
	// ChargeConnectorInfo struct {
	// 	FastCharge int `json:"total_connector_number_dc"`
	// 	SlowCharge int `json:"total_connector_number_ac"`
	// } `json:"charge_connector_info"`
}

// type StationDetailResp struct {
// 	RequestID   string `json:"request_id"`
// 	ServerTime  int64  `json:"server_time"`
// 	ResultCode  string `json:"result_code"` // success || sign_failed
// 	EncryptType int    `json:"encrypt_type"`
// 	Data        string `json:"data"`
// }

var OPERATOR_MAP = map[string]string{
	"NIO":   "蔚来能源",
	"XX":    "星星充电",
	"CAS":   "开迈斯",
	"KD":    "快电",
	"YKC":   "云快充",
	"ZSH":   "中石化",
	"SHS":   "南方电网",
	"XP":    "小鹏",
	"WJY":   "蔚景云",
	"JYC":   "均悦充",
	"WM":    "万马爱充",
	"GQ":    "广汽能源",
	"YS":    "云杉智慧",
	"PHS":   "共享私桩",
	"SGC":   "国家电网",
	"XJ":    "小桔",
	"TLD":   "特来电",
	"OTHER": "其他",
}

func (sdd StationDetailData) convert(channel, province string, p Param) ([]map[string]any, error) {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	tips := []string{}
	operatorName := OPERATOR_MAP[sdd.OperatorId]
	for _, tag := range sdd.Tags {
		if tag.TagName != "" {
			tips = append(tips, tag.TagName)
			// 如果字典没有对应的运营商映射，从 tags 里的 operator 取
			if operatorName == "" && tag.TagCode == "operator" {
				operatorName = tag.TagName
			}
		}
	}

	// 兜底，如果 tags 没有对应项
	if operatorName == "" {
		operatorName = channel + "_其他"
	}

	fastCharge := strconv.FormatInt(int64(sdd.FastCharge), 10)
	slowCharge := strconv.FormatInt(int64(sdd.SlowCharge), 10)
	if p.DetailJSON == "" {
		fastCharge = ""
		slowCharge = ""
	}

	am := []map[string]any{
		{
			"id":           rd,              // YYYYMMDDHHmmss + 18位整数随机数
			"jobId":        rd,              // YYYYMMDDHHmmss + 18位整数随机数
			"channel":      channel,         // 中文渠道名
			"city":         p.City,          // 城市
			"province":     province,        // 省份
			"stationId":    p.StationID,     // 站点ID
			"stationName":  p.StationName,   // 站点名称
			"stationAddr":  sdd.StationAddr, // 站点地址
			"operatorId":   sdd.OperatorId,  // 运营商ID(站点列表字段) operator_id
			"operatorName": operatorName,    // 运营商名称，从字典中用 operatorId 映射
			"fastCharge":   fastCharge,      // 快枪数量 charger_total_number_dc
			"slowCharge":   slowCharge,      // 慢枪数量 charger_total_number_ac
			// location 示例 "location": "120.364575,31.483741"
			"lon":     p.Longitude,             // location 字段，用逗号拆分，第一个元素
			"lat":     p.Latitude,              // location 字段，用逗号拆分，第二个元素
			"tips":    strings.Join(tips, ","), // 遍历 tags 数组，tag_name字段用逗号拼接
			"runTime": ct,                      // 时间戳 YYYY-MM-DD HH:mm:ss
		},
	}
	return am, nil
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	// ts := time.Now().Unix()
	// params := map[string]string{
	// 	"app_id":    "100211",
	// 	"app_ver":   "4.5.0",
	// 	"lang":      "zh-cn",
	// 	"region":    "cn",
	// 	"device_id": a.accountInfo.DeviceID,
	// 	"terminal":  `{"name":"Redmi","model":"Redmi"}`,
	// 	"timestamp": strconv.FormatInt(ts, 10),
	// }

	// kArr := []string{}
	// for k := range params {
	// 	kArr = append(kArr, k)
	// }
	// sort.Strings(kArr)

	// parsedURL, _ := url.Parse(StationDetailURL + p.StationID)
	// s := ""
	// query := parsedURL.Query()
	// for i := range kArr {
	// 	query.Add(kArr[i], params[kArr[i]])
	// 	if i != 0 {
	// 		s += "&"
	// 	}
	// 	s += kArr[i] + "=" + params[kArr[i]]
	// }

	// parsedURL.RawQuery = query.Encode()

	// signature := util.ParamsSign([]byte(s), "GET", parsedURL.Path, a.accountInfo.Token)
	// query.Add("sign", signature)

	// siteDetailURL := StationDetailURL + p.StationID + "?" + s + "&sign=" + signature
	// logger.DetailLogger.Info("SiteDetailURL with params: ", siteDetailURL)

	// headers := map[string]string{
	// 	"content-type":  "application/x-www-form-urlencoded",
	// 	"authorization": a.accountInfo.Token,
	// 	// "referer":       "https://servicewechat.com/wx35849c7f0cf7f7a9/132/page-frame.html",
	// }

	// r, err := common.GetRequest(siteDetailURL, map[string]string{}, 6, request.WithHeader(headers))
	// if err != nil {
	// 	return nil, errors.NewFromErr(err)
	// }

	// code, status := r.Status()
	// if code != http.StatusOK {
	// 	return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	// }

	sdr := StationDetailData{}
	_ = json.Unmarshal([]byte(p.DetailJSON), &sdr)
	// if sdr.ResultCode != "success" {
	// 	return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	// }

	result, err := sdr.convert(a.targetDesc, a.gParam.Province, p)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点详情数据转换失败, 对端返回 %s", err.Error()))
	}

	return result, nil
}
