package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/yunhui/api/common"

	"github.com/riete/errors"
)

const (
	PriceListURL = "https://evcs.yhrongdian.com/v3/evc/mini/policy/list"
)

type StationPriceParams struct {
	OperatorID string `json:"OperatorID"`
	StationID  string `json:"StationID"`
}

type PriceData struct {
	StartTime          string `json:"StartTime"`
	EndTime            string `json:"EndTime"`
	ElecPrice          string `json:"ElecPrice"`
	ServicePrice       string `json:"ServicePrice"`
	TotalPrice         string `json:"TotalPrice"`
	MemberServicePrice string `json:"VipServicePrice"`
	MemberTotalPrice   string `json:"VipPrice"`
}

type StationPriceResp struct {
	Data []PriceData `json:"PolicyInfos"`
}

func (spr StationPriceResp) convert(
	targetDesc,
	stationId,
	stationName,
	latitude,
	longitude,
	province,
	city string,
) ([]map[string]any, error) {
	const hoursPerDay = 24
	r := []map[string]any{}
	if len(spr.Data) != 0 {
		dc, err := handlePriceData(
			spr.Data,
			hoursPerDay,
			targetDesc,
			stationId,
			stationName,
			city,
			province,
			latitude,
			longitude,
		)
		if err != nil {
			return []map[string]any{}, err
		}
		r = append(r, dc...)
	}
	return r, nil
}

func handlePriceData(
	data []PriceData,
	hoursPerDay int,
	targetDesc,
	stationId,
	stationName,
	city,
	province,
	latitude,
	longitude string,
) ([]map[string]any, error) {
	ts := time.Now().Format("********150405")
	rd := ts + commonUtil.RandIntStr(18)
	pd := make([]map[string]any, hoursPerDay)
	for _, price := range data {
		startTime, err := time.Parse(time.TimeOnly, price.StartTime+":00")
		if err != nil {
			return pd, err
		}
		stopTime, _ := time.Parse(time.TimeOnly, price.EndTime+":00")
		// 24:00:00 无法处理，所以错误忽略
		// if err != nil {
		// 	return pd, err
		// }

		start := startTime.Add(30 * time.Minute).Hour()
		end := stopTime.Add(30 * time.Minute).Hour()
		if end == 0 {
			end = hoursPerDay
		}

		for i := start; i < end; i++ {
			pd[i] = map[string]any{
				"id":                 rd,
				"jobId":              rd,
				"stationId":          stationId,
				"stationName":        stationName,
				"city":               city,
				"province":           province,
				"stationLat":         latitude,
				"stationLng":         longitude,
				"accountType":        "普通用户",
				"crawlDay":           time.Now().Format("********"),
				"crawlTime":          time.Now().Format(time.TimeOnly),
				"channel":            targetDesc,
				"operName":           targetDesc,
				"pileType":           "3",
				"timeInterval":       fmt.Sprintf("%02d", i),
				"elecPrice":          price.ElecPrice,
				"servicePrice":       price.ServicePrice,
				"totalPrice":         price.TotalPrice,
				"memberElecPrice":    price.ElecPrice,
				"memberServicePrice": price.MemberServicePrice,
				"memberPrice":        price.MemberTotalPrice,
			}
		}
	}
	return pd, nil
}

func (a Api) GetStationPrice(p Param) ([]map[string]any, errors.Error) {
	originParams := StationPriceParams{
		OperatorID: p.BusinessId,
		StationID:  p.StationID,
	}

	reqJSON, err := json.Marshal(originParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数加密后转换 JSON 失败")
	}

	headers := map[string]string{
		"authorization": a.accountInfo.Token,
		"Content-Type":  "application/json;charset=UTF-8",
	}
	r, err := common.DoRequest(PriceListURL, reqJSON, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	spr := new(StationPriceResp)
	_ = json.Unmarshal(r.Content(), spr)

	result, err := spr.convert(
		a.targetDesc,
		p.StationID,
		p.StationName,
		p.Latitude,
		p.Longitude,
		a.gParam.Province,
		p.City,
	)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("站点价格数据处理失败, %s", err.Error()))
	}

	return result, nil
}
