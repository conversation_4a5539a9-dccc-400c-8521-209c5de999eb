package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/request"
	"tianyan-crawler/internal/source/yunhui/api/common"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://evcs.yhrongdian.com/v3/wx_new_station/info_list_distance/"
)

type StationListParams struct {
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
	IndexType   string  `json:"index_type"`
	PageNum     int     `json:"page_start"`
	PageSize    int     `json:"page_size"`
	StationName string  `json:"StationName"`
	Code        string  `json:"code"`
	UserId      string  `json:"user_id,omitempty"`
}

type StationListData struct {
	StationId      string  `json:"StationID"`
	OperatorID     string  `json:"OperatorID"`
	StationName    string  `json:"StationName"`
	Latitude       float64 `json:"StationLat"`
	Longitude      float64 `json:"StationLng"`
	FastCharge     int     `json:"dc_total"`
	FreeFastCharge int     `json:"dc_free"`
	SlowCharge     int     `json:"ac_total"`
	FreeSlowCharge int     `json:"ac_free"`
}

type StationListResp struct {
	Data      []StationListData `json:"list"`
	PageCount int               `json:"PageCount"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Data {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"businessId":     d.OperatorID,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p Param, pageNum int) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)
	slp := StationListParams{
		Latitude:  lat,
		Longitude: lng,
		IndexType: "distance",
		PageNum:   pageNum,
		PageSize:  10,
		Code:      "",
	}

	reqJSON, err := json.Marshal(slp)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	headers := map[string]string{
		// "authorization": a.accountInfo.Token,
		"Content-Type": "application/json;charset=UTF-8",
	}
	r, err := common.DoRequest(StationListURL, reqJSON, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := new(StationListResp)
	_ = json.Unmarshal(r.Content(), sr)
	return sr.convert(a.targetDesc, p.CITY), nil
}
