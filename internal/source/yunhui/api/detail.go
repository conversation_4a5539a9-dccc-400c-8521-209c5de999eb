package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/yunhui/api/common"

	"github.com/riete/errors"
)

const (
	StationDetailURL = "https://evcs.yhrongdian.com/v3/wx_station/detail"
)

type ConnectorInfo struct {
	ConnectorID        string  `json:"ConnectorID"`
	Power              float64 `json:"Power"`
	VoltageUpperLimits float64 `json:"VoltageUpperLimits"`
	VoltageLowerLimits float64 `json:"VoltageLowerLimits"`
	ConnectorType      float64 `json:"ConnectorType"`
}

type StationDetailResp struct {
	StationId      string  `json:"StationID"`
	StationName    string  `json:"StationName"`
	StationAddr    string  `json:"Address"`
	OperatorName   string  `json:"OperatorName"`
	OperatorID     string  `json:"OperatorID"`
	OperatorTel    string  `json:"StationTel"`
	ServiceTel     string  `json:"ServiceTel"`
	Lon            float64 `json:"StationLng"`
	Lat            float64 `json:"StationLat"`
	BusinessTime   string  `json:"BusineHours"`
	ParkFeeDesc    string  `json:"ParkFee"`
	EquipmentInfos []struct {
		ConnectorInfos []ConnectorInfo `json:"ConnectorInfos"`
	} `json:"EquipmentInfos"`
}

var GUN_TYPE_MAP = map[string]float64{
	"DC": 4.0,
	"AC": 3.0,
}

var OPERATOR_ID_MAP = map[string]string{
	"395815801": "特来电",
	"313744932": "星星充电",
	"101437000": "小桔充电",
	"MA5DT8Q54": "中国南方电网",
	"MA5DLDKY3": "润诚达",
	"MA59G0765": "蔚景云",
	"324887859": "驿联",
	"551433697": "华商三优",
	"MA2274PG1": "速充",
	"MA7D6FPW6": "清辰绿能",
	"MA5DA0053": "车电网",
}

func (sdr *StationDetailResp) convert(channel, province, city string) []map[string]any {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	fastCharge := 0
	slowCharge := 0
	chargingGun := []map[string]any{}
	for _, gun := range sdr.EquipmentInfos {
		for _, gInfo := range gun.ConnectorInfos {
			gunType := ""
			if gInfo.ConnectorType == GUN_TYPE_MAP["DC"] {
				gunType = "直流"
				fastCharge += 1
			} else if gInfo.ConnectorType == GUN_TYPE_MAP["AC"] {
				gunType = "交流"
				slowCharge += 1
			}
			chargingGun = append(chargingGun, map[string]any{
				"channel":           channel,
				"id":                rd,
				"jobId":             rd,
				"stationId":         sdr.StationId,
				"stationName":       sdr.StationName,
				"city":              city,
				"gunId":             gInfo.ConnectorID,
				"gunType":           gunType,
				"power":             gInfo.Power,
				"voltageUpperLimit": gInfo.VoltageUpperLimits,
				"voltageLowerLimit": gInfo.VoltageLowerLimits,
				"runTime":           ct,
			})
		}
	}

	operatorTel := sdr.OperatorTel
	if operatorTel == "" {
		operatorTel = sdr.ServiceTel
	}

	operatorName := OPERATOR_ID_MAP[sdr.OperatorID]
	if operatorName == "" {
		operatorName = channel + "_其他"
	}

	am := []map[string]any{
		{
			"id":           rd,
			"jobId":        rd,
			"channel":      channel,
			"city":         city,
			"province":     province,
			"stationId":    sdr.StationId,
			"stationName":  sdr.StationName,
			"stationAddr":  sdr.StationAddr,
			"operatorId":   sdr.OperatorID,
			"operatorName": operatorName,
			"operatorTel":  operatorTel,
			"fastCharge":   fastCharge,
			"slowCharge":   slowCharge,
			"lon":          sdr.Lon,
			"lat":          sdr.Lat,
			"businessTime": sdr.BusinessTime,
			"parkFeeDesc":  sdr.ParkFeeDesc,
			"chargingGun":  chargingGun,
			"runTime":      ct,
		},
	}
	return am
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	ts := time.Now().UnixMilli()
	query := map[string]string{
		"date":       strconv.FormatInt(ts, 10),
		"OperatorID": p.BusinessId,
		"station_id": p.StationID,
	}

	headers := map[string]string{
		"authorization": a.accountInfo.Token,
		"Content-Type":  "application/json;charset=utf-8",
	}
	r, err := common.GetRequest(StationDetailURL, query, 6, request.WithHeader(headers))
	if err != nil {
		return []map[string]any{}, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := new(StationDetailResp)
	_ = json.Unmarshal(r.Content(), sdr)
	return sdr.convert(a.targetDesc, a.gParam.Province, p.City), nil
}
