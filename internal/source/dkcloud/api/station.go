package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/internal/source/dkcloud/api/common"
	"tianyan-crawler/internal/source/dkcloud/api/util"
)

const (
	StationListURL = "https://mpcs.dazzlesky.com/services/v4/wechat/zhNewStationInfoPageList"
)

type StationListData struct {
	StationId      string `json:"stationId"`
	CzbStationId   string `json:"czbStationId"`
	StationName    string `json:"czbStationName"`
	Latitude       string `json:"stationLat"`
	Longitude      string `json:"stationLng"`
	FastCharge     int    `json:"directCount"`
	FreeFastCharge int    `json:"directLeftCount"`
	SlowCharge     int    `json:"alternateCount"`
	FreeSlowCharge int    `json:"alternateLeftCount"`
}

type StationListResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Result  struct {
		ChargeStationInfoList []StationListData `json:"chargeStationInfoList"`
	} `json:"result"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Result.ChargeStationInfoList {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      d.StationId,
			"stationName":    d.StationName,
			"businessId":     d.CzbStationId,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"fastCharge":     d.FastCharge,
			"freeFastCharge": d.FreeFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p Param) ([]map[string]any, errors.Error) {
	ts := int(time.Now().UnixMicro() / 1e3)
	body := map[string]string{
		"tagIds":       "",
		"userLatStr":   p.Lat,
		"userLngStr":   p.Lgn,
		"pageSize":     "100",
		"pageIndex":    "1",
		"token":        "",
		"platformType": "3",
		"app_key":      AppKey,
		"timestamp":    strconv.Itoa(ts),
	}

	body["sign"] = util.Sign(body, AppSecret)

	r, err := common.DoRequest(StationListURL, body, 6)
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := StationListResp{}
	_ = json.Unmarshal(r.Content(), &sr)
	if sr.Code != 200 {
		return []map[string]any{}, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.targetDesc, p.CITY), nil
}
