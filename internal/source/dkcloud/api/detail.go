package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/riete/errors"

	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/dkcloud/api/common"
	"tianyan-crawler/internal/source/dkcloud/api/util"
)

const (
	StationDetailURL = "https://mpcs.dazzlesky.com/services/v4/wechat/zhStationInfoDetail"
)

type StationDetailData struct {
	// Id       string
	// JobId    string
	// Channel  string `json:"channel"`
	// City     string `json:"city"`
	// Province string `json:"province"`
	StationId    string  `json:"czbStationId"`
	StationName  string  `json:"czbStationName"` // 站点名称
	StationAddr  string  `json:"address"`
	OperatorId   string  `json:"czbOperatorId"`   // 运营商手机号码
	OperatorName string  `json:"czbOperatorName"` // 运营商名称
	FastCharge   int     `json:"directCount"`
	SlowCharge   int     `json:"alternateCount"`
	Lon          float32 `json:"stationLng"`
	Lat          float32 `json:"stationLat"`
	BusinessTime string  `json:"busineHours"` // 营运时间
	ParkFeeDesc  string  `json:"parkFee"`
}

type StationDetailResp struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Result  StationDetailData `json:"result"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) map[string]any {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	am := map[string]any{
		"id":           rd,
		"jobId":        rd,
		"channel":      channel,
		"city":         city,
		"province":     province,
		"stationId":    sdr.Result.StationId,
		"stationName":  sdr.Result.StationName,
		"operatorId":   sdr.Result.OperatorId,
		"operatorName": sdr.Result.OperatorName,
		"fastCharge":   sdr.Result.FastCharge,
		"slowCharge":   sdr.Result.SlowCharge,
		"lon":          sdr.Result.Lon,
		"lat":          sdr.Result.Lat,
		"businessTime": sdr.Result.BusinessTime,
		"parkFeeDesc":  sdr.Result.ParkFeeDesc,
		"stationAddr":  sdr.Result.StationAddr,
		"runTime":      ct,
	}
	return am
}

func (a Api) GetStationDetail(p Param) (map[string]any, errors.Error) {
	ts := int(time.Now().UnixMicro() / 1e3)
	body := map[string]string{
		"userLatStr":   p.Latitude,
		"userLngStr":   p.Longitude,
		"stationId":    p.StationID,
		"token":        "",
		"platformType": "3",
		"app_key":      AppKey,
		"timestamp":    strconv.Itoa(ts),
	}

	body["sign"] = util.Sign(body, AppSecret)

	r, resErr := common.DoRequest(StationDetailURL, body, 6)
	if resErr != nil {
		return nil, errors.NewFromErr(resErr)
	}

	if code, status := r.Status(); code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := StationDetailResp{}
	_ = json.Unmarshal(r.Content(), &sdr)
	if sdr.Code != 200 {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sdr.convert(a.targetDesc, a.gParam.Province, p.City), nil
}
