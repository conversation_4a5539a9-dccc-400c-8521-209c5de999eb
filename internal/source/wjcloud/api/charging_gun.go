package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/wjcloud/api/common"

	"github.com/riete/errors"
)

const (
	ChargingGunURL = "https://miniapp.evking.cn/ChargeDevice/list?atoken=wx86a32937faf25ebd&utoken=xxxxxxxxxxxxxxxx&appplat=11&appver=138"
)

type ChargingGunParams struct {
	StationID string `json:"stationId"`
	PageSize  int    `json:"pageSize"`
	PageNo    int    `json:"pageNo"`
	// ChargeSpeed  interface{} `json:"chargeSpeed"`
	// IsFreeTime   interface{} `json:"isFreeTime"`
	// DeviceName   interface{} `json:"deviceName"`
	// AddressLat   interface{} `json:"addressLat"`
	// AddressLng   interface{} `json:"addressLng"`
}

type ChargingGunData struct {
	GunType        string `json:"chargeTypeName"`
	Power          string `json:"power"`
	InterfaceInfos []struct {
		ScanCode string `json:"scanCode"`
	} `json:"interfaceInfos"`
}

type ChargingGunResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Items      []ChargingGunData `json:"items"`
		NextPage   int               `json:"nextPage"`
		PageNo     int               `json:"pageNo"`
		PageSize   int               `json:"pageSize"`
		PrePage    int               `json:"prePage"`
		TotalCount int               `json:"totalCount"`
		TotalPages int               `json:"totalPages"`
		Virtual    bool              `json:"virtual"`
	} `json:"data"`
}

func (cgr *ChargingGunResp) convert(targetDesc, stationId, stationName, city string) ([]map[string]any, error) {
	r := []map[string]any{}

	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)
	for _, gun := range cgr.Data.Items {
		for _, gInfo := range gun.InterfaceInfos {
			r = append(r, map[string]any{
				"channel":     targetDesc,
				"id":          rd,
				"jobId":       rd,
				"stationId":   stationId,
				"stationName": stationName,
				"city":        city,
				"gunId":       gInfo.ScanCode,
				"gunType":     gun.GunType,
				"power":       gun.Power,
				"runTime":     ct,
			})
		}
	}

	return r, nil
}

func (a Api) GetChargingGunList(p Param) ([]map[string]any, errors.Error) {
	originParams := ChargingGunParams{
		StationID: p.StationID,
		PageNo:    1,
		PageSize:  1000,
	}

	reqJSON, err := json.Marshal(originParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"ts":       strconv.FormatInt(ts, 10),
		"utoken":   "xxxxxxxxxxxxxxxx",
		"atoken":   "wx86a32937faf25ebd",
		"aliScene": "[object Null]",
		"wxScene":  "1089",
		"appid":    "wx86a32937faf25ebd",
	}
	r, err := common.DoRequest(ChargingGunURL, reqJSON, 6, request.WithHeader(headers))

	if err != nil {
		return nil, errors.NewFromErr(err)
	}
	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点价格数据失败, http响应: %s", status))
	}

	cgr := ChargingGunResp{}
	_ = json.Unmarshal(r.Content(), &cgr)
	if cgr.Code != 1 {
		return []map[string]any{}, errors.New(fmt.Sprintf("获取站点充电枪数据失败, 对端返回 %s", r.ContentToString()))
	}

	result, err := cgr.convert(a.targetDesc, p.StationID, p.StationName, p.City)
	if err != nil {
		return []map[string]any{}, errors.New(fmt.Sprintf("站点充电枪数据处理失败, %s", err.Error()))
	}

	return result, nil
}
