package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"tianyan-crawler/internal/common/request"
	"tianyan-crawler/internal/source/wjcloud/api/common"

	"github.com/riete/errors"
)

const (
	StationListURL = "https://miniapp.evking.cn/chargeStation/map?atoken=wx86a32937faf25ebd&utoken=xxxxxxxxxxxxxxxx&appplat=11&appver=138"
)

type StationListParams struct {
	AddressLat         float64 `json:"addressLat"`
	AddressLng         float64 `json:"addressLng"`
	MapZoom            int     `json:"mapZoom"`
	Content            any     `json:"content"`
	MaxDistance        any     `json:"maxDistance"`
	CityId             any     `json:"cityId"`
	ChargeStationTypes any     `json:"chargeStationTypes"`
	CampaignId         int     `json:"campaignId"`
}

type StationListData struct {
	StationId           int64   `json:"stationId"`
	PriceShowDeviceId   int64   `json:"priceShowDeviceId"`
	StationName         string  `json:"stationName"`
	Latitude            float64 `json:"addressLat"`
	Longitude           float64 `json:"addressLng"`
	FastCharge          int     `json:"interfaceFastCount"`
	FreeFastCharge      int     `json:"interfaceFastFreeCount"`
	SuperFastCharge     int     `json:"interfaceSuperFastCount"`
	FreeSuperFastCharge int     `json:"interfaceSuperFastFreeCount"`
	SlowCharge          int     `json:"interfaceSlowCount"`
	FreeSlowCharge      int     `json:"interfaceSlowFreeCount"`
}

type StationListResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Items      []StationListData `json:"items"`
		NextPage   int               `json:"nextPage"`
		PageNo     int               `json:"pageNo"`
		PageSize   int               `json:"pageSize"`
		PrePage    int               `json:"prePage"`
		TotalCount int               `json:"totalCount"`
		TotalPages int               `json:"totalPages"`
		Virtual    bool              `json:"virtual"`
	} `json:"data"`
}

func (slr *StationListResp) convert(channel, city string) []map[string]any {
	ac := []map[string]any{}
	ct := time.Now().Format(time.DateTime)
	date := time.Now().Format(time.DateOnly)
	timeInterval := time.Now().Format("15")
	for _, d := range slr.Data.Items {
		ac = append(ac, map[string]any{
			"channel":        channel,
			"city":           city,
			"stationId":      strconv.FormatInt(d.StationId, 10),
			"stationName":    d.StationName,
			"businessId":     d.PriceShowDeviceId,
			"lat":            d.Latitude,
			"lon":            d.Longitude,
			"fastCharge":     d.FastCharge + d.SuperFastCharge,
			"freeFastCharge": d.FreeFastCharge + d.FreeSuperFastCharge,
			"slowCharge":     d.SlowCharge,
			"freeSlowCharge": d.FreeSlowCharge,
			"date":           date,
			"timeInterval":   timeInterval,
			"runTime":        ct,
		})
	}
	return ac
}

func (a Api) GetStationList(p Param) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Lat, 64)
	lng, _ := strconv.ParseFloat(p.Lgn, 64)

	slp := StationListParams{
		AddressLat:  lat,
		AddressLng:  lng,
		MapZoom:     9,
		CampaignId:  0,
		MaxDistance: 50000,
	}

	reqJSON, err := json.Marshal(slp)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"ts":       strconv.FormatInt(ts, 10),
		"utoken":   "xxxxxxxxxxxxxxxx",
		"atoken":   "wx86a32937faf25ebd",
		"aliScene": "[object Null]",
		"wxScene":  "1089",
		"appid":    "wx86a32937faf25ebd",
	}
	r, err := common.DoRequest(StationListURL, reqJSON, 6, request.WithHeader(headers))

	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点列表数据失败, http响应: %s", status))
	}

	sr := new(StationListResp)
	_ = json.Unmarshal(r.Content(), sr)
	if sr.Code != 1 {
		return nil, errors.New(fmt.Sprintf("获取站点列表数据失败, 对端返回 %s", r.ContentToString()))
	}

	return sr.convert(a.targetDesc, p.CITY), nil
}
