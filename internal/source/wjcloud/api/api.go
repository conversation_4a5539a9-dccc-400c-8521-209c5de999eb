package api

import (
	"encoding/json"
	"tianyan-crawler/internal/app/task"
	"tianyan-crawler/internal/common/util"

	"github.com/riete/errors"
)

type Api struct {
	targetDesc  string
	accountInfo AccountInfo
	gParam      GlobalParam
}

type AccountInfo struct {
	Token string `json:"token"`
}

type GlobalParam struct {
	TaskId     string `json:"taskId"`
	TemplateId string `json:"templateId"`
	Channel    string `json:"channel"`
	ScriptUrl  string `json:"scriptUrl"`
	BizType    string `json:"bizType"`
	Province   string `json:"province"`
	City       string `json:"city"`
}

func (gp GlobalParam) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(gp)
}

type Param struct {
	StationID   string `json:"station_id,omitempty"`
	BusinessId  string `json:"business_id,omitempty"`
	StationName string `json:"station_name,omitempty"`
	CITY        string `json:"CITY,omitempty"`
	City        string `json:"city,omitempty"`
	CityCode    string `json:"CITY_CODE,omitempty"`
	Lat         string `json:"LAT,omitempty"`
	Lgn         string `json:"LGN,omitempty"`
	Latitude    string `json:"lat,omitempty"`
	Longitude   string `json:"lon,omitempty"`
}

func (p Param) ToAnyMap() map[string]any {
	return util.ToAnyMapWithJSONTag(p)
}

type TaskMessage struct {
	TaskInstanceId string      `json:"taskInstanceId"`
	Target         string      `json:"target"`
	AccountInfo    AccountInfo `json:"accountInfo"`
	GlobalParam    GlobalParam `json:"globalParam"`
	Params         []Param     `json:"params"`
}

func (msg *TaskMessage) Marshal(stmsg task.StartTaskMessage) error {
	s, err := json.Marshal(stmsg)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(s, msg); err != nil {
		return err
	}
	return nil
}

type WJCloudApi interface {
	GetStationList(p Param) ([]map[string]any, errors.Error)
	GetStationDetail(p Param) ([]map[string]any, errors.Error)
	GetStationPrice(p Param) ([]map[string]any, errors.Error)
	GetChargingGunList(p Param) ([]map[string]any, errors.Error)
}

func New(targetDesc string, tm TaskMessage) Api {
	return Api{
		targetDesc:  targetDesc,
		accountInfo: tm.AccountInfo,
		gParam:      tm.GlobalParam,
	}
}
