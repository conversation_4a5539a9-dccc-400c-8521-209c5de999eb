package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"tianyan-crawler/internal/common/request"
	commonUtil "tianyan-crawler/internal/common/util"
	"tianyan-crawler/internal/source/wjcloud/api/common"

	"github.com/riete/errors"
)

const (
	StationDetailURL = "https://miniapp.evking.cn/chargeStation/detail?atoken=wx86a32937faf25ebd&utoken=xxxxxxxxxxxxxxxx&appplat=11&appver=138"
)

type StationDetailParams struct {
	StationID string  `json:"stationId"`
	Lat       float64 `json:"lat"`
	Lng       float64 `json:"lng"`
}

type StationDetailData struct {
	StationId               int      `json:"stationId"`
	StationName             string   `json:"stationName"`
	StationAddr             string   `json:"address"`
	OperatorName            string   `json:"orgName"`
	OperatorType            int      `json:"operateType"`
	OperatorTel             string   `json:"orgPhone"`
	InterfaceFastCount      int      `json:"interfaceFastCount"`
	InterfaceSuperFastCount int      `json:"interfaceSuperFastCount"`
	InterfaceSlowCount      int      `json:"interfaceSlowCount"`
	Lon                     float64  `json:"addressLng"`
	Lat                     float64  `json:"addressLat"`
	OpenTimeList            []string `json:"openTimeList"`
	ParkFeeDesc             string   `json:"parkingContent"`
	CategoryTypeName        string   `json:"categoryTypeName"`
	LocateTagInfos          []struct {
		Tag string `json:"tag"`
	} `json:"locateTagInfos"`
	NearbyTagInfos []struct {
		Tag string `json:"tag"`
	} `json:"nearbyTagInfos"`
}

type StationDetailResp struct {
	Code int               `json:"code"`
	Msg  string            `json:"msg"`
	Data StationDetailData `json:"data"`
}

func (sdr *StationDetailResp) convert(channel, province, city string) []map[string]any {
	ct := time.Now().Format(time.DateTime)
	ts := time.Now().Format("20060102150405")
	rd := ts + commonUtil.RandIntStr(18)

	operatorType := ""
	if sdr.Data.OperatorType == 1 {
		operatorType = "自营"
	} else if sdr.Data.OperatorType == 3 {
		operatorType = "他营"
	}

	facilities := []string{}
	for _, info := range sdr.Data.LocateTagInfos {
		if info.Tag != "" {
			facilities = append(facilities, info.Tag)
		}
	}
	for _, info := range sdr.Data.NearbyTagInfos {
		if info.Tag != "" {
			facilities = append(facilities, info.Tag)
		}
	}

	am := []map[string]any{
		{
			"id":                   rd,
			"jobId":                rd,
			"channel":              channel,
			"city":                 city,
			"province":             province,
			"stationId":            sdr.Data.StationId,
			"stationName":          sdr.Data.StationName,
			"stationAddr":          sdr.Data.StationAddr,
			"operatorName":         sdr.Data.OperatorName,
			"siteOperator":         sdr.Data.OperatorName,
			"operatorType":         operatorType,
			"operatorTel":          sdr.Data.OperatorTel,
			"fastCharge":           sdr.Data.InterfaceFastCount + sdr.Data.InterfaceSuperFastCount,
			"slowCharge":           sdr.Data.InterfaceSlowCount,
			"lon":                  sdr.Data.Lon,
			"lat":                  sdr.Data.Lat,
			"businessTime":         sdr.Data.OpenTimeList[0],
			"parkFeeDesc":          sdr.Data.ParkFeeDesc,
			"supportingFacilities": strings.Join(facilities, ","),
			"runTime":              ct,
		},
	}
	return am
}

func (a Api) GetStationDetail(p Param) ([]map[string]any, errors.Error) {
	lat, _ := strconv.ParseFloat(p.Latitude, 64)
	lng, _ := strconv.ParseFloat(p.Longitude, 64)

	originParams := StationDetailParams{
		StationID: p.StationID,
		Lat:       lat,
		Lng:       lng,
	}

	reqJSON, err := json.Marshal(originParams)
	if err != nil {
		return nil, errors.NewFromErr(err).Trace("参数转换 JSON 失败")
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"ts":       strconv.FormatInt(ts, 10),
		"utoken":   "xxxxxxxxxxxxxxxx",
		"atoken":   "wx86a32937faf25ebd",
		"aliScene": "[object Null]",
		"wxScene":  "1089",
		"appid":    "wx86a32937faf25ebd",
	}
	r, err := common.DoRequest(StationDetailURL, reqJSON, 6, request.WithHeader(headers))
	if err != nil {
		return nil, errors.NewFromErr(err)
	}

	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status).Trace(fmt.Sprintf("获取站点详情数据失败, http响应: %s", status))
	}

	sdr := new(StationDetailResp)
	_ = json.Unmarshal(r.Content(), sdr)
	if sdr.Code != 1 {
		return nil, errors.New(fmt.Sprintf("获取站点详情数据失败, 对端返回: %s", r.ContentToString()))
	}

	return sdr.convert(a.targetDesc, a.gParam.Province, p.City), nil
}
