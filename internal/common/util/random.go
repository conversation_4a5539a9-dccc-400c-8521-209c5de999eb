package util

import (
	"math"
	"math/rand"
	"strconv"
	"time"
)

func RandStr(length int) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano() + rand.Int63n(100)))
	str := "0123456789abcdefghijklmnopqrstuvwxyz"
	bytes := []byte(str)
	result := []byte{}
	for i := 0; i < length; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

// RandIntStr returns the random int string with digit length,
// for 1 < digital <= 18. The result will be in the range of [0, 10^18 - 1].
// for digit length.
func RandIntStr(digit int) string {
	r := rand.New(rand.NewSource(time.Now().Unix()))
	return strconv.FormatInt(r.Int63n(int64(math.Pow10(digit))), 10)
}
