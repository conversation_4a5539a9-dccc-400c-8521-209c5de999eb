package util

import (
	"reflect"
	"slices"
	"strings"
)

func ToAnyMap[T any](o T) map[string]any {
	am := make(map[string]any)
	v := reflect.ValueOf(o)
	t := reflect.TypeOf(o)
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldName := t.Field(i).Name
		am[fieldName] = field.Interface()
	}
	return am
}

func ToAnyMapWithJSONTag[T any](o T) map[string]any {
	am := map[string]any{}
	v := reflect.ValueOf(o)
	t := reflect.TypeOf(o)
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		jsonTags := t.Field(i).Tag.Get("json")
		if jsonTags == "" {
			continue
		}
		tagSlice := strings.Split(jsonTags, ",")
		isOmitEmpty := slices.Contains(tagSlice, "omitempty")
		if isOmitEmpty && field.Interface() == "" {
			continue
		}
		am[tagSlice[0]] = field.Interface()
	}
	return am
}
