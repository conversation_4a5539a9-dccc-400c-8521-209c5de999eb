package request

import (
	"net/http"
	"net/url"
	"time"
)

type Option func(*Request)

func WithTimeout(t time.Duration) Option {
	return func(r *Request) {
		r.SetTimeout(t)
	}
}

func WithHeader(headers ...map[string]string) Option {
	return func(r *Request) {
		for _, header := range headers {
			r.<PERSON>(header)
		}
	}
}

func WithProxy(proxy Proxy) Option {
	return func(r *Request) {
		r.SetProxy(proxy)
	}
}

func WithProxyFunc(f func(*http.Request) (*url.URL, error)) Option {
	return func(r *Request) {
		r.client.Transport.(*http.Transport).Proxy = f
	}
}

func WithUnsetProxy() Option {
	return func(r *Request) {
		r.UnsetProxy()
	}
}

func WithSkipTLSVerify() Option {
	return func(r *Request) {
		r.SkipTL<PERSON>erify()
	}
}

func WithBasicAuth(username, password string) Option {
	return func(r *Request) {
		r.SetBasic<PERSON>uth(username, password)
	}
}

func WithBearerTokenAuth(token string) Option {
	return func(r *Request) {
		r.<PERSON><PERSON>earer<PERSON>oken<PERSON>uth(token)
	}
}

func WithTransport(tr http.RoundTripper) Option {
	return func(r *Request) {
		r.SetTransport(tr)
	}
}

func WithDefaultTransport() Option {
	return func(r *Request) {
		r.SetTransport(NewDefaultTransport())
	}
}

func WithClient(client *http.Client) Option {
	return func(r *Request) {
		r.SetClient(client)
	}
}

func WithDefaultClient() Option {
	return func(r *Request) {
		r.SetClient(NewDefaultClient())
	}
}
