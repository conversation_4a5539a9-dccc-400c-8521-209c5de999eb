package logger

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/common/request"

	"github.com/riete/errors"
	"github.com/riete/logger"
)

var (
	ErrorLogger  = logger.NewLogger("error.log", logger.WithMaxSizePerFile(1024*logger.SizeMiB))
	KafkaLogger  = logger.NewLogger("kafka.log", logger.WithMaxSizePerFile(1024*logger.SizeMiB))
	TaskLogger   = logger.NewLogger("task.log", logger.WithMaxSizePerFile(1024*logger.SizeMiB))
	DetailLogger = logger.NewLogger("detail.log", logger.WithMaxSizePerFile(1024*logger.SizeMiB))
	PanicLogger  = logger.NewLogger("panic.log", logger.WithMaxSizePerFile(1024*logger.SizeMiB))
)

func Info(v ...any) {
	if config.Config.Mode == "DEBUG" {
		log.Println(v...)
	}
	TaskLogger.Info(v...)
}

func Error(err errors.Error) {
	if config.Config.Mode == "DEBUG" {
		log.Println(err.Stack())
	}
	ErrorLogger.Log(err.Stack())
}

func Panic(panicMsg string) {
	if config.Config.Mode == "DEBUG" {
		log.Println("PANIC:", panicMsg)
	}
	PanicLogger.Error(panicMsg)
}

const PostErrorLogURL = "/fastdata-platform-admin/crawl/task/client/uploadErrorLog"

type MessageMap struct {
	Type string
	Desc string
}

type LogMessageMap struct {
	DEBUG MessageMap
	INFO  MessageMap
	WARN  MessageMap
	ERROR MessageMap
}

var ERROR_LOG_MESSAGE_MAP = LogMessageMap{
	DEBUG: MessageMap{Type: "DEBUG", Desc: "调试"},
	INFO:  MessageMap{Type: "INFO", Desc: "信息"},
	WARN:  MessageMap{Type: "WARN", Desc: "警告"},
	ERROR: MessageMap{Type: "ERROR", Desc: "错误"},
}

type ErrorLogMessage struct {
	ErrorTime   string `json:"errorTime"`
	Level       string `json:"level,omitempty"`
	BizType     string `json:"bizType,omitempty"`
	Channel     string `json:"channel,omitempty"`
	StackTrace  string `json:"stackTrace,omitempty"`
	RequestNo   string `json:"requestNo,omitempty"`
	RequestData any    `json:"requestData,omitempty"`
	ErrorMsg    string `json:"errorMsg,omitempty"`
}

func PostErrorLog(errorMsg ErrorLogMessage) error {
	options := []request.Option{
		request.WithDefaultClient(),
		request.WithTimeout(10 * time.Second),
		request.WithHeader(map[string]string{
			"c-random": "HM",
		}),
	}
	rClient := request.NewRequest(options...)

	et := time.Now().Format(time.DateTime)
	errorMsg.ErrorTime = et
	if errorMsg.Level == "" {
		errorMsg.Level = "ERROR"
	}

	jsonBody, err := json.Marshal(errorMsg)
	if err != nil {
		Error(errors.New(err.Error()).Trace(fmt.Sprintf("上传错误日志数据序列化失败: %s", errorMsg)))
		return err
	}

	err = rClient.Post(config.Config.FastDataHost+PostErrorLogURL, jsonBody)
	if err != nil {
		Error(errors.New(fmt.Sprintf("上传错误日志失败: %s", errorMsg)).Trace(err.Error()))
		return err
	}

	Info(fmt.Sprintf("上传错误日志成功: %s", jsonBody))
	return nil
}
