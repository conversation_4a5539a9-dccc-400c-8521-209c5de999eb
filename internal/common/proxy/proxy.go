package proxy

import (
	"container/list"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/riete/errors"

	"tianyan-crawler/config"
	"tianyan-crawler/internal/common/logger"
	"tianyan-crawler/internal/common/request"
)

// 青果网络代理, www.qg.net

const (
	proxyApiUrl           = "https://share.proxy.qg.net/pool"
	proxyApiSuccessCode   = "SUCCESS"
	proxyApiRetryInterval = time.Second
	proxyValidateUrl      = "https://d.qg.net/ip"
)

// 动态代理, https://www.qg.net/doc/1846.html

type ProxyIp struct {
	ProxyIp    string `json:"proxy_ip"`
	Server     string `json:"server"`
	Area       string `json:"area"`
	Isp        string `json:"isp"`
	ExpireTime string `json:"deadline"`
	expiredAt  time.Time
}

func (p *ProxyIp) setExpiredAt() {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	t, _ := time.ParseInLocation(time.DateTime, p.ExpireTime, loc)
	p.expiredAt = t
}

func (p ProxyIp) AsProxyEnv() request.Proxy {
	return request.NewHttpProxy(p.Server)
}

func (p ProxyIp) ProxyUrl() *url.URL {
	u, _ := url.Parse("http://" + p.Server)
	return u
}

func (p ProxyIp) IsExpired() bool {
	return time.Now().Add(10 * time.Second).After(p.expiredAt)
}

func (p ProxyIp) IsValid() bool {
	r := request.NewRequest(
		request.WithDefaultClient(),
		request.WithTimeout(10*time.Second),
		request.WithProxyFunc(http.ProxyURL(p.ProxyUrl())),
	)
	if err := r.Get(proxyValidateUrl, nil); err != nil {
		return false
	}
	code, _ := r.Status()
	if code == http.StatusOK {
		return strings.Contains(r.ContentToString(), p.ProxyIp)
	}
	return false
}

type ProxyApiResp struct {
	Code      string    `json:"code"`
	RequestId string    `json:"request_id"`
	Message   string    `json:"message"`
	Data      []ProxyIp `json:"data"`
}

type ProxyIpPool struct {
	pool  *list.List
	mutex sync.Mutex
}

func (p *ProxyIpPool) put() (*ProxyApiResp, errors.Error) {
	r := request.NewRequest(
		request.WithDefaultClient(),
		request.WithTimeout(5*time.Second),
	)
	if err := r.Get(
		proxyApiUrl,
		map[string]string{
			"key":      config.Config.Proxy.Key,
			"num":      config.Config.Proxy.IpMax,
			"area":     "",
			"isp":      "1",
			"fmt":      "json",
			"seq":      "",
			"distinct": "true",
			"pool":     "1",
		},
	); err != nil {
		return nil, errors.NewFromErr(err)
	}
	logger.Info("调用青果获取代理API返回", r.ContentToString())
	code, status := r.Status()
	if code != http.StatusOK {
		return nil, errors.New(status)
	}
	resp := new(ProxyApiResp)
	if err := json.Unmarshal(r.Content(), resp); err != nil {
		return nil, errors.NewFromErr(err).Trace("parse proxy api resp error")
	}
	return resp, nil
}

func (p *ProxyIpPool) Put() {
	var resp *ProxyApiResp
	var err errors.Error
	for {
		resp, err = p.put()
		if err != nil {
			logger.Error(err.Trace("获取代理ip失败, 5秒后重试"))
			time.Sleep(proxyApiRetryInterval)
			continue
		}
		if resp.Code != proxyApiSuccessCode {
			logger.Error(errors.New("获取代理ip失败: " + resp.Message + ", 5秒后重试"))
			time.Sleep(proxyApiRetryInterval)
			continue
		}
		if len(resp.Data) == 0 {
			logger.Error(errors.New("获取代理ip失败: 返回ip为空, 5秒后重试"))
			time.Sleep(proxyApiRetryInterval)
			continue
		}
		wg := &sync.WaitGroup{}
		wg.Add(len(resp.Data))
		for _, r := range resp.Data {
			go func(r ProxyIp) {
				defer wg.Done()
				if r.IsValid() {
					r.setExpiredAt()
					p.pool.PushFront(r)
					logger.Info("代理ip访问测试成功: " + r.Server)
				} else {
					logger.Info("代理ip访问测试失败: " + r.Server)
				}
			}(r)
		}
		wg.Wait()
		return
	}
}

func (p *ProxyIpPool) Get() ProxyIp {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	for {
		if p.pool.Len() == 0 {
			logger.Info("代理地址池为空, 重新获取代理ip")
			time.Sleep(4 * time.Second)
			p.Put()
		}
		if p.pool.Len() == 0 {
			logger.Info("代理地址池为空")
			continue
		}
		item := p.pool.Front()
		proxyIp, ok := item.Value.(ProxyIp)
		if !ok {
			logger.Info("IP 池中元素类型错误 [type]: ", reflect.TypeOf(item), " [item]: ", item)
			// p.pool.Remove(item)
			p.pool = list.New()
			continue
		}
		if !proxyIp.IsExpired() {
			p.pool.MoveToBack(item)
			return proxyIp
		} else {
			logger.Info("代理ip将失效, 更换新代理ip")
			p.pool.Remove(item)
			logger.Info(fmt.Sprintf("删除将失效ip, 地址池ip数量: %d", p.pool.Len()))
			continue
		}
	}
}

func NewQGIpPoolProxy() *ProxyIpPool {
	return &ProxyIpPool{pool: list.New()}
}

var defaultProxyIpPool = NewQGIpPoolProxy()

func GetIp() ProxyIp {
	return defaultProxyIpPool.Get()
}
